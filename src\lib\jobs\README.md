# Modular Job Runner System

This directory contains the refactored, modular job runner system that replaces the monolithic job processing logic in `jobRunner.ts`. The new architecture provides better separation of concerns, improved maintainability, and easier extensibility.

## Architecture Overview

### Core Components

1. **Types & Interfaces** (`types.ts`)
   - `JobHandler` - Base interface for all job handlers
   - `JobResult` - Standardized result format
   - `JobExecutionContext` - Execution context with utilities
   - `JobHandlerFactory` - Factory interface for creating handlers

2. **Base Classes** (`base/BaseJobHandler.ts`)
   - Abstract base class providing common functionality
   - Helper methods for error handling, progress tracking, and validation
   - Standardized logging and cancellation support

3. **Execution Context** (`context/JobExecutionContext.ts`)
   - Provides shared utilities for job execution
   - Handles logging, cancellation checking, and progress tracking
   - Supports retry logic and timing operations

4. **Job Handler Factory** (`factory/JobHandlerFactory.ts`)
   - Manages registration and instantiation of job handlers
   - Provides validation and debugging capabilities
   - Singleton pattern for global access

### Job Handlers

Each job type has its own dedicated handler:

- **Oracle** (`oracle/OracleJobHandler.ts`) - Oracle database operations
- **MySQL** (`mysql/MySQLJobHandler.ts`) - MySQL database operations  
- **Database Admin** (`database-admin/DatabaseAdminJobHandler.ts`) - Complex database administration
- **SFTP** (`sftp/SftpJobHandler.ts`) - SFTP file operations
- **PDF DIPA** (`pdf-dipa/PdfDipaJobHandler.ts`) - PDF extraction and processing
- **ADK Processing** (`adk-processing/AdkProcessingJobHandler.ts`) - Archive processing and XML extraction

## Usage

### Basic Usage

```typescript
import { jobHandlerFactory, JobExecutionContext } from './jobs';

// Get a handler for a specific job type
const handler = jobHandlerFactory.getHandler('oracle');

// Create execution context
const context = new JobExecutionContext(jobId, executionId, jobDefinition);

// Execute the job
const result = await handler.execute(context);
```

### Adding a New Job Type

1. **Create the Handler Class**

```typescript
// src/lib/jobs/my-new-job/MyNewJobHandler.ts
import { BaseJobHandler } from "../base/BaseJobHandler";
import type { JobResult, JobExecutionContext } from "../types";

export class MyNewJobHandler extends BaseJobHandler {
  public readonly jobType = "my_new_job";

  public async execute(context: JobExecutionContext): Promise<JobResult> {
    // Your job logic here
    await context.addLog("Starting my new job...");
    
    // Process data
    const data = await this.processData(context);
    
    return this.createJobResult(data.length, data);
  }

  public validateConfig(jobDef: JobDefinition): boolean {
    // Validate job configuration
    return super.validateConfig(jobDef) && jobDef.dataSource.my_new_job != null;
  }

  private async processData(context: JobExecutionContext): Promise<any[]> {
    // Implementation details
    return [];
  }
}
```

2. **Register the Handler**

```typescript
// In src/lib/jobs/index.ts
import { MyNewJobHandler } from "./my-new-job/MyNewJobHandler";

export function registerAllJobHandlers(): void {
  // ... existing registrations
  jobHandlerFactory.registerHandler("my_new_job", MyNewJobHandler);
}
```

3. **Export the Handler**

```typescript
// In src/lib/jobs/index.ts
export { MyNewJobHandler } from "./my-new-job/MyNewJobHandler";
```

## Key Features

### 1. **Backward Compatibility**
- The main `jobRunner.ts` uses the new modular system but falls back to legacy code if needed
- Existing job configurations continue to work without changes
- No breaking changes to external APIs

### 2. **Enhanced Error Handling**
- Standardized error handling across all job types
- Detailed error logging with context information
- Graceful fallback mechanisms

### 3. **Progress Tracking**
- Built-in progress tracking for long-running operations
- Cancellation support at multiple checkpoints
- Timing and performance metrics

### 4. **Validation & Testing**
- Configuration validation for each job type
- Integration testing utilities
- Handler validation on startup

### 5. **Extensibility**
- Easy to add new job types without modifying core logic
- Plugin-like architecture for job handlers
- Consistent interfaces across all handlers

## Testing

Run the integration test to verify the system:

```typescript
import { testJobSystemIntegration } from './jobs/test-integration';

const result = await testJobSystemIntegration();
console.log('Integration test result:', result);
```

## Migration Guide

### For Developers

The modular system is automatically used by the main job runner. No changes are required for existing job definitions or API usage.

### For New Features

When adding new job types:
1. Create a new handler class extending `BaseJobHandler`
2. Implement the required methods (`execute`, `validateConfig`)
3. Register the handler in the factory
4. Add appropriate TypeScript types to `jobManager.ts`

## Benefits

1. **Maintainability** - Each job type is in its own file with focused responsibilities
2. **Testability** - Individual handlers can be tested in isolation
3. **Scalability** - Easy to add new job types without affecting existing code
4. **Consistency** - Standardized interfaces and error handling across all job types
5. **Performance** - Better resource management and cancellation support

## File Structure

```
src/lib/jobs/
├── README.md                           # This file
├── index.ts                           # Main exports and registration
├── types.ts                           # Core interfaces and types
├── test-integration.ts                # Integration testing utilities
├── base/
│   └── BaseJobHandler.ts              # Abstract base class
├── context/
│   └── JobExecutionContext.ts         # Execution context implementation
├── factory/
│   └── JobHandlerFactory.ts           # Handler factory implementation
├── oracle/
│   └── OracleJobHandler.ts            # Oracle job handler
├── mysql/
│   └── MySQLJobHandler.ts             # MySQL job handler
├── database-admin/
│   └── DatabaseAdminJobHandler.ts     # Database admin job handler
├── sftp/
│   └── SftpJobHandler.ts              # SFTP job handler
├── pdf-dipa/
│   └── PdfDipaJobHandler.ts           # PDF DIPA job handler
└── adk-processing/
    └── AdkProcessingJobHandler.ts     # ADK processing job handler
```

This modular architecture provides a solid foundation for scalable job processing while maintaining backward compatibility and ease of use.
