/**
 * Test script to verify PDF DIPA incremental processing functionality
 * This script tests the new conditional database operations based on file status flags
 */

const mysql = require('mysql2/promise');

// Test configuration
const testConfig = {
  fileMetadataDatabase: {
    host: 'localhost',
    port: 3306,
    database: 'monev2024',
    username: 'root',
    password: '',
    table: 'file_metadata_test'
  },
  destinationDatabase: {
    host: 'localhost',
    port: 3306,
    database: 'pulltest',
    username: 'root',
    password: '',
    table: 'pdf_dipa_test'
  }
};

async function setupTestData() {
  console.log('🔧 Setting up test data...');
  
  const connection = await mysql.createConnection({
    host: testConfig.fileMetadataDatabase.host,
    port: testConfig.fileMetadataDatabase.port,
    database: testConfig.fileMetadataDatabase.database,
    user: testConfig.fileMetadataDatabase.username,
    password: testConfig.fileMetadataDatabase.password,
  });

  try {
    // Create test file metadata table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS ${testConfig.fileMetadataDatabase.table} (
        id INT PRIMARY KEY AUTO_INCREMENT,
        folder VARCHAR(255) NOT NULL,
        nmfile VARCHAR(255) NOT NULL,
        status VARCHAR(10) NOT NULL DEFAULT 'NEW',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_folder (folder)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Clear existing test data
    await connection.execute(`DELETE FROM ${testConfig.fileMetadataDatabase.table}`);

    // Insert test files with different statuses
    const testFiles = [
      { folder: '34/152/2025', nmfile: 'test1.pdf', status: 'NEW' },
      { folder: '34/152/2025', nmfile: 'test2.pdf', status: 'NEW' },
      { folder: '34/153/2025', nmfile: 'test3.pdf', status: 'OLD' },
      { folder: '34/153/2025', nmfile: 'test4.pdf', status: 'NEW' },
    ];

    for (const file of testFiles) {
      await connection.execute(
        `INSERT INTO ${testConfig.fileMetadataDatabase.table} (folder, nmfile, status) VALUES (?, ?, ?)`,
        [file.folder, file.nmfile, file.status]
      );
    }

    console.log(`✅ Inserted ${testFiles.length} test files`);
    
    // Show current file status distribution
    const [statusCounts] = await connection.execute(`
      SELECT status, COUNT(*) as count 
      FROM ${testConfig.fileMetadataDatabase.table} 
      GROUP BY status
    `);
    
    console.log('📊 File status distribution:');
    statusCounts.forEach(row => {
      console.log(`   ${row.status}: ${row.count} files`);
    });

  } finally {
    await connection.end();
  }
}

async function testStatusFiltering() {
  console.log('\n🧪 Testing status-based file filtering...');
  
  const connection = await mysql.createConnection({
    host: testConfig.fileMetadataDatabase.host,
    port: testConfig.fileMetadataDatabase.port,
    database: testConfig.fileMetadataDatabase.database,
    user: testConfig.fileMetadataDatabase.username,
    password: testConfig.fileMetadataDatabase.password,
  });

  try {
    // Test filtering for NEW files (should return 3 files)
    const [newFiles] = await connection.query(
      `SELECT DISTINCT folder, nmfile FROM ${testConfig.fileMetadataDatabase.table}
       WHERE RIGHT(nmfile,3)='pdf' AND status=? ORDER BY folder`,
      ['NEW']
    );
    
    console.log(`✅ Found ${newFiles.length} NEW files to process:`);
    newFiles.forEach(file => {
      console.log(`   ${file.folder}/${file.nmfile}`);
    });

    // Test filtering for OLD files (should return 1 file)
    const [oldFiles] = await connection.query(
      `SELECT DISTINCT folder, nmfile FROM ${testConfig.fileMetadataDatabase.table}
       WHERE RIGHT(nmfile,3)='pdf' AND status=? ORDER BY folder`,
      ['OLD']
    );
    
    console.log(`✅ Found ${oldFiles.length} OLD files (should be skipped):`);
    oldFiles.forEach(file => {
      console.log(`   ${file.folder}/${file.nmfile}`);
    });

  } finally {
    await connection.end();
  }
}

async function testDestinationTableCreation() {
  console.log('\n🏗️ Testing destination table creation...');
  
  const connection = await mysql.createConnection({
    host: testConfig.destinationDatabase.host,
    port: testConfig.destinationDatabase.port,
    database: testConfig.destinationDatabase.database,
    user: testConfig.destinationDatabase.username,
    password: testConfig.destinationDatabase.password,
  });

  try {
    // Drop test table if exists
    await connection.execute(`DROP TABLE IF EXISTS ${testConfig.destinationDatabase.table}`);
    
    // Test table creation (simulating ensureDestinationTable method)
    await connection.query(`
      CREATE TABLE IF NOT EXISTS \`${testConfig.destinationDatabase.table}\` (
        id BIGINT PRIMARY KEY AUTO_INCREMENT,
        kdjendok VARCHAR(10) NOT NULL,
        kddept VARCHAR(10) NOT NULL,
        kdunit VARCHAR(10) NOT NULL,
        kdsatker VARCHAR(20) NOT NULL,
        kddekon VARCHAR(10) NOT NULL,
        norev VARCHAR(10) NOT NULL,
        pagu DECIMAL(15,2) DEFAULT 0,
        ds VARCHAR(255) DEFAULT '',
        kpa TEXT DEFAULT '',
        bendahara TEXT DEFAULT '',
        ppspm TEXT DEFAULT '',
        status VARCHAR(10) DEFAULT 'OLD',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_pdf_record (kdjendok, kddept, kdunit, kdsatker, kddekon, norev),
        INDEX idx_kdsatker (kdsatker),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log(`✅ Destination table ${testConfig.destinationDatabase.table} created successfully`);

  } finally {
    await connection.end();
  }
}

async function testInsertIgnore() {
  console.log('\n🔄 Testing INSERT IGNORE functionality...');
  
  const connection = await mysql.createConnection({
    host: testConfig.destinationDatabase.host,
    port: testConfig.destinationDatabase.port,
    database: testConfig.destinationDatabase.database,
    user: testConfig.destinationDatabase.username,
    password: testConfig.destinationDatabase.password,
  });

  try {
    // Test data
    const testData = {
      kdjendok: '01',
      kddept: '034',
      kdunit: '15',
      kdsatker: '450774',
      kddekon: '2',
      norev: '00',
      pagu: 1000000.50,
      ds: 'TEST001',
      kpa: 'Test KPA',
      bendahara: 'Test Bendahara',
      ppspm: 'Test PPSPM',
      status: 'OLD'
    };

    // First insert (should succeed)
    const columns = Object.keys(testData);
    const values = Object.values(testData);
    const placeholders = columns.map(() => "?").join(", ");

    const insertSQL = `
      INSERT IGNORE INTO \`${testConfig.destinationDatabase.table}\` (\`${columns.join("`, `")}\`)
      VALUES (${placeholders})
    `;

    const [result1] = await connection.execute(insertSQL, values);
    console.log(`✅ First insert: ${result1.affectedRows} row(s) affected`);

    // Second insert with same unique key (should be ignored)
    const [result2] = await connection.execute(insertSQL, values);
    console.log(`✅ Duplicate insert: ${result2.affectedRows} row(s) affected (should be 0)`);

    // Check final count
    const [countResult] = await connection.execute(
      `SELECT COUNT(*) as count FROM ${testConfig.destinationDatabase.table}`
    );
    console.log(`✅ Total records in destination table: ${countResult[0].count}`);

  } finally {
    await connection.end();
  }
}

async function runTests() {
  console.log('🚀 Starting PDF DIPA Incremental Processing Tests\n');
  
  try {
    await setupTestData();
    await testStatusFiltering();
    await testDestinationTableCreation();
    await testInsertIgnore();
    
    console.log('\n✅ All tests completed successfully!');
    console.log('\n📋 Summary of changes implemented:');
    console.log('   ✓ Removed truncate operation from PDF DIPA jobs');
    console.log('   ✓ Implemented status-based processing (NEW vs OLD files)');
    console.log('   ✓ Added INSERT IGNORE for duplicate handling');
    console.log('   ✓ File status updated to OLD after successful processing');
    console.log('   ✓ PDF DIPA jobs handle their own destination saving');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  setupTestData,
  testStatusFiltering,
  testDestinationTableCreation,
  testInsertIgnore,
  runTests
};
