import mysql from "mysql2/promise";
import { logger } from "./jobManager";

// Database connection configuration for system operations
const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3399"),
  user: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "",
  database: process.env.DB_NAME || "pulltest", // Configurable database name
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  // Note: acquireTimeout and timeout are not valid for mysql2 connection config
  // They should be handled at query level if needed
};

// Create connection pool
let pool = mysql.createPool(dbConfig);

// Function to recreate the pool if needed
export function recreatePool(): void {
  pool = mysql.createPool(dbConfig);
}

// Database initialization function
export async function initializeDatabase(): Promise<void> {
  try {
    logger.info("Initializing database tables...");

    // Recreate pool if it was previously closed
    recreatePool();

    // Create tables if they don't exist
    await createTables();

    logger.info("Database initialization completed successfully");
  } catch (error) {
    logger.error("Failed to initialize database:", error);
    throw error;
  }
}

// Create all required tables
async function createTables(): Promise<void> {
  const connection = await pool.getConnection();

  try {
    // App configuration table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_config (
        id INT PRIMARY KEY AUTO_INCREMENT,
        config_key VARCHAR(255) UNIQUE NOT NULL,
        config_value TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_config_key (config_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Job definitions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS job_definitions (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        schedule_cron VARCHAR(100) NOT NULL,
        enabled BOOLEAN DEFAULT TRUE,
        data_source_type ENUM('oracle', 'sftp', 'mysql', 'database_admin', 'pdf_dipa', 'adk_processing') NOT NULL,
        data_source_config JSON,
        destination_config JSON,
        retry_config JSON,
        sequence_id VARCHAR(36) NULL,
        sequence_order INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_job_enabled (enabled),
        INDEX idx_job_type (data_source_type),
        INDEX idx_job_sequence (sequence_id, sequence_order)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Job executions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS job_executions (
        id VARCHAR(100) PRIMARY KEY,
        job_id VARCHAR(36) NOT NULL,
        status ENUM('running', 'completed', 'failed', 'scheduled', 'stopped') NOT NULL,
        trigger_type ENUM('manual', 'automatic') NOT NULL,
        start_time TIMESTAMP NOT NULL,
        end_time TIMESTAMP NULL,
        duration_seconds INT NULL,
        records_processed INT NULL,
        error_message TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (job_id) REFERENCES job_definitions(id) ON DELETE CASCADE,
        INDEX idx_job_executions_job_id (job_id),
        INDEX idx_job_executions_status (status),
        INDEX idx_job_executions_start_time (start_time)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Job execution logs table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS job_execution_logs (
        id BIGINT PRIMARY KEY AUTO_INCREMENT,
        execution_id VARCHAR(100) NOT NULL,
        log_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        log_level ENUM('info', 'warn', 'error', 'debug') DEFAULT 'info',
        message TEXT NOT NULL,
        FOREIGN KEY (execution_id) REFERENCES job_executions(id) ON DELETE CASCADE,
        INDEX idx_job_logs_execution_id (execution_id),
        INDEX idx_job_logs_timestamp (log_timestamp),
        INDEX idx_job_logs_level (log_level)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Job sequences table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS job_sequences (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        schedule_cron VARCHAR(100) NULL,
        enabled BOOLEAN DEFAULT TRUE,
        on_failure ENUM('stop', 'continue', 'retry') DEFAULT 'stop',
        max_retries INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_sequence_enabled (enabled),
        INDEX idx_sequence_schedule (schedule_cron)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Job sequence executions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS job_sequence_executions (
        id VARCHAR(100) PRIMARY KEY,
        sequence_id VARCHAR(36) NOT NULL,
        status ENUM('running', 'completed', 'failed', 'stopped') NOT NULL,
        current_job_id VARCHAR(36) NULL,
        current_job_order INT NULL,
        start_time TIMESTAMP NOT NULL,
        end_time TIMESTAMP NULL,
        duration_seconds INT NULL,
        trigger_type ENUM('manual', 'automatic') NOT NULL,
        error_message TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (sequence_id) REFERENCES job_sequences(id) ON DELETE CASCADE,
        FOREIGN KEY (current_job_id) REFERENCES job_definitions(id) ON DELETE SET NULL,
        INDEX idx_sequence_executions_sequence_id (sequence_id),
        INDEX idx_sequence_executions_status (status),
        INDEX idx_sequence_executions_start_time (start_time)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Add foreign key constraint for job_definitions.sequence_id
    // This must be done after job_sequences table is created
    await connection
      .execute(
        `
      ALTER TABLE job_definitions
      ADD CONSTRAINT fk_job_sequence
      FOREIGN KEY (sequence_id) REFERENCES job_sequences(id) ON DELETE SET NULL
    `
      )
      .catch(() => {
        // Ignore error if constraint already exists
      });

    // System settings table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS system_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        setting_category VARCHAR(100) NOT NULL,
        setting_key VARCHAR(255) NOT NULL,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_category_key (setting_category, setting_key),
        INDEX idx_settings_category (setting_category)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Job schedule history table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS job_schedule_history (
        id BIGINT PRIMARY KEY AUTO_INCREMENT,
        job_id VARCHAR(36) NOT NULL,
        scheduled_time TIMESTAMP NOT NULL,
        actual_start_time TIMESTAMP NULL,
        execution_id VARCHAR(100) NULL,
        status ENUM('scheduled', 'started', 'skipped', 'failed_to_start') NOT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (job_id) REFERENCES job_definitions(id) ON DELETE CASCADE,
        FOREIGN KEY (execution_id) REFERENCES job_executions(id) ON DELETE SET NULL,
        INDEX idx_schedule_history_job_id (job_id),
        INDEX idx_schedule_history_scheduled_time (scheduled_time),
        INDEX idx_schedule_history_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // SFTP file transfers table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS sftp_file_transfers (
        id BIGINT PRIMARY KEY AUTO_INCREMENT,
        job_id VARCHAR(36) NOT NULL,
        execution_id VARCHAR(100) NOT NULL,
        remote_file_path VARCHAR(1000) NOT NULL,
        remote_file_name VARCHAR(500) NOT NULL,
        local_file_path VARCHAR(1000) NOT NULL,
        local_directory VARCHAR(500) NOT NULL,
        file_size_bytes BIGINT NULL,
        file_hash VARCHAR(64) NULL,
        transfer_status ENUM('pending', 'downloading', 'completed', 'failed', 'skipped') NOT NULL DEFAULT 'pending',
        transfer_start_time TIMESTAMP NULL,
        transfer_end_time TIMESTAMP NULL,
        transfer_duration_ms INT NULL,
        error_message TEXT NULL,
        file_modified_date TIMESTAMP NULL,
        file_created_date TIMESTAMP NULL,
        processing_status ENUM('not_processed', 'processing', 'processed', 'failed_processing') DEFAULT 'not_processed',
        processing_notes TEXT NULL,
        metadata JSON NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (job_id) REFERENCES job_definitions(id) ON DELETE CASCADE,
        FOREIGN KEY (execution_id) REFERENCES job_executions(id) ON DELETE CASCADE,
        INDEX idx_sftp_transfers_job_id (job_id),
        INDEX idx_sftp_transfers_execution_id (execution_id),
        INDEX idx_sftp_transfers_status (transfer_status),
        INDEX idx_sftp_transfers_processing_status (processing_status),
        INDEX idx_sftp_transfers_remote_file (remote_file_name),
        INDEX idx_sftp_transfers_created_at (created_at),
        UNIQUE KEY unique_execution_remote_file (execution_id, remote_file_path(500))
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    logger.info("All database tables created successfully");
  } finally {
    connection.release();
  }
}

// Generic database helper functions
export async function executeQuery<T = unknown>(
  query: string,
  params: unknown[] = []
): Promise<T[]> {
  let retries = 0;
  const maxRetries = 2;

  while (retries <= maxRetries) {
    try {
      const connection = await pool.getConnection();
      try {
        const [rows] = await connection.execute(query, params);
        return rows as T[];
      } finally {
        connection.release();
      }
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes("Pool is closed") &&
        retries < maxRetries
      ) {
        logger.warn("Pool is closed, recreating and retrying...");
        recreatePool();
        retries++;
        continue;
      }
      throw error;
    }
  }
  throw new Error("Failed to execute query after retries");
}

export async function executeUpdate(
  query: string,
  params: unknown[] = []
): Promise<{ affectedRows: number; insertId?: number }> {
  let retries = 0;
  const maxRetries = 2;

  while (retries <= maxRetries) {
    try {
      const connection = await pool.getConnection();
      try {
        const [result] = await connection.execute(query, params);
        const resultInfo = result as mysql.ResultSetHeader;
        return {
          affectedRows: resultInfo.affectedRows,
          insertId: resultInfo.insertId,
        };
      } finally {
        connection.release();
      }
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes("Pool is closed") &&
        retries < maxRetries
      ) {
        logger.warn("Pool is closed, recreating and retrying...");
        recreatePool();
        retries++;
        continue;
      }
      throw error;
    }
  }
  throw new Error("Failed to execute update after retries");
}

export async function executeTransaction<T>(
  callback: (connection: mysql.PoolConnection) => Promise<T>
): Promise<T> {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

// Close database connection pool
export async function closeDatabase(): Promise<void> {
  try {
    await pool.end();
    logger.info("Database connection pool closed");
  } catch (error) {
    logger.error("Error closing database connection pool:", error);
    throw error;
  }
}

// Health check for database connection
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    return true;
  } catch (error) {
    logger.error("Database health check failed:", error);
    // If the error indicates the pool is closed, try to recreate it
    if (error instanceof Error && error.message.includes("Pool is closed")) {
      logger.info("Pool is closed, recreating...");
      recreatePool();
      try {
        const connection = await pool.getConnection();
        await connection.ping();
        connection.release();
        return true;
      } catch (retryError) {
        logger.error("Failed to reconnect after recreating pool:", retryError);
        return false;
      }
    }
    return false;
  }
}

// Function to execute queries on a different database (like monev2024)
export async function executeQueryOnDatabase<T = unknown>(
  databaseName: string,
  query: string,
  params: unknown[] = []
): Promise<T[]> {
  let retries = 0;
  const maxRetries = 2;

  while (retries <= maxRetries) {
    try {
      // Create a temporary connection with different database
      const tempConfig = {
        ...dbConfig,
        database: databaseName,
      };
      const tempPool = mysql.createPool(tempConfig);

      try {
        const connection = await tempPool.getConnection();
        try {
          const [rows] = await connection.execute(query, params);
          return rows as T[];
        } finally {
          connection.release();
        }
      } finally {
        await tempPool.end();
      }
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes("Pool is closed") &&
        retries < maxRetries
      ) {
        logger.warn("Pool is closed, recreating and retrying...");
        retries++;
        continue;
      }
      throw error;
    }
  }
  throw new Error("Failed to execute query on external database after retries");
}

// Function to execute updates on a different database (like monev2024)
export async function executeUpdateOnDatabase(
  databaseName: string,
  query: string,
  params: unknown[] = []
): Promise<{ affectedRows: number; insertId?: number }> {
  let retries = 0;
  const maxRetries = 2;

  while (retries <= maxRetries) {
    try {
      // Create a temporary connection with different database
      const tempConfig = {
        ...dbConfig,
        database: databaseName,
      };
      const tempPool = mysql.createPool(tempConfig);

      try {
        const connection = await tempPool.getConnection();
        try {
          const [result] = await connection.execute(query, params);
          const resultInfo = result as mysql.ResultSetHeader;
          return {
            affectedRows: resultInfo.affectedRows,
            insertId: resultInfo.insertId,
          };
        } finally {
          connection.release();
        }
      } finally {
        await tempPool.end();
      }
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes("Pool is closed") &&
        retries < maxRetries
      ) {
        logger.warn("Pool is closed, recreating and retrying...");
        retries++;
        continue;
      }
      throw error;
    }
  }
  throw new Error(
    "Failed to execute update on external database after retries"
  );
}

export { pool };
