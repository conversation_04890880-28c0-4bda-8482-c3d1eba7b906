const mysql = require("mysql2/promise");

async function checkStatuses() {
  const connection = await mysql.createConnection({
    host: "localhost",
    port: 3306,
    user: "root",
    password: "",
    database: "monev2025",
  });

  console.log("Checking file statuses for PDF files...");
  const [rows] = await connection.query(
    "SELECT status, COUNT(*) as count FROM file_metadata WHERE RIGHT(nmfile,3)='pdf' GROUP BY status ORDER BY status"
  );
  console.log("Status distribution:");
  rows.forEach((row) => {
    console.log(`  ${row.status}: ${row.count} files`);
  });

  console.log("\nSample files with their current status:");
  const [samples] = await connection.query(
    "SELECT folder, nmfile, status FROM file_metadata WHERE RIGHT(nmfile,3)='pdf' LIMIT 10"
  );
  samples.forEach((file) => {
    console.log(`  ${file.folder} / ${file.nmfile} -> ${file.status}`);
  });

  await connection.end();
}

checkStatuses().catch(console.error);
