import * as cron from "node-cron";
import { getJobDefinitions } from "./jobManager";
import { runDataPullingJob } from "./jobRunner";
import { logger } from "./jobManager";
import { loadJobSequences } from "./sequencePersistence";
import JobSequenceManager from "./jobSequenceManager";

interface ScheduledJob {
  jobId: string;
  cronJob: cron.ScheduledTask;
}

interface ScheduledSequence {
  sequenceId: string;
  cronJob: cron.ScheduledTask;
}

class CronScheduler {
  private scheduledJobs: Map<string, ScheduledJob> = new Map();
  private scheduledSequences: Map<string, ScheduledSequence> = new Map();

  async initializeScheduler(): Promise<void> {
    logger.info("Initializing cron scheduler...");

    try {
      // Load job definitions and sequences
      const [jobDefinitions, sequences] = await Promise.all([
        getJobDefinitions(),
        loadJobSequences(),
      ]);

      // Schedule independent jobs (not part of sequences)
      for (const jobDef of jobDefinitions) {
        if (jobDef.enabled && !jobDef.sequenceConfig) {
          this.scheduleJob(jobDef.id, jobDef.schedule, jobDef.name);
        }
      }

      // Schedule sequences
      for (const sequence of sequences) {
        if (sequence.enabled && sequence.schedule) {
          this.scheduleSequence(sequence.id, sequence.schedule, sequence.name);
        }
      }

      logger.info(
        `Cron scheduler initialized with ${this.scheduledJobs.size} active jobs and ${this.scheduledSequences.size} active sequences`
      );
    } catch (error) {
      logger.error("Failed to initialize cron scheduler", {
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  scheduleJob(jobId: string, cronExpression: string, jobName: string): void {
    try {
      // Validate cron expression
      if (!cron.validate(cronExpression)) {
        throw new Error(`Invalid cron expression: ${cronExpression}`);
      }

      // Remove existing scheduled job if it exists
      this.unscheduleJob(jobId);

      // Create new scheduled job
      const cronJob = cron.schedule(
        cronExpression,
        async () => {
          logger.info(`Cron triggered job execution for ${jobName}`, { jobId });

          try {
            await runDataPullingJob(jobId, "automatic");
          } catch (error) {
            logger.error(`Scheduled job ${jobId} failed`, {
              jobId,
              jobName,
              error: error instanceof Error ? error.message : "Unknown error",
            });
          }
        },
        {
          timezone: process.env.TIMEZONE || "Asia/Jakarta",
        }
      );

      this.scheduledJobs.set(jobId, {
        jobId,
        cronJob,
      });

      logger.info(
        `Job ${jobName} scheduled with cron expression: ${cronExpression}`,
        {
          jobId,
          cronExpression,
        }
      );
    } catch (error) {
      logger.error(`Failed to schedule job ${jobId}`, {
        jobId,
        jobName,
        cronExpression,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  scheduleSequence(
    sequenceId: string,
    cronExpression: string,
    sequenceName: string
  ): void {
    try {
      // Validate cron expression
      if (!cron.validate(cronExpression)) {
        throw new Error(`Invalid cron expression: ${cronExpression}`);
      }

      // Remove existing scheduled sequence if it exists
      this.unscheduleSequence(sequenceId);

      // Create new scheduled sequence
      const cronJob = cron.schedule(
        cronExpression,
        async () => {
          logger.info(`Cron triggered sequence execution for ${sequenceName}`, {
            sequenceId,
          });

          try {
            const sequenceManager = JobSequenceManager.getInstance();
            await sequenceManager.executeSequence(sequenceId, "automatic");
          } catch (error) {
            logger.error(`Scheduled sequence ${sequenceId} failed`, {
              sequenceId,
              sequenceName,
              error: error instanceof Error ? error.message : "Unknown error",
            });
          }
        },
        {
          timezone: process.env.TIMEZONE || "Asia/Jakarta",
        }
      );

      this.scheduledSequences.set(sequenceId, {
        sequenceId,
        cronJob,
      });

      logger.info(
        `Sequence ${sequenceName} scheduled with cron expression: ${cronExpression}`,
        {
          sequenceId,
          cronExpression,
        }
      );
    } catch (error) {
      logger.error(`Failed to schedule sequence ${sequenceId}`, {
        sequenceId,
        sequenceName,
        cronExpression,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  unscheduleJob(jobId: string): void {
    const scheduledJob = this.scheduledJobs.get(jobId);

    if (scheduledJob) {
      scheduledJob.cronJob.stop();
      scheduledJob.cronJob.destroy();
      this.scheduledJobs.delete(jobId);

      logger.info(`Job ${jobId} unscheduled`);
    }
  }

  unscheduleSequence(sequenceId: string): void {
    const scheduledSequence = this.scheduledSequences.get(sequenceId);

    if (scheduledSequence) {
      scheduledSequence.cronJob.stop();
      scheduledSequence.cronJob.destroy();
      this.scheduledSequences.delete(sequenceId);

      logger.info(`Sequence ${sequenceId} unscheduled`);
    }
  }

  stopJob(jobId: string): void {
    const scheduledJob = this.scheduledJobs.get(jobId);

    if (scheduledJob) {
      scheduledJob.cronJob.stop();
      logger.info(`Job ${jobId} stopped but not destroyed`);
    }
  }

  startJob(jobId: string): void {
    const scheduledJob = this.scheduledJobs.get(jobId);

    if (scheduledJob) {
      scheduledJob.cronJob.start();
      logger.info(`Job ${jobId} started`);
    }
  }

  rescheduleJob(
    jobId: string,
    newCronExpression: string,
    jobName: string
  ): void {
    this.unscheduleJob(jobId);
    this.scheduleJob(jobId, newCronExpression, jobName);
  }

  getScheduledJobs(): string[] {
    return Array.from(this.scheduledJobs.keys());
  }

  isJobScheduled(jobId: string): boolean {
    return this.scheduledJobs.has(jobId);
  }

  stopAllJobs(): void {
    logger.info("Stopping all scheduled jobs...");

    for (const [jobId, scheduledJob] of this.scheduledJobs) {
      scheduledJob.cronJob.stop();
      scheduledJob.cronJob.destroy();
      logger.info(`Stopped job ${jobId}`);
    }

    this.scheduledJobs.clear();
    logger.info("All scheduled jobs stopped");
  }

  // Sequence-related methods
  stopSequence(sequenceId: string): void {
    const scheduledSequence = this.scheduledSequences.get(sequenceId);

    if (scheduledSequence) {
      scheduledSequence.cronJob.stop();
      logger.info(`Sequence ${sequenceId} stopped`);
    }
  }

  startSequence(sequenceId: string): void {
    const scheduledSequence = this.scheduledSequences.get(sequenceId);

    if (scheduledSequence) {
      scheduledSequence.cronJob.start();
      logger.info(`Sequence ${sequenceId} started`);
    }
  }

  rescheduleSequence(
    sequenceId: string,
    newCronExpression: string,
    sequenceName: string
  ): void {
    this.unscheduleSequence(sequenceId);
    this.scheduleSequence(sequenceId, newCronExpression, sequenceName);
  }

  getScheduledSequences(): string[] {
    return Array.from(this.scheduledSequences.keys());
  }

  isSequenceScheduled(sequenceId: string): boolean {
    return this.scheduledSequences.has(sequenceId);
  }

  stopAllSequences(): void {
    logger.info("Stopping all scheduled sequences...");

    for (const [sequenceId, scheduledSequence] of this.scheduledSequences) {
      scheduledSequence.cronJob.stop();
      scheduledSequence.cronJob.destroy();
      logger.info(`Stopped sequence ${sequenceId}`);
    }

    this.scheduledSequences.clear();
    logger.info("All scheduled sequences stopped");
  }

  async refreshScheduler(): Promise<void> {
    logger.info(
      "Refreshing scheduler with latest job definitions and sequences..."
    );

    // Stop all current jobs and sequences
    this.stopAllJobs();
    this.stopAllSequences();

    // Reinitialize with latest job definitions and sequences
    await this.initializeScheduler();
  }
}

// Create singleton instance
export const cronScheduler = new CronScheduler();

// Initialize scheduler when module is loaded
let signalHandlersRegistered = false;

export async function initializeCronScheduler(): Promise<void> {
  // Always reinitialize the scheduler to pick up any job changes
  await cronScheduler.initializeScheduler();

  // Only register signal handlers once
  if (!signalHandlersRegistered) {
    // Handle graceful shutdown
    process.on("SIGINT", () => {
      logger.info("Received SIGINT, stopping all cron jobs...");
      cronScheduler.stopAllJobs();
      process.exit(0);
    });

    process.on("SIGTERM", () => {
      logger.info("Received SIGTERM, stopping all cron jobs...");
      cronScheduler.stopAllJobs();
      process.exit(0);
    });

    signalHandlersRegistered = true;
  }
}

export function resetCronScheduler(): void {
  cronScheduler.stopAllJobs();
  cronScheduler.stopAllSequences();
}

export { CronScheduler };
