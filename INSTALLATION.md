# Sintesa Data Puller - Complete Installation Guide

A comprehensive installation guide for the enterprise-grade data extraction and synchronization platform. This guide covers everything from basic setup to advanced configuration for production environments.

## 📋 System Requirements

### Minimum Requirements

- **Node.js** 18.0 or higher (LTS recommended)
- **MySQL** 5.7+ or MariaDB 10.3+
- **RAM** 4GB minimum (8GB recommended for production)
- **Storage** 10GB free space (more for data processing)
- **Git** for repository management

### Operating System Support

- **Windows** 10/11, Windows Server 2019/2022
- **Linux** Ubuntu 20.04+, CentOS 8+, RHEL 8+
- **macOS** 11.0+ (Big Sur and later)

### Optional Components

- **Oracle Instant Client** (for Oracle database connectivity)
- **SFTP Server Access** (for file transfer jobs)
- **PDF Processing Tools** (for PDF DIPA jobs)
- **Archive Tools** (RAR/7-Zip for ADK processing)

## 🚀 Step-by-Step Installation

### Step 1: Repository Setup

1. **Clone the Repository**

   ```bash
   git clone <your-repository-url>
   cd sintesa-datapuller
   ```

2. **Verify Node.js Version**
   ```bash
   node --version  # Should be 18.0 or higher
   npm --version   # Should be 8.0 or higher
   ```

### Step 2: Dependency Installation

1. **Install Core Dependencies**

   ```bash
   npm install
   ```

2. **Verify Installation**
   ```bash
   npm list --depth=0  # Check installed packages
   ```

### Step 3: Environment Configuration

1. **Create Environment File**

   ```bash
   # Copy the example file
   cp .env.example .env.local
   ```

2. **Configure Database Settings**

   Edit `.env.local` with your database configuration:

   ```env
   # Main Application Database (Required)
   DB_HOST=localhost
   DB_PORT=3306
   DB_USER=root
   DB_PASSWORD=your_secure_password
   DB_NAME=pulltest

   # Timezone Configuration
   TIMEZONE=America/New_York  # Adjust to your timezone
   ```

3. **Configure Data Source Connections (Optional)**

   Add configurations for your specific data sources:

   ```env
   # Oracle Database (for Oracle jobs)
   ORACLE_HOST=your_oracle_host.company.com
   ORACLE_PORT=1521
   ORACLE_SERVICE_NAME=XEPDB1
   ORACLE_USERNAME=your_oracle_user
   ORACLE_PASSWORD=your_oracle_password

   # SFTP Server (for SFTP jobs)
   SFTP_HOST=sftp.yourcompany.com
   SFTP_PORT=22
   SFTP_USERNAME=your_sftp_user
   SFTP_PASSWORD=your_sftp_password

   # Application Settings
   NODE_ENV=development
   LOG_LEVEL=info
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

### Step 4: Database Setup

#### Option A: Quick Setup (Recommended for Development)

1. **Create Database**

   ```sql
   -- Connect to MySQL as root
   mysql -u root -p

   -- Create the main database
   CREATE DATABASE pulltest CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

   -- Exit MySQL
   EXIT;
   ```

2. **Run Migration**
   ```bash
   npm run migrate
   ```

#### Option B: Production Setup with Dedicated User

1. **Create Database and User**

   ```sql
   -- Connect to MySQL as root
   mysql -u root -p

   -- Create the main database
   CREATE DATABASE pulltest CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

   -- Create dedicated user
   CREATE USER 'datapuller'@'localhost' IDENTIFIED BY 'secure_password_here';
   GRANT ALL PRIVILEGES ON pulltest.* TO 'datapuller'@'localhost';

   -- Create additional databases for file tracking (optional)
   CREATE DATABASE file_tracking CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   GRANT ALL PRIVILEGES ON file_tracking.* TO 'datapuller'@'localhost';

   FLUSH PRIVILEGES;
   EXIT;
   ```

2. **Update Environment File**

   ```env
   DB_USER=datapuller
   DB_PASSWORD=secure_password_here
   ```

3. **Run Migration**
   ```bash
   npm run migrate
   ```

#### Migration Results

The migration script will create:

- ✅ **6 Core Tables**: job_definitions, job_executions, job_execution_logs, job_schedule_history, system_settings, app_config
- ✅ **2 Sequence Tables**: job_sequences, job_sequence_executions
- ✅ **Default Settings**: Application configuration with sensible defaults
- ✅ **Production-Ready Jobs**: 6 default job templates based on current production configurations
- ✅ **System Initialization**: Ready-to-use configuration with 2025 data sources

### Step 5: Application Startup

#### Development Mode (Recommended for Testing)

```bash
# Start with hot reload and debugging
npm run dev
```

The application will start on http://localhost:3000 with:

- ✅ Hot module replacement for instant updates
- ✅ Detailed error messages and stack traces
- ✅ Development-optimized builds

#### Production Mode

```bash
# Build optimized version
npm run build

# Start production server
npm start
```

Production mode provides:

- ✅ Optimized bundle sizes
- ✅ Enhanced performance
- ✅ Production error handling

### Step 6: Verify Installation

1. **Access the Dashboard**

   Open your browser and navigate to:

   - **Development**: http://localhost:3000
   - **Production**: http://your-server:3000

2. **Check System Status**

   The dashboard should display:

   - ✅ Green connection indicator
   - ✅ System status: "Healthy"
   - ✅ Database connection: "Connected"
   - ✅ Job definitions loaded

3. **Verify Default Jobs**

   You should see pre-configured jobs:

   - **Job 1**: Oracle data pulling (Tarik Pagu Real)
   - **Job 4**: SFTP bulk download with file tracking
   - Additional job templates for all supported types

## 🎛️ Initial Configuration & Testing

### Configure Your First Job

#### Option 1: Oracle Database Job

1. **Open Job Administration**

   - Click the database icon in the top-right corner
   - Navigate to the job list

2. **Edit Oracle Job**

   - Find "Tarik Pagu Real" job
   - Click edit to configure:
     - Oracle host and credentials
     - SQL query for data extraction
     - Destination database settings

3. **Test Connection**
   ```bash
   # Test Oracle connectivity
   curl -X POST http://localhost:3000/api/oracle/test \
     -H "Content-Type: application/json" \
     -d '{"query": "SELECT 1 FROM DUAL", "maxRows": 1}'
   ```

#### Option 2: SFTP File Transfer Job

1. **Configure SFTP Job**

   - Edit the SFTP bulk download job
   - Set SFTP server credentials
   - Configure remote and local paths
   - Enable file tracking if needed

2. **Test SFTP Job**
   - Go to Dashboard
   - Find your SFTP job
   - Click "Run Job" to test download

### Advanced Configuration

#### Enable Job Sequencing

1. **Create a Sequence**

   - Open Job Administration
   - Click "Sequences" tab
   - Create new sequence with multiple jobs

2. **Configure Sequence Settings**
   - Set execution order
   - Configure failure handling (stop/continue/retry)
   - Set sequence schedule

#### Configure File Tracking

For SFTP jobs that need audit trails:

1. **Enable File Tracking**

   - Toggle "Enable file tracking in database"
   - Configure tracking database connection
   - Set custom table name

2. **Tracking Options**

   ```env
   # Use existing database
   Database: monev2024
   Table: file_metadata

   # Or create dedicated tracking database
   Database: file_tracking
   Table: downloaded_files
   ```

## 📊 File Tracking Configuration

### What is File Tracking?

File tracking allows you to monitor and log all files downloaded via SFTP jobs to a database table for audit and processing purposes.

### Configuration Options:

#### Option 1: Use Existing Database

```
Database Host: localhost
Database Port: 3306
Database Name: monev2024  (or your existing DB)
Username: your_db_user
Password: your_db_password
Table Name: file_metadata
```

#### Option 2: Use Dedicated Tracking Database

```
Database Host: localhost
Database Port: 3306
Database Name: file_tracking
Username: tracking_user
Password: tracking_password
Table Name: downloaded_files
```

#### Option 3: Disable File Tracking

Simply leave the "Enable file tracking" toggle **OFF** for jobs that don't need tracking.

### Automatic Features:

- ✅ **Table Creation**: System creates tracking tables automatically
- ✅ **Duplicate Prevention**: Prevents duplicate file entries
- ✅ **Connection Pooling**: Efficient database connections
- ✅ **Error Handling**: Graceful handling of database issues

## 🔧 Advanced Configuration

### Custom Database Names

You can use any database name by updating your `.env.local`:

```env
DB_NAME=my_custom_datapuller_db
```

Then run the migration again:

```bash
npm run migrate
```

### Multiple Environments

Create different environment files:

- `.env.local` (development)
- `.env.production` (production)
- `.env.staging` (staging)

### Scheduler Configuration

The application includes a built-in cron scheduler. Configure job schedules in the Job Administration panel using standard cron expressions:

- `0 2 * * *` - Daily at 2 AM
- `30 3 * * *` - Daily at 3:30 AM
- `0 */6 * * *` - Every 6 hours
- `0 9 * * 1-5` - Weekdays at 9 AM

## 🛠️ Troubleshooting Guide

### Installation Issues

#### Node.js Version Problems

```bash
# Check current version
node --version

# Install Node.js 18+ using nvm (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

#### Dependency Installation Failures

```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# For permission issues on Linux/macOS
sudo chown -R $(whoami) ~/.npm
```

### Database Issues

#### MySQL Connection Failed

```bash
# Check MySQL service status
# Linux/macOS:
sudo systemctl status mysql
# or
brew services list | grep mysql

# Windows:
net start mysql

# Test connection manually
mysql -h localhost -u root -p
```

#### Migration Failures

```bash
# Verify database exists
mysql -u root -p -e "SHOW DATABASES;"

# Check user permissions
mysql -u root -p -e "SHOW GRANTS FOR 'your_user'@'localhost';"

# Re-run migration with verbose output
DEBUG=* npm run migrate
```

#### Permission Errors

```sql
-- Grant all necessary permissions
GRANT ALL PRIVILEGES ON pulltest.* TO 'your_user'@'localhost';
GRANT CREATE, ALTER, DROP, INDEX ON *.* TO 'your_user'@'localhost';
FLUSH PRIVILEGES;
```

### Application Runtime Issues

#### Port Already in Use

```bash
# Find process using port 3000
lsof -i :3000  # macOS/Linux
netstat -ano | findstr :3000  # Windows

# Kill the process
kill -9 <PID>  # macOS/Linux
taskkill /PID <PID> /F  # Windows
```

#### Environment Variable Issues

```bash
# Verify environment file exists
ls -la .env.local

# Check environment variables are loaded
node -e "require('dotenv').config({path: '.env.local'}); console.log(process.env.DB_HOST);"
```

### Data Source Connection Issues

#### Oracle Database Problems

1. **Install Oracle Instant Client**

   ```bash
   # Download from Oracle website
   # Extract to /opt/oracle/instantclient_21_1 (Linux)
   # Set environment variables
   export ORACLE_HOME=/opt/oracle/instantclient_21_1
   export LD_LIBRARY_PATH=$ORACLE_HOME:$LD_LIBRARY_PATH
   ```

2. **Test Oracle Connection**
   ```bash
   # Use sqlplus to test
   sqlplus username/password@host:port/service_name
   ```

#### SFTP Connection Problems

1. **Test SFTP Manually**

   ```bash
   # Test connection
   sftp username@hostname

   # Test with key authentication
   sftp -i /path/to/private/key username@hostname
   ```

2. **Common SFTP Issues**
   - Firewall blocking port 22
   - SSH key permissions (should be 600)
   - Host key verification failures

### Performance Issues

#### High Memory Usage

```bash
# Monitor Node.js memory usage
node --max-old-space-size=4096 npm start

# Check system resources
top  # Linux/macOS
taskmgr  # Windows
```

#### Slow Database Queries

```sql
-- Enable MySQL slow query log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- Check for missing indexes
SHOW INDEX FROM job_definitions;
```

### Log Analysis

#### Application Logs

```bash
# View real-time logs
tail -f logs/combined.log

# Search for errors
grep -i error logs/combined.log

# View last 100 lines
tail -n 100 logs/error.log
```

#### Debug Mode

```bash
# Start with debug logging
LOG_LEVEL=debug npm run dev

# Enable database query logging
DEBUG=mysql:* npm run dev
```

### Getting Help

#### Before Reporting Issues

1. **Check System Requirements**

   - Node.js version 18+
   - MySQL 5.7+ running
   - Sufficient disk space

2. **Verify Configuration**

   - Environment variables set correctly
   - Database credentials valid
   - Network connectivity to data sources

3. **Review Logs**
   - Application logs for errors
   - Database logs for connection issues
   - System logs for resource problems

#### Information to Include

When seeking support, provide:

- Operating system and version
- Node.js and npm versions
- MySQL version
- Complete error messages
- Relevant log excerpts
- Steps to reproduce the issue
- Environment configuration (without sensitive data)

## 🎯 Next Steps

After successful installation, follow these steps to get the most out of your data puller:

### Immediate Actions

1. **Verify System Health**

   - Check dashboard for green status indicators
   - Confirm all services are running properly
   - Test database connectivity

2. **Configure Your First Job**

   - Choose between Oracle, MySQL, or SFTP job types
   - Set up data source connections
   - Configure destination settings
   - Test job execution

3. **Set Up Monitoring**
   - Configure system settings for notifications
   - Set up email alerts for job failures
   - Review performance metrics

### Advanced Setup

4. **Create Job Sequences**

   - Design workflow dependencies
   - Configure error handling strategies
   - Set up complex data processing pipelines

5. **Optimize Performance**

   - Tune database connection pools
   - Configure retry policies
   - Set up appropriate logging levels

6. **Production Readiness**
   - Configure backup schedules
   - Set up monitoring and alerting
   - Review security settings

## 📚 Documentation Resources

### Core Guides

- **[README.md](README.md)** - Complete feature overview and API reference
- **[Job Configuration Guide](ORACLE_SFTP_CONFIG.md)** - Detailed job setup instructions
- **[Job Sequencing Guide](docs/JOB_SEQUENCING.md)** - Advanced workflow management

### Specialized Documentation

- **[ADK Processing Guide](ADK_BULK_DOWNLOAD_DOCUMENTATION.md)** - Archive processing setup
- **[SFTP Schema Documentation](SFTP_SCHEMA_DOCUMENTATION.md)** - File transfer configurations
- **[Database Templates](database-admin-templates.json)** - Pre-built database operations

### API References

- **REST API Endpoints** - See README.md API section
- **Job Management APIs** - Complete CRUD operations
- **System Status APIs** - Health monitoring endpoints

## 🚀 What's New in This Version

### Enhanced Job Management

- ✅ **6 Job Types**: Oracle, MySQL, SFTP, Database Admin, PDF DIPA, ADK Processing
- ✅ **Advanced Sequencing**: Dependency management with error handling
- ✅ **Real-time Dashboard**: Live updates with Server-Sent Events
- ✅ **Configurable File Tracking**: Flexible audit trails for file operations

### Modern Architecture

- ✅ **Next.js 15**: Latest React framework with App Router
- ✅ **TypeScript**: Full type safety and developer experience
- ✅ **HeroUI Components**: Modern, accessible UI components
- ✅ **MySQL Integration**: Optimized database operations with pooling

### Enterprise Features

- ✅ **Backup & Restore**: Complete system state management
- ✅ **Settings Framework**: Categorized configuration management
- ✅ **Performance Monitoring**: Detailed execution metrics
- ✅ **Comprehensive Logging**: Structured logging with rotation

## 🎉 Installation Complete!

Your Sintesa Data Puller is now ready for production use. The system includes:

- **Pre-configured job templates** for all supported data sources
- **Comprehensive database schema** with optimized indexes
- **Real-time monitoring dashboard** with live status updates
- **Advanced job sequencing** for complex workflows
- **Flexible file tracking** for audit and compliance
- **Enterprise-grade features** for production environments

### Quick Start Commands

```bash
# Start development server
npm run dev

# Access dashboard
open http://localhost:3000

# View logs
tail -f logs/combined.log

# Run migration (if needed)
npm run migrate
```

**Happy data pulling!** 🚀
