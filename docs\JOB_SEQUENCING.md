# Job Sequencing Feature

## Overview

The Job Sequencing feature allows you to define and execute jobs in a specific order, ensuring that each job in a sequence waits for the previous job to complete before starting. This is useful for ETL pipelines, data processing workflows, and any scenario where job dependencies exist.

## Key Features

- **Sequential Execution**: Jobs run in a defined order, one after another
- **Flexible Error Handling**: Configure how sequences handle job failures (stop, continue, or retry)
- **Scheduling**: Sequences can be scheduled using cron expressions
- **Monitoring**: Real-time status tracking and execution history
- **Backward Compatibility**: Existing independent jobs continue to work unchanged

## Core Concepts

### Job Sequences

A **Job Sequence** is a collection of jobs that execute in a specific order. Each sequence has:

- **ID**: Unique identifier for the sequence
- **Name**: Human-readable name
- **Description**: Optional description of the sequence purpose
- **Schedule**: Optional cron expression for automatic execution
- **Jobs**: Ordered list of job IDs to execute
- **Failure Handling**: How to handle job failures (`stop`, `continue`, `retry`)
- **Max Retries**: Number of retry attempts for failed jobs (when using `retry` mode)

### Failure Handling Strategies

1. **Stop** (default): Stop the entire sequence if any job fails
2. **Continue**: Continue with the next job even if the current job fails
3. **Retry**: Retry the failed job up to `maxRetries` times, then stop if still failing

## Database Schema

### New Tables

#### `job_sequences`
Stores sequence definitions:
```sql
CREATE TABLE job_sequences (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  schedule_cron VARCHAR(100) NULL,
  enabled BOOLEAN DEFAULT TRUE,
  on_failure ENUM('stop', 'continue', 'retry') DEFAULT 'stop',
  max_retries INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### `job_sequence_executions`
Tracks sequence execution history:
```sql
CREATE TABLE job_sequence_executions (
  id VARCHAR(100) PRIMARY KEY,
  sequence_id VARCHAR(36) NOT NULL,
  status ENUM('running', 'completed', 'failed', 'stopped') NOT NULL,
  current_job_id VARCHAR(36) NULL,
  current_job_order INT NULL,
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP NULL,
  duration_seconds INT NULL,
  trigger_type ENUM('manual', 'automatic') NOT NULL,
  error_message TEXT NULL,
  FOREIGN KEY (sequence_id) REFERENCES job_sequences(id) ON DELETE CASCADE
);
```

### Modified Tables

#### `job_definitions`
Added sequence assignment fields:
```sql
ALTER TABLE job_definitions 
ADD COLUMN sequence_id VARCHAR(36) NULL,
ADD COLUMN sequence_order INT NULL,
ADD FOREIGN KEY (sequence_id) REFERENCES job_sequences(id) ON DELETE SET NULL;
```

## API Endpoints

### Sequence Management

- `GET /api/admin/sequences` - List all sequences
- `POST /api/admin/sequences` - Create new sequence
- `PUT /api/admin/sequences` - Update sequence
- `DELETE /api/admin/sequences?id={id}` - Delete sequence

### Individual Sequence Operations

- `GET /api/admin/sequences/{id}` - Get sequence details with execution history
- `PUT /api/admin/sequences/{id}` - Update specific sequence
- `DELETE /api/admin/sequences/{id}` - Delete specific sequence

### Execution Control

- `POST /api/admin/sequences/{id}/execute` - Execute sequence manually
- `POST /api/admin/sequences/{id}/stop` - Stop running sequence
- `GET /api/admin/sequences/{id}/status` - Get detailed sequence status

### Job Assignment

- `PUT /api/admin/jobs/{id}/sequence` - Assign job to sequence
- `DELETE /api/admin/jobs/{id}/sequence` - Remove job from sequence

## Usage Examples

### Creating a Sequence

```javascript
const sequence = {
  id: "etl-pipeline-1",
  name: "Daily ETL Pipeline",
  description: "Extract, transform, and load daily data",
  schedule: "0 2 * * *", // Daily at 2 AM
  enabled: true,
  onFailure: "stop",
  maxRetries: 1,
  jobs: ["extract-job", "transform-job", "load-job"]
};

// Create sequence
await fetch('/api/admin/sequences', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ sequence })
});
```

### Assigning Jobs to Sequence

```javascript
// Assign jobs to sequence in order
await fetch('/api/admin/jobs/extract-job/sequence', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ sequenceId: "etl-pipeline-1", order: 1 })
});

await fetch('/api/admin/jobs/transform-job/sequence', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ sequenceId: "etl-pipeline-1", order: 2 })
});

await fetch('/api/admin/jobs/load-job/sequence', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ sequenceId: "etl-pipeline-1", order: 3 })
});
```

### Manual Execution

```javascript
// Execute sequence manually
const response = await fetch('/api/admin/sequences/etl-pipeline-1/execute', {
  method: 'POST'
});

const result = await response.json();
console.log(`Execution started: ${result.executionId}`);
```

### Monitoring Execution

```javascript
// Get sequence status
const response = await fetch('/api/admin/sequences/etl-pipeline-1/status');
const status = await response.json();

console.log(`Sequence: ${status.status.sequenceName}`);
console.log(`Status: ${status.status.currentExecution?.status || 'Not running'}`);
console.log(`Current Job: ${status.status.currentExecution?.currentJobId || 'None'}`);
console.log(`Progress: ${status.status.currentExecution?.currentJobOrder || 0}/${status.status.totalJobs}`);
```

## UI Integration

### Accessing Sequence Management

1. Open the Job Administration modal
2. Click the "Sequences" button in the header
3. View, create, edit, and manage sequences

### Sequence List View

The sequences view shows:
- Sequence name and description
- Enabled/disabled status
- Number of jobs in sequence
- Failure handling strategy
- Schedule information

### Creating Sequences

1. In the sequences view, click "Create Sequence"
2. Fill in sequence details (name, description, schedule)
3. Configure failure handling and retry settings
4. Assign jobs to the sequence using the job assignment API

## Best Practices

### Sequence Design

1. **Keep sequences focused**: Group related jobs that have clear dependencies
2. **Use descriptive names**: Make sequence purpose clear from the name
3. **Document dependencies**: Use descriptions to explain why jobs need to run in order
4. **Test thoroughly**: Verify sequences work correctly with different failure scenarios

### Error Handling

1. **Choose appropriate failure strategy**:
   - Use `stop` for critical pipelines where any failure should halt processing
   - Use `continue` for data collection where some failures are acceptable
   - Use `retry` for jobs that might fail due to temporary issues

2. **Set reasonable retry limits**: Avoid infinite retry loops

3. **Monitor execution**: Set up alerts for sequence failures

### Scheduling

1. **Avoid overlapping executions**: Ensure sequences complete before the next scheduled run
2. **Consider dependencies**: Schedule sequences to run after their data sources are updated
3. **Use appropriate time zones**: The system uses the configured timezone for scheduling

## Troubleshooting

### Common Issues

1. **Sequence won't start**: Check that the sequence is enabled and has jobs assigned
2. **Jobs skip sequence logic**: Ensure jobs are properly assigned to the sequence
3. **Scheduling not working**: Verify cron expression syntax and timezone settings
4. **Sequence stuck**: Check for failed jobs and review failure handling strategy

### Debugging

1. **Check sequence status**: Use the status API endpoint for detailed information
2. **Review execution logs**: Check individual job logs for error details
3. **Verify job assignments**: Ensure jobs have correct sequence_id and order
4. **Test manually**: Execute sequences manually to isolate scheduling issues

## Migration Guide

### Existing Jobs

Existing jobs will continue to work unchanged. They will:
- Run independently based on their individual schedules
- Not be affected by sequence logic
- Maintain all current functionality

### Converting Jobs to Sequences

To convert existing independent jobs to a sequence:

1. Create a new sequence
2. Assign jobs to the sequence using the assignment API
3. Disable individual job schedules (optional)
4. Enable sequence scheduling if needed

The jobs will automatically switch from independent to sequential execution once assigned to a sequence.
