"use client";

// ============================================================================
// IMPORTS
// ============================================================================

// React imports
import React, { useState, useEffect } from "react";

// UI library imports
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Select,
  SelectItem,
  Chip,
  Spinner,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
} from "@heroui/react";

// Icon imports
import {
  Database,
  Table,
  Search,
  RefreshCw,
  Eye,
  Key,
  Hash,
  Type,
  Maximize,
  Minimize,
} from "lucide-react";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface DatabaseConnection {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
}

interface TableInfo {
  name: string;
  type: "TABLE" | "VIEW";
  engine?: string;
  rows?: number;
  columns: ColumnInfo[];
}

interface ColumnInfo {
  name: string;
  type: string;
  nullable: boolean;
  key: "PRI" | "UNI" | "MUL" | "";
  default?: string;
  extra?: string;
}

interface DatabaseSchemaBrowserProps {
  connection: DatabaseConnection;
  onTableSelect?: (table: TableInfo) => void;
  onColumnSelect?: (table: string, column: ColumnInfo) => void;
  selectedTables?: string[];
  className?: string;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export default function DatabaseSchemaBrowser({
  connection,
  onTableSelect,
  onColumnSelect,
  selectedTables = [],
  className = "",
}: DatabaseSchemaBrowserProps) {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [tables, setTables] = useState<TableInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTableType, setSelectedTableType] = useState<string>("all");
  const [expandedTables, setExpandedTables] = useState<Set<string>>(new Set());
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Load database schema
  const loadSchema = async () => {
    setLoading(true);
    try {
      // This would be an API call to get database schema
      // For now, we'll simulate with mock data
      const mockTables: TableInfo[] = [
        {
          name: "pagu_real_detail_harian_2025",
          type: "TABLE",
          engine: "InnoDB",
          rows: 15420,
          columns: [
            {
              name: "id",
              type: "bigint(20)",
              nullable: false,
              key: "PRI",
              extra: "auto_increment",
            },
            {
              name: "kddept",
              type: "varchar(10)",
              nullable: false,
              key: "MUL",
            },
            { name: "pagu", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "real1", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "real2", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "real3", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "real4", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "real5", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "real6", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "real7", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "real8", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "real9", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "real10", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "real11", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "real12", type: "decimal(15,2)", nullable: true, key: "" },
            { name: "blokir", type: "decimal(15,2)", nullable: true, key: "" },
            {
              name: "created_at",
              type: "timestamp",
              nullable: false,
              key: "",
              default: "CURRENT_TIMESTAMP",
            },
            {
              name: "updated_at",
              type: "timestamp",
              nullable: false,
              key: "",
              default: "CURRENT_TIMESTAMP",
            },
          ],
        },
        {
          name: "t_dept_2025",
          type: "TABLE",
          engine: "InnoDB",
          rows: 156,
          columns: [
            {
              name: "id",
              type: "int(11)",
              nullable: false,
              key: "PRI",
              extra: "auto_increment",
            },
            {
              name: "kddept",
              type: "varchar(10)",
              nullable: false,
              key: "UNI",
            },
            { name: "nmdept", type: "varchar(255)", nullable: false, key: "" },
            {
              name: "parent_dept",
              type: "varchar(10)",
              nullable: true,
              key: "MUL",
            },
            { name: "level", type: "int(11)", nullable: false, key: "" },
            {
              name: "active",
              type: "tinyint(1)",
              nullable: false,
              key: "",
              default: "1",
            },
            {
              name: "created_at",
              type: "timestamp",
              nullable: false,
              key: "",
              default: "CURRENT_TIMESTAMP",
            },
            {
              name: "updated_at",
              type: "timestamp",
              nullable: false,
              key: "",
              default: "CURRENT_TIMESTAMP",
            },
          ],
        },
        {
          name: "TEST_DROP_CREATE",
          type: "TABLE",
          engine: "InnoDB",
          rows: 156,
          columns: [
            { name: "kddept", type: "varchar(10)", nullable: true, key: "" },
            { name: "nmdept", type: "varchar(255)", nullable: true, key: "" },
            {
              name: "PAGU_DIPA",
              type: "decimal(15,0)",
              nullable: true,
              key: "",
            },
            {
              name: "REALISASI",
              type: "decimal(15,0)",
              nullable: true,
              key: "",
            },
            { name: "BLOKIR", type: "decimal(15,0)", nullable: true, key: "" },
          ],
        },
      ];

      setTables(mockTables);
    } catch (error) {
      console.error("Failed to load database schema:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (connection.host && connection.database) {
      loadSchema();
    }
  }, [connection]);

  // Filter tables based on search and type
  const filteredTables = tables.filter((table) => {
    const matchesSearch = table.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesType =
      selectedTableType === "all" || table.type === selectedTableType;
    return matchesSearch && matchesType;
  });

  // Toggle table expansion
  const toggleTableExpansion = (tableName: string) => {
    const newExpanded = new Set(expandedTables);
    if (newExpanded.has(tableName)) {
      newExpanded.delete(tableName);
    } else {
      newExpanded.add(tableName);
    }
    setExpandedTables(newExpanded);
  };

  // Get icon for column type
  const getColumnIcon = (column: ColumnInfo) => {
    if (column.key === "PRI")
      return <Key className="w-4 h-4 text-yellow-500" />;
    if (column.key === "UNI") return <Key className="w-4 h-4 text-blue-500" />;
    if (column.key === "MUL")
      return <Hash className="w-4 h-4 text-green-500" />;
    return <Type className="w-4 h-4 text-gray-400" />;
  };

  const renderContent = () => (
    <Card className={`w-full ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          <h3 className="text-lg font-semibold">Database Schema</h3>
          <Chip size="sm" variant="flat" color="primary">
            {connection.database}
          </Chip>
        </div>
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="ghost"
            isIconOnly
            onPress={loadSchema}
            isLoading={loading}
          >
            <RefreshCw className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            variant="flat"
            isIconOnly
            startContent={
              isFullscreen ? (
                <Minimize className="w-4 h-4" />
              ) : (
                <Maximize className="w-4 h-4" />
              )
            }
            onPress={() => setIsFullscreen(!isFullscreen)}
            title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
          />
        </div>
      </CardHeader>

      <CardBody className="space-y-4">
        {/* Search and Filter Controls */}
        <div className="flex gap-2">
          <Input
            placeholder="Search tables..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            startContent={<Search className="w-4 h-4" />}
            size="sm"
            className="flex-1"
          />
          <Select
            placeholder="Type"
            selectedKeys={[selectedTableType]}
            onSelectionChange={(keys) =>
              setSelectedTableType(Array.from(keys)[0] as string)
            }
            size="sm"
            className="w-32"
          >
            <SelectItem key="all">All</SelectItem>
            <SelectItem key="TABLE">Tables</SelectItem>
            <SelectItem key="VIEW">Views</SelectItem>
          </Select>
        </div>

        {/* Tables List */}
        {loading ? (
          <div className="flex justify-center py-8">
            <Spinner size="lg" />
          </div>
        ) : (
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {filteredTables.map((table) => (
              <Card
                key={table.name}
                className={`cursor-pointer transition-colors ${
                  selectedTables.includes(table.name)
                    ? "border-primary bg-primary/5"
                    : "hover:bg-gray-50"
                }`}
                isPressable
                onPress={() => {
                  onTableSelect?.(table);
                  toggleTableExpansion(table.name);
                }}
              >
                <CardBody className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Table className="w-4 h-4" />
                      <span className="font-medium">{table.name}</span>
                      <Chip
                        size="sm"
                        variant="flat"
                        color={table.type === "TABLE" ? "primary" : "secondary"}
                      >
                        {table.type}
                      </Chip>
                      {table.rows && (
                        <Chip size="sm" variant="flat" color="default">
                          {table.rows.toLocaleString()} rows
                        </Chip>
                      )}
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      isIconOnly
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleTableExpansion(table.name);
                      }}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* Columns (when expanded) */}
                  {expandedTables.has(table.name) && (
                    <div className="mt-3 space-y-1">
                      <div className="text-sm font-medium text-gray-600">
                        Columns:
                      </div>
                      <div className="grid gap-1">
                        {table.columns.map((column) => (
                          <div
                            key={column.name}
                            className="flex items-center gap-2 p-2 rounded bg-gray-50 hover:bg-gray-100 cursor-pointer text-sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              onColumnSelect?.(table.name, column);
                            }}
                          >
                            {getColumnIcon(column)}
                            <span className="font-medium">{column.name}</span>
                            <span className="text-gray-500">{column.type}</span>
                            {!column.nullable && (
                              <Chip size="sm" variant="flat" color="warning">
                                NOT NULL
                              </Chip>
                            )}
                            {column.default && (
                              <Chip size="sm" variant="flat" color="default">
                                {column.default}
                              </Chip>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardBody>
              </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && filteredTables.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Database className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>No tables found</p>
            {searchTerm && (
              <p className="text-sm">Try adjusting your search criteria</p>
            )}
          </div>
        )}
      </CardBody>
    </Card>
  );

  if (isFullscreen) {
    return (
      <Modal
        isOpen={isFullscreen}
        onClose={() => setIsFullscreen(false)}
        size="full"
        scrollBehavior="inside"
        backdrop="blur"
        hideCloseButton
        classNames={{
          base: "max-h-screen",
          body: "min-h-[calc(100vh-100px)] max-h-[calc(100vh-100px)] overflow-y-auto p-0",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              <span>Database Schema Browser - Fullscreen</span>
              <Chip size="sm" variant="flat" color="primary">
                {connection.database}
              </Chip>
            </div>
            <Button
              variant="flat"
              size="sm"
              isIconOnly
              startContent={<Minimize className="w-4 h-4" />}
              onPress={() => setIsFullscreen(false)}
              title="Exit Fullscreen"
            />
          </ModalHeader>
          <ModalBody>{renderContent()}</ModalBody>
        </ModalContent>
      </Modal>
    );
  }

  return renderContent();
}
