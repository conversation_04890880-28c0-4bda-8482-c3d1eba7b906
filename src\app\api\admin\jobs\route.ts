import { NextRequest, NextResponse } from "next/server";
import {
  saveJobDefinition,
  loadJobDefinition,
  loadJobDefinitions,
} from "@/lib/jobPersistence";
import { JobDefinition } from "@/lib/jobManager";
import { logger } from "@/lib/jobManager";
import { executeTransaction } from "@/lib/database";
import mysql from "mysql2/promise";

// Interface for batch job update request
interface BatchJobUpdateRequest {
  jobs: JobDefinition[];
}

// Helper function to save job definition within a transaction
async function saveJobDefinitionInTransaction(
  connection: mysql.PoolConnection,
  job: JobDefinition
): Promise<void> {
  const query = `
    INSERT INTO job_definitions (
      id, name, description, schedule_cron, enabled,
      data_source_type, data_source_config, destination_config, retry_config,
      sequence_id, sequence_order
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
      name = VALUES(name),
      description = VALUES(description),
      schedule_cron = VALUES(schedule_cron),
      enabled = VALUES(enabled),
      data_source_type = VALUES(data_source_type),
      data_source_config = VALUES(data_source_config),
      destination_config = VALUES(destination_config),
      retry_config = VALUES(retry_config),
      sequence_id = VALUES(sequence_id),
      sequence_order = VALUES(sequence_order),
      updated_at = CURRENT_TIMESTAMP
  `;

  const params = [
    job.id,
    job.name,
    job.description,
    job.schedule,
    job.enabled,
    job.dataSource.type,
    JSON.stringify(job.dataSource),
    JSON.stringify(job.destination),
    JSON.stringify(job.retryConfig),
    job.sequenceConfig?.sequenceId || null,
    job.sequenceConfig?.order || null,
  ];

  await connection.execute(query, params);
}

// GET /api/admin/jobs - Get all job definitions for editing
// GET /api/admin/jobs/[id] - Get specific job definition
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get("id");

    if (jobId) {
      // Get specific job definition
      const job = await loadJobDefinition(jobId);
      if (!job) {
        return NextResponse.json(
          { error: `Job definition not found: ${jobId}` },
          { status: 404 }
        );
      }
      return NextResponse.json({ job });
    } else {
      // Get all job definitions
      const jobs = await loadJobDefinitions();
      return NextResponse.json({ jobs });
    }
  } catch (error) {
    logger.error("Error fetching job definitions:", error);
    return NextResponse.json(
      { error: "Failed to fetch job definitions" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/jobs - Update job definition
export async function PUT(request: NextRequest) {
  try {
    const updatedJob: JobDefinition = await request.json();

    // Validate required fields
    if (!updatedJob.id || !updatedJob.name || !updatedJob.schedule) {
      return NextResponse.json(
        { error: "Missing required fields: id, name, schedule" },
        { status: 400 }
      );
    }

    // Validate job ID exists
    const existingJob = await loadJobDefinition(updatedJob.id);
    if (!existingJob) {
      return NextResponse.json(
        { error: `Job definition not found: ${updatedJob.id}` },
        { status: 404 }
      );
    }

    // Save the updated job definition
    await saveJobDefinition(updatedJob);

    logger.info(
      `Job definition updated via admin API: ${updatedJob.id} - ${updatedJob.name}`
    );

    // Update the cron scheduler if the schedule changed
    try {
      const { cronScheduler } = await import("@/lib/cronScheduler");
      if (existingJob.schedule !== updatedJob.schedule) {
        logger.info(
          `Rescheduling job ${updatedJob.id} from "${existingJob.schedule}" to "${updatedJob.schedule}"`
        );
        cronScheduler.rescheduleJob(
          updatedJob.id,
          updatedJob.schedule,
          updatedJob.name
        );
      }
    } catch (error) {
      logger.error("Could not update cron scheduler", { error });
    }

    // Broadcast the update to all connected clients
    try {
      const { broadcastJobUpdate } = await import("@/lib/sseManager");
      await broadcastJobUpdate();
    } catch (error) {
      logger.debug("Could not broadcast job update", { error });
    }

    return NextResponse.json({
      message: "Job definition updated successfully",
      jobId: updatedJob.id,
    });
  } catch (error) {
    logger.error("Error updating job definition:", error);
    return NextResponse.json(
      { error: "Failed to update job definition" },
      { status: 500 }
    );
  }
}

// POST /api/admin/jobs - Create new job definition
export async function POST(request: NextRequest) {
  try {
    const newJob: JobDefinition = await request.json();

    // Validate required fields
    if (!newJob.id || !newJob.name || !newJob.schedule) {
      return NextResponse.json(
        { error: "Missing required fields: id, name, schedule" },
        { status: 400 }
      );
    }

    // Check if job ID already exists
    const existingJob = await loadJobDefinition(newJob.id);
    if (existingJob) {
      return NextResponse.json(
        { error: `Job definition already exists: ${newJob.id}` },
        { status: 409 }
      );
    }

    // Save the new job definition
    await saveJobDefinition(newJob);

    logger.info(
      `New job definition created via admin API: ${newJob.id} - ${newJob.name}`
    );

    // Broadcast the update to all connected clients
    try {
      const { broadcastJobUpdate } = await import("@/lib/sseManager");
      await broadcastJobUpdate();
    } catch (error) {
      logger.debug("Could not broadcast job update", { error });
    }

    return NextResponse.json(
      {
        message: "Job definition created successfully",
        jobId: newJob.id,
      },
      { status: 201 }
    );
  } catch (error) {
    logger.error("Error creating job definition:", error);
    return NextResponse.json(
      { error: "Failed to create job definition" },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/jobs - Batch update multiple job definitions
export async function PATCH(request: NextRequest) {
  try {
    const { jobs }: BatchJobUpdateRequest = await request.json();

    // Validate request structure
    if (!Array.isArray(jobs) || jobs.length === 0) {
      return NextResponse.json(
        { error: "Request must contain an array of jobs" },
        { status: 400 }
      );
    }

    // Validate each job in the batch
    const validationErrors: string[] = [];
    const existingJobs: Record<string, JobDefinition> = {};

    for (let i = 0; i < jobs.length; i++) {
      const job = jobs[i];
      const jobIndex = `jobs[${i}]`;

      // Validate required fields
      if (!job.id || !job.name || !job.schedule) {
        validationErrors.push(
          `${jobIndex}: Missing required fields: id, name, schedule`
        );
        continue;
      }

      // Check if job exists
      try {
        const existingJob = await loadJobDefinition(job.id);
        if (!existingJob) {
          validationErrors.push(
            `${jobIndex}: Job definition not found: ${job.id}`
          );
        } else {
          existingJobs[job.id] = existingJob;
        }
      } catch (error) {
        validationErrors.push(
          `${jobIndex}: Error loading job ${job.id}: ${error}`
        );
      }
    }

    // Return validation errors if any
    if (validationErrors.length > 0) {
      return NextResponse.json(
        {
          error: "Validation failed",
          details: validationErrors,
        },
        { status: 400 }
      );
    }

    // Track jobs that need scheduler updates
    const schedulerUpdates: Array<{
      jobId: string;
      oldSchedule: string;
      newSchedule: string;
      jobName: string;
    }> = [];

    // Process all job updates in a transaction for atomicity
    const updateResults: Array<{
      jobId: string;
      success: boolean;
      error?: string;
    }> = [];

    try {
      await executeTransaction(async (connection) => {
        // Process all jobs within the transaction
        for (const job of jobs) {
          try {
            // Save the updated job definition within transaction
            await saveJobDefinitionInTransaction(connection, job);

            // Track scheduler updates needed
            const existingJob = existingJobs[job.id];
            if (existingJob.schedule !== job.schedule) {
              schedulerUpdates.push({
                jobId: job.id,
                oldSchedule: existingJob.schedule,
                newSchedule: job.schedule,
                jobName: job.name,
              });
            }

            updateResults.push({ jobId: job.id, success: true });
            logger.info(
              `Job definition updated via batch API: ${job.id} - ${job.name}`
            );
          } catch (error) {
            // If any job fails, the entire transaction will be rolled back
            const errorMessage =
              error instanceof Error ? error.message : String(error);
            logger.error(
              `Failed to update job ${job.id} in batch transaction:`,
              error
            );
            throw new Error(`Job ${job.id} update failed: ${errorMessage}`);
          }
        }
      });

      // If we get here, all jobs were updated successfully
      logger.info(
        `Batch job update transaction completed successfully for ${jobs.length} jobs`
      );
    } catch (transactionError) {
      // Transaction was rolled back, mark all jobs as failed
      updateResults.length = 0; // Clear any partial results
      for (const job of jobs) {
        updateResults.push({
          jobId: job.id,
          success: false,
          error:
            transactionError instanceof Error
              ? transactionError.message
              : String(transactionError),
        });
      }
      logger.error(
        "Batch job update transaction failed and was rolled back:",
        transactionError
      );
    }

    // Update cron scheduler for jobs with schedule changes
    if (schedulerUpdates.length > 0) {
      try {
        const { cronScheduler } = await import("@/lib/cronScheduler");
        for (const update of schedulerUpdates) {
          try {
            logger.info(
              `Rescheduling job ${update.jobId} from "${update.oldSchedule}" to "${update.newSchedule}"`
            );
            cronScheduler.rescheduleJob(
              update.jobId,
              update.newSchedule,
              update.jobName
            );
          } catch (error) {
            logger.error(`Could not reschedule job ${update.jobId}:`, error);
          }
        }
      } catch (error) {
        logger.error("Could not load cron scheduler for batch updates:", error);
      }
    }

    // Broadcast the update to all connected clients
    try {
      const { broadcastJobUpdate } = await import("@/lib/sseManager");
      await broadcastJobUpdate();
    } catch (error) {
      logger.debug("Could not broadcast job update after batch:", error);
    }

    // Check if all updates were successful (with atomic transaction, it's all or nothing)
    const failedUpdates = updateResults.filter((result) => !result.success);
    const successfulUpdates = updateResults.filter((result) => result.success);

    if (failedUpdates.length === 0) {
      return NextResponse.json({
        message: `Successfully updated ${successfulUpdates.length} job definitions atomically`,
        results: updateResults,
        schedulerUpdates: schedulerUpdates.length,
        atomic: true,
      });
    } else {
      // With atomic transactions, if any job fails, all fail
      return NextResponse.json(
        {
          message: `Batch update failed - all changes rolled back due to transaction failure`,
          results: updateResults,
          schedulerUpdates: 0, // No scheduler updates if transaction failed
          atomic: true,
          rollback: true,
        },
        { status: 400 } // Bad Request instead of Multi-Status since it's atomic
      );
    }
  } catch (error) {
    logger.error("Error in batch job update:", error);
    return NextResponse.json(
      { error: "Failed to process batch job update" },
      { status: 500 }
    );
  }
}
