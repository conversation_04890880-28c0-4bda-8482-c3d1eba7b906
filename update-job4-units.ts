// Script to update job 4 organizational units
import { saveJobDefinition, loadJobDefinition } from "./src/lib/jobPersistence";
import { initializeDatabase } from "./src/lib/database";

async function updateJob4Units() {
  try {
    console.log("Initializing database...");
    await initializeDatabase();

    console.log("Loading current job 4 definition...");
    const job4 = await loadJobDefinition("4");

    if (!job4) {
      console.error("Job 4 not found in database");
      process.exit(1);
    }

    console.log(
      "Current organizational units:",
      job4.dataSource.options?.organisationalUnits || []
    );

    // Update with the new organizational units from your code
    const newOrganizationalUnits = [
      "341522024", // Only this one unit as shown in your selection
      // Add any other units you want here
    ];

    // Update the job definition
    job4.dataSource.options = {
      ...job4.dataSource.options,
      organisationalUnits: newOrganizationalUnits,
    };

    console.log("Updating job 4 with new organizational units...");
    await saveJobDefinition(job4);

    console.log("Job 4 updated successfully!");
    console.log("New organizational units:", newOrganizationalUnits);

    process.exit(0);
  } catch (error) {
    console.error("Error:", error);
    process.exit(1);
  }
}

updateJob4Units();
