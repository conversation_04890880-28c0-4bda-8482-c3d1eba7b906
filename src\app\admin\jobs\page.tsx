"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from "@heroui/react";
import { Settings, Database, Plus } from "lucide-react";
import { JobAdminModal as JobAdminModalEnhanced } from "@/components";

export default function JobAdminPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Database className="w-8 h-8 text-blue-600" />
              Job Administration
            </h1>
            <p className="text-gray-600 mt-2">
              Manage your data pulling jobs with advanced CRUD operations and
              cron scheduling
            </p>
          </div>
          <Button
            color="primary"
            size="lg"
            startContent={<Settings className="w-5 h-5" />}
            onPress={() => setIsModalOpen(true)}
          >
            Open Job Manager
          </Button>
        </div>

        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Plus className="w-5 h-5 text-green-600" />
                <h3 className="font-semibold">Create Jobs</h3>
              </div>
            </CardHeader>
            <CardBody>
              <p className="text-sm text-gray-600">
                Create new data pulling jobs with Oracle database or SFTP
                sources. Configure destinations, schedules, and retry policies.
              </p>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Settings className="w-5 h-5 text-blue-600" />
                <h3 className="font-semibold">Advanced Scheduling</h3>
              </div>
            </CardHeader>
            <CardBody>
              <p className="text-sm text-gray-600">
                Use the built-in cron expression builder with presets or create
                custom schedules. Preview next run times and validate
                expressions.
              </p>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Database className="w-5 h-5 text-purple-600" />
                <h3 className="font-semibold">Full CRUD Operations</h3>
              </div>
            </CardHeader>
            <CardBody>
              <p className="text-sm text-gray-600">
                Complete Create, Read, Update, Delete operations for job
                definitions. Duplicate jobs, manage configurations, and maintain
                job lifecycle.
              </p>
            </CardBody>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Quick Actions</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-3">
                <Button
                  color="primary"
                  variant="flat"
                  onPress={() => setIsModalOpen(true)}
                >
                  View All Jobs
                </Button>
                <Button
                  color="success"
                  variant="flat"
                  onPress={() => setIsModalOpen(true)}
                >
                  Create New Job
                </Button>
                <Button
                  color="warning"
                  variant="flat"
                  onPress={() => setIsModalOpen(true)}
                >
                  Manage Schedules
                </Button>
              </div>

              <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded">
                <strong>Features Available:</strong>
                <ul className="mt-2 space-y-1 list-disc list-inside">
                  <li>Visual cron expression builder with common presets</li>
                  <li>Oracle database and SFTP data source configuration</li>
                  <li>Database and local file system destinations</li>
                  <li>Retry configuration with customizable delays</li>
                  <li>Job duplication and template creation</li>
                  <li>Real-time validation and next run preview</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Cron Examples */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Common Cron Patterns</h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                    0 2 * * *
                  </span>
                  <span className="text-gray-600">Daily at 02:00</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                    */15 * * * *
                  </span>
                  <span className="text-gray-600">Every 15 minutes</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                    0 */6 * * *
                  </span>
                  <span className="text-gray-600">Every 6 hours</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                    0 9 * * 1-5
                  </span>
                  <span className="text-gray-600">Weekdays at 09:00</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                    0 0 1 * *
                  </span>
                  <span className="text-gray-600">Monthly (1st day)</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                    0 0 * * 0
                  </span>
                  <span className="text-gray-600">Weekly (Sunday)</span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Enhanced Job Administration Modal */}
      <JobAdminModalEnhanced
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
}
