/**
 * Test script to verify job cleanup fix by calling the API
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function testJobCleanupViaAPI() {
  console.log('🧪 Testing job cleanup fix via API...');
  
  try {
    // First attempt - run job 4 (should fail due to SFTP connection)
    console.log('🚀 First attempt: Running job 4...');
    
    try {
      const response1 = await axios.post(`${API_BASE}/jobs`, {
        jobId: '4',
        action: 'run'
      });
      console.log('📊 First attempt response:', response1.status, response1.data);
    } catch (error) {
      console.log('📊 First attempt failed as expected:', error.response?.status, error.response?.data);
    }
    
    // Wait a moment for the job to fail and cleanup to complete
    console.log('⏳ Waiting 3 seconds for job to fail and cleanup to complete...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Second attempt - should not be blocked by "already running"
    console.log('🔄 Second attempt: Running job 4 again...');
    
    try {
      const response2 = await axios.post(`${API_BASE}/jobs`, {
        jobId: '4',
        action: 'run'
      });
      console.log('📊 Second attempt response:', response2.status, response2.data);
      
      if (response2.data && !response2.data.error?.includes('already running')) {
        console.log('✅ SUCCESS: Second attempt was not blocked by "already running" error!');
      } else {
        console.log('❌ FAILURE: Second attempt was blocked by "already running" error');
      }
    } catch (error) {
      const errorMsg = error.response?.data?.error || error.message;
      if (errorMsg.includes('already running')) {
        console.log('❌ FAILURE: Second attempt was blocked by "already running" error:', errorMsg);
      } else {
        console.log('✅ SUCCESS: Second attempt failed for different reason (not "already running"):', errorMsg);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testJobCleanupViaAPI().then(() => {
  console.log('🏁 Test completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test crashed:', error);
  process.exit(1);
});
