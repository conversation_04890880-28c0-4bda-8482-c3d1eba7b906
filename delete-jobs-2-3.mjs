#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to delete jobs 2 and 3 from the database
 * Run this script to remove the old pre-configured jobs from the database
 */

import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function deleteJobsDirectly() {
  let connection;
  
  try {
    console.log('🚀 Starting direct job deletion process...');
    
    // Create database connection
    console.log('📊 Connecting to database...');
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3399'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'pulltest',
    });
    
    console.log('✅ Connected to database');
    
    // Start transaction
    await connection.beginTransaction();
    
    try {
      // Delete jobs 2 and 3
      const jobIds = ['2', '3'];
      
      for (const jobId of jobIds) {
        console.log(`🗑️  Deleting job ${jobId}...`);
        
        // Check if job exists
        const [jobRows] = await connection.execute(
          'SELECT id, name FROM job_definitions WHERE id = ?',
          [jobId]
        );
        
        if (jobRows.length === 0) {
          console.log(`⚠️  Job ${jobId} not found, skipping`);
          continue;
        }
        
        const jobName = jobRows[0].name;
        console.log(`📋 Found job ${jobId}: ${jobName}`);
        
        // Delete job execution logs first
        const [logsResult] = await connection.execute(
          'DELETE jel FROM job_execution_logs jel JOIN job_executions je ON jel.execution_id = je.id WHERE je.job_id = ?',
          [jobId]
        );
        console.log(`   ✓ Deleted ${logsResult.affectedRows} execution logs`);
        
        // Delete job executions
        const [executionsResult] = await connection.execute(
          'DELETE FROM job_executions WHERE job_id = ?',
          [jobId]
        );
        console.log(`   ✓ Deleted ${executionsResult.affectedRows} executions`);
        
        // Delete schedule history
        const [scheduleResult] = await connection.execute(
          'DELETE FROM job_schedule_history WHERE job_id = ?',
          [jobId]
        );
        console.log(`   ✓ Deleted ${scheduleResult.affectedRows} schedule entries`);
        
        // Delete SFTP file transfers if any
        const [sftpResult] = await connection.execute(
          'DELETE FROM sftp_file_transfers WHERE job_id = ?',
          [jobId]
        );
        console.log(`   ✓ Deleted ${sftpResult.affectedRows} SFTP transfer records`);
        
        // Delete job definition
        const [jobResult] = await connection.execute(
          'DELETE FROM job_definitions WHERE id = ?',
          [jobId]
        );
        
        if (jobResult.affectedRows > 0) {
          console.log(`   ✅ Successfully deleted job ${jobId}: ${jobName}`);
        } else {
          console.log(`   ⚠️  Job ${jobId} was not deleted (may have been removed already)`);
        }
      }
      
      // Commit transaction
      await connection.commit();
      console.log('✅ Transaction committed successfully');
      
      console.log('🎉 Successfully deleted jobs 2 and 3 from the database!');
      console.log('📝 Note: The pre-configured job definitions in the code have already been removed.');
      
    } catch (error) {
      // Rollback transaction on error
      await connection.rollback();
      throw error;
    }
    
  } catch (error) {
    console.error('❌ Error deleting jobs:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('📊 Database connection closed');
    }
    process.exit(0);
  }
}

// Run the script
deleteJobsDirectly();
