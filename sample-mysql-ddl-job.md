# Sample MySQL DDL Job Configuration

This document shows how to configure your DROP/CREATE table procedure as a MySQL job in the UI.

## Job Configuration

### Basic Information
- **Job Name**: `Create TEST_DROP_CREATE Table`
- **Description**: `Drop and recreate TEST_DROP_CREATE table with aggregated data from pagu_real_detail_harian_2025`
- **Schedule**: `0 3 * * *` (Daily at 3 AM)
- **Enabled**: `true`

### Data Source Configuration
- **Type**: `MySQL Database`
- **Host**: `your-mysql-host` (e.g., `localhost` or your server IP)
- **Port**: `3306`
- **Database**: `monev2025`
- **Username**: `your-username`
- **Password**: `your-password`
- **Connection Limit**: `5`
- **Acquire Timeout**: `60000`
- **Query Timeout**: `120000` (2 minutes for DDL operations)

### SQL Query
```sql
SET @table_name = 'TEST_DROP_CREATE';
SET @db_name = 'monev2025';

SELECT COUNT(*) INTO @exists FROM information_schema.tables 
WHERE table_schema = 'monev2025' 
AND table_name = 'TEST_DROP_CREATE';

DROP TABLE IF EXISTS monev2025.TEST_DROP_CREATE;

CREATE TABLE monev2025.TEST_DROP_CREATE AS
SELECT 
    a.kddept,
    b.nmdept,
    ROUND(SUM(a.pagu)/1, 0) AS PAGU_DIPA,
    ROUND(SUM(real1 + real2 + real3 + real4 + real5 + real6 + real7 + real8 + real9 + real10 + real11 + real12) / 1, 0) AS REALISASI,
    ROUND(SUM(a.blokir) / 1, 0) AS BLOKIR
FROM 
    monev2025.pagu_real_detail_harian_2025 a
LEFT JOIN 
    dbref.t_dept_2025 b ON a.kddept = b.kddept
GROUP BY 
    a.kddept;
```

### Destination Configuration
- **Type**: `Local File System`
- **Local Path**: `./output/ddl-results.json`
- **Options**: 
  - Create Directory: `true`
  - Preserve Structure: `true`

### Retry Configuration
- **Max Retries**: `2`
- **Retry Delay**: `60` seconds

## Simplified Version (Recommended)

For better reliability, here's a simplified version without dynamic SQL:

```sql
-- Drop table if exists
DROP TABLE IF EXISTS monev2025.TEST_DROP_CREATE;

-- Create the new table
CREATE TABLE monev2025.TEST_DROP_CREATE AS
SELECT 
    a.kddept,
    b.nmdept,
    ROUND(SUM(a.pagu)/1, 0) AS PAGU_DIPA,
    ROUND(SUM(real1 + real2 + real3 + real4 + real5 + real6 + real7 + real8 + real9 + real10 + real11 + real12) / 1, 0) AS REALISASI,
    ROUND(SUM(a.blokir) / 1, 0) AS BLOKIR
FROM 
    monev2025.pagu_real_detail_harian_2025 a
LEFT JOIN 
    dbref.t_dept_2025 b ON a.kddept = b.kddept
GROUP BY 
    a.kddept;
```

## How to Create This Job in the UI

1. **Open the Job Admin Modal**
   - Click the "Create New Job" button
   - Or click the "+" icon in the job management interface

2. **Fill Basic Information**
   - Enter the job name and description
   - Set the schedule using the cron expression builder
   - Enable the job

3. **Configure Data Source**
   - Select "MySQL Database" from the dropdown
   - Fill in your MySQL connection details
   - Paste the SQL query in the query field

4. **Configure Destination**
   - Select "Local File System"
   - Set the output path for results

5. **Set Retry Configuration**
   - Configure retry attempts and delay

6. **Save and Test**
   - Save the job configuration
   - Run a manual test to verify it works

## Expected Results

When the job runs successfully, you'll see:

1. **Log Messages**:
   ```
   Connected to MySQL database successfully
   Executing SQL query...
   Executing DDL/administrative operations...
   Executing statement 1/2...
   Executing statement 2/2...
   DDL operations completed successfully. Executed 2 statements
   Data saved to destination successfully
   ```

2. **Output File**: A JSON file containing:
   ```json
   {
     "type": "DDL",
     "statements": 2,
     "results": [...],
     "message": "DDL operations completed successfully"
   }
   ```

3. **Database**: The `TEST_DROP_CREATE` table will be created/updated with the aggregated data

## Security Considerations

⚠️ **Important**: DDL operations are powerful and can modify your database structure. Make sure:

1. **Test First**: Always test on a development database first
2. **Backup**: Ensure you have database backups before running DDL jobs
3. **Permissions**: Use a database user with appropriate permissions
4. **Monitoring**: Monitor the job execution logs carefully
5. **Schedule Carefully**: Consider the impact on database performance

## Troubleshooting

### Common Issues:

1. **Permission Denied**: Ensure your MySQL user has CREATE/DROP privileges
2. **Table Locked**: Make sure no other processes are using the table
3. **Timeout**: Increase the query timeout for large operations
4. **Connection Issues**: Verify MySQL connection details

### Debug Steps:

1. Test the SQL manually in MySQL Workbench or command line
2. Check the job execution logs for detailed error messages
3. Verify database permissions and connectivity
4. Start with a simpler version and gradually add complexity
