import React from "react";
import { Card, CardBody, Progress } from "@heroui/react";
import {
  Archive,
  FileText,
  Database,
  CheckCircle,
  ArrowRight,
  Trash2,
} from "lucide-react";

interface AdkWorkflowVisualizationProps {
  className?: string;
}

export const AdkWorkflowVisualization: React.FC<
  AdkWorkflowVisualizationProps
> = ({ className = "" }) => {
  const workflowSteps = [
    {
      id: 1,
      title: "Archive Discovery",
      description: "Scan source directory for .s25/.s2504/.s2507 files",
      icon: <Archive className="w-5 h-5" />,
      color: "blue",
      details: ["Apply file prefix filters", "Exclude specified extensions"],
    },
    {
      id: 2,
      title: "Extraction",
      description: "Extract XML files using RAR tool",
      icon: <FileText className="w-5 h-5" />,
      color: "purple",
      details: ["Clean extraction directory", "Extract ~27 XML files per archive"],
    },
    {
      id: 3,
      title: "Multi-Table Processing",
      description: "Route XML data to appropriate database tables",
      icon: <Database className="w-5 h-5" />,
      color: "green",
      details: ["Parse XML structure", "Route to 14 target tables", "Dynamic schema adaptation"],
    },
    {
      id: 4,
      title: "Cleanup & Status",
      description: "Clean temporary files and update tracking",
      icon: <CheckCircle className="w-5 h-5" />,
      color: "emerald",
      details: ["Remove extracted XML files", "Update file_metadata status"],
    },
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: {
        bg: "bg-blue-50",
        border: "border-blue-200",
        icon: "text-blue-600",
        text: "text-blue-700",
      },
      purple: {
        bg: "bg-purple-50",
        border: "border-purple-200",
        icon: "text-purple-600",
        text: "text-purple-700",
      },
      green: {
        bg: "bg-green-50",
        border: "border-green-200",
        icon: "text-green-600",
        text: "text-green-700",
      },
      emerald: {
        bg: "bg-emerald-50",
        border: "border-emerald-200",
        icon: "text-emerald-600",
        text: "text-emerald-700",
      },
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h4 className="text-lg font-semibold text-gray-900 mb-2">
          ADK Processing Workflow
        </h4>
        <p className="text-sm text-gray-600">
          Automated multi-step processing pipeline for ADK archives
        </p>
      </div>

      {/* Workflow Steps */}
      <div className="relative">
        {/* Progress Line */}
        <div className="absolute left-8 top-12 bottom-12 w-0.5 bg-gray-200"></div>

        <div className="space-y-6">
          {workflowSteps.map((step, index) => {
            const colors = getColorClasses(step.color);
            const isLast = index === workflowSteps.length - 1;

            return (
              <div key={step.id} className="relative flex items-start gap-4">
                {/* Step Icon */}
                <div
                  className={`relative z-10 w-16 h-16 rounded-full ${colors.bg} ${colors.border} border-2 flex items-center justify-center flex-shrink-0`}
                >
                  <div className={colors.icon}>{step.icon}</div>
                </div>

                {/* Step Content */}
                <div className="flex-1 min-w-0">
                  <Card className={`${colors.bg} ${colors.border} border`}>
                    <CardBody className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h5 className={`font-semibold ${colors.text}`}>
                            Step {step.id}: {step.title}
                          </h5>
                          <p className="text-sm text-gray-600 mt-1">
                            {step.description}
                          </p>
                        </div>
                        {!isLast && (
                          <ArrowRight className="w-4 h-4 text-gray-400 mt-1" />
                        )}
                      </div>

                      {/* Step Details */}
                      <div className="mt-3">
                        <ul className="text-xs text-gray-600 space-y-1">
                          {step.details.map((detail, detailIndex) => (
                            <li key={detailIndex} className="flex items-center gap-2">
                              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                              {detail}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardBody>
                  </Card>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Performance Metrics */}
      <Card className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200">
        <CardBody className="p-6">
          <h5 className="font-semibold text-gray-800 mb-4">
            📊 Expected Performance Metrics
          </h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">~173</div>
              <div className="text-xs text-gray-600">Files per batch</div>
              <Progress
                value={100}
                color="primary"
                size="sm"
                className="mt-2"
              />
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">~67K</div>
              <div className="text-xs text-gray-600">Records processed</div>
              <Progress
                value={100}
                color="success"
                size="sm"
                className="mt-2"
              />
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">~0.5s</div>
              <div className="text-xs text-gray-600">Per file average</div>
              <Progress
                value={100}
                color="secondary"
                size="sm"
                className="mt-2"
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Error Handling Info */}
      <Card className="bg-yellow-50 border border-yellow-200">
        <CardBody className="p-4">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 rounded-full bg-yellow-100 flex items-center justify-center flex-shrink-0 mt-0.5">
              <Trash2 className="w-3 h-3 text-yellow-600" />
            </div>
            <div>
              <h6 className="font-semibold text-yellow-800 mb-2">
                🛡️ Built-in Error Handling
              </h6>
              <ul className="text-xs text-yellow-700 space-y-1">
                <li>• <strong>Continue on Error:</strong> Processing continues even if individual files fail</li>
                <li>• <strong>Automatic Cleanup:</strong> Temporary files are always cleaned up</li>
                <li>• <strong>Schema Adaptation:</strong> Handles varying XML structures gracefully</li>
                <li>• <strong>Status Tracking:</strong> File processing status is always updated</li>
              </ul>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};
