// Fix ADK Processing Job - Add missing destination configuration
import { initializeDatabase } from "./src/lib/database";
import { loadJobDefinition, saveJobDefinition } from "./src/lib/jobPersistence";

async function fixAdkJobDestination() {
  try {
    console.log("🔧 Fixing ADK Processing Job Destination Configuration...\n");

    console.log("Initializing database...");
    await initializeDatabase();

    // Job 6 is the ADK Processing job that's failing
    const jobId = "6";
    console.log(`Loading job: ${jobId}`);
    const job = await loadJobDefinition(jobId);

    if (!job) {
      console.log(`❌ Job ${jobId} not found`);
      return;
    }

    console.log(`📄 Current job: ${job.name}`);
    console.log(
      `Current destination config:`,
      JSON.stringify(job.destination, null, 2)
    );

    // Check if destination is missing or incomplete
    const needsDestinationFix =
      !job.destination ||
      !job.destination.fileTracking ||
      !job.destination.fileTracking.database;

    if (!needsDestinationFix) {
      console.log("✅ Job already has proper destination configuration");
      return;
    }

    // Add the missing destination configuration
    const updatedJob = {
      ...job,
      destination: {
        type: "database" as const,
        database: {
          type: "mysql" as const,
          host: "localhost",
          port: 3306,
          database: "monev2025",
          username: "root",
          password: "",
          table: "adk_data", // Default table name, actual tables are determined by XML file mapping
          schema: undefined,
        },
        fileTracking: {
          enabled: true,
          database: {
            host: "localhost",
            port: 3306,
            username: "root",
            password: "",
            database: "monev2025",
            table: "file_metadata",
          },
        },
      },
    };

    console.log(
      `\n🔧 Updated destination config:`,
      JSON.stringify(updatedJob.destination, null, 2)
    );

    // Save the updated job
    console.log(`\n💾 Saving updated job configuration...`);
    await saveJobDefinition(updatedJob);

    console.log(`✅ Job configuration updated successfully!`);

    // Verify the fix by loading and validating again
    console.log(`\n🧪 Verifying the fix...`);
    const { jobHandlerFactory } = await import("./src/lib/jobs");
    const handler = jobHandlerFactory.getHandler("adk_processing");
    const isValid = handler.validateConfig(updatedJob);

    console.log(`Configuration now valid: ${isValid}`);

    if (isValid) {
      console.log("🎉 ADK Processing job is now properly configured!");
      console.log("\n📋 Next steps:");
      console.log("1. Enable the job if needed");
      console.log("2. Test run the job to verify it works");
      console.log(
        '3. Check that file_metadata table has records with status "NEW"'
      );
    } else {
      console.log(
        "❌ Configuration is still invalid. Please check the job definition."
      );
    }

    process.exit(0);
  } catch (error) {
    console.error("❌ Error fixing job:", error);
    process.exit(1);
  }
}

fixAdkJobDestination();
