import { NextRequest, NextResponse } from "next/server";
import { loadJobSequence } from "@/lib/sequencePersistence";
import { logger } from "@/lib/jobManager";
import JobSequenceManager from "@/lib/jobSequenceManager";

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// POST /api/admin/sequences/[id]/stop - Stop a running sequence
export async function POST(request: NextRequest, { params }: RouteParams) {
  const { id: sequenceId } = await params;
  try {
    // Check if sequence exists
    const sequence = await loadJobSequence(sequenceId);
    if (!sequence) {
      return NextResponse.json(
        { error: `Sequence not found: ${sequenceId}` },
        { status: 404 }
      );
    }

    // Check if sequence is running
    const sequenceManager = JobSequenceManager.getInstance();
    if (!sequenceManager.isSequenceRunning(sequenceId)) {
      return NextResponse.json(
        { error: `Sequence ${sequenceId} is not currently running` },
        { status: 400 }
      );
    }

    // Stop the sequence
    await sequenceManager.stopSequence(sequenceId);

    logger.info(`Sequence stopped: ${sequenceId}`);

    return NextResponse.json({
      message: "Sequence stopped successfully",
      sequenceId,
    });
  } catch (error) {
    logger.error(`Failed to stop sequence ${sequenceId}:`, error);
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to stop sequence",
      },
      { status: 500 }
    );
  }
}
