# Copilot Instructions

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This is a Next.js application built with TypeScript, React, HeroUI, and TailwindCSS for managing daily data pulling tasks.

## Project Structure

- **Frontend**: React/Next.js with HeroUI components and TailwindCSS styling
- **Backend**: Next.js API routes for data management and job control
- **Scheduling**: Node-cron for daily data pulling automation
- **Logging**: Winston for comprehensive error handling and debugging
- **Data Flow**: Replaces legacy Windows batch file workflow

## Key Features

- Dashboard for monitoring data pulling jobs
- Cron job scheduler for automated daily tasks
- Error handling and logging system
- Real-time job status updates
- Configuration management for data sources

## Development Guidelines

- Use TypeScript for all code
- Follow HeroUI component patterns
- Implement proper error boundaries
- Use server-side API routes for data operations
- Maintain comprehensive logging for debugging
