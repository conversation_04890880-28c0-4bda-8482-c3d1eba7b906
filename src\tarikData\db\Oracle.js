// Dynamic import for oracledb to avoid client-side bundling issues
let oracledb;

async function getOracleDB() {
  if (!oracledb) {
    // Only import oracledb on server side
    if (typeof window === "undefined") {
      oracledb = await import("oracledb");
      return oracledb.default || oracledb;
    } else {
      throw new Error(
        "Oracle database operations are only available on the server side"
      );
    }
  }
  return oracledb;
}

let isOracleClientInitialized = false;

// Initialize Oracle client once
async function initializeOracleClient() {
  if (!isOracleClientInitialized) {
    try {
      const oracleModule = await getOracleDB();

      // Try to initialize Oracle client library (for Windows)
      // This is only needed for "thick mode" - we'll handle both modes
      try {
        oracleModule.initOracleClient({
          libDir: process.env.ORACLE_CLIENT_LIB_DIR || "C:\\instantclient_19_6",
        });
        console.log("Oracle thick mode initialized successfully");
      } catch (thickModeError) {
        // If thick mode fails, we'll fall back to thin mode
        console.log(
          "Oracle thick mode failed, using thin mode:",
          thickModeError.message
        );
        console.log(
          "Note: Thin mode has some limitations but works without Instant Client"
        );
      }

      isOracleClientInitialized = true;
    } catch (error) {
      // Client may already be initialized
      if (!error.message.includes("already been initialized")) {
        throw error;
      }
      isOracleClientInitialized = true;
    }
  }
}

async function oracle_db(config = {}) {
  try {
    // Initialize Oracle client if not already done
    await initializeOracleClient();

    const oracleModule = await getOracleDB();

    // Use provided config or fall back to environment variables
    const connectionConfig = {
      user: config.user || process.env.ORACLE_USERNAME || "USRPA",
      password: config.password || process.env.ORACLE_PASSWORD || "pdpsipa",
      connectString:
        config.connectString ||
        `${process.env.ORACLE_HOST || "**************"}:${
          process.env.ORACLE_PORT || "1521"
        }/${process.env.ORACLE_SERVICE_NAME || "olap23"}`,
    };

    console.log("Menghubungkan ke database Oracle...");

    const connection = await oracleModule.getConnection(connectionConfig);

    console.log("Koneksi ke Oracle berhasil.");
    console.log(
      `--------------------------------------------------------------`
    );

    return connection;
  } catch (error) {
    console.error(
      "Terjadi kesalahan saat menghubungkan ke database ORACLE:",
      error
    );
    throw error; // Re-throw to allow proper error handling
  }
}

// Function to execute a query with proper connection management
async function executeOracleQuery(query, params = [], config = {}) {
  let connection;
  try {
    const oracleModule = await getOracleDB();
    connection = await oracle_db(config);

    // Set up execution options with enhanced type handling
    const options = {
      outFormat: oracleModule.OUT_FORMAT_OBJECT, // Return results as objects
      // Only apply maxRows if explicitly set by user, no default limit
      ...(config.maxRows && { maxRows: config.maxRows }),
      // Configure Oracle driver to handle NUMBER types appropriately
      // This ensures large numbers are properly handled
      fetchInfo: {
        // Fetch NUMBER columns as strings to preserve precision
        // This is the correct way to handle fetchAsString in newer oracledb versions
      },
    };

    // For NUMBER columns, we'll handle them in post-processing instead
    // since fetchAsString is read-only in newer oracledb versions

    // Execute the query with enhanced type handling
    const result = await connection.execute(query, params, options);

    // Post-process the results to handle Oracle-specific NUMBER types
    if (result && result.rows) {
      result.rows = result.rows.map((row) => {
        const processedRow = {};

        // Process each column in the row
        Object.keys(row).forEach((key) => {
          const value = row[key];
          processedRow[key] = processOracleValue(key, value);
        });

        return processedRow;
      });
    }

    return result;
  } catch (error) {
    console.error("Error executing Oracle query:", error);
    throw error;
  } finally {
    if (connection) {
      try {
        await connection.close();
      } catch (err) {
        console.error("Error closing Oracle connection:", err);
      }
    }
  }
}

// Helper function to process Oracle values consistently
function processOracleValue(key, value) {
  // Handle null/undefined values
  if (value === null || value === undefined) {
    return value;
  }

  // Handle Date objects (Oracle DATE/TIMESTAMP values)
  if (value instanceof Date) {
    return value;
  }

  // Handle date strings that might come from Oracle
  if (typeof value === "string" && /^\d{4}-\d{2}-\d{2}/.test(value)) {
    const dateObj = new Date(value);
    if (!isNaN(dateObj.getTime())) {
      return dateObj;
    }
    return value;
  }

  // Handle NUMBER columns - keep all as strings to preserve leading zeros
  if (typeof value === "string" && !isNaN(value)) {
    return value;
  }

  // Return value as-is for all other types
  return value;
}

// Add streaming function for large datasets
async function executeOracleQueryStream(
  query,
  params = [],
  config = {},
  onBatch
) {
  let connection;
  let resultSet;

  try {
    const oracleModule = await getOracleDB();
    connection = await oracle_db(config);

    const options = {
      outFormat: oracleModule.OUT_FORMAT_OBJECT,
      resultSet: true, // Enable result set for streaming
      maxRows: 0, // No limit - we'll control batching manually
    };

    const result = await connection.execute(query, params, options);
    resultSet = result.resultSet;

    let totalRows = 0;
    let batch;
    const batchSize = config.batchSize || 10000;

    while ((batch = await resultSet.getRows(batchSize)).length > 0) {
      // Post-process batch similar to regular query
      const processedBatch = batch.map((row) => {
        const processedRow = {};
        for (const [key, value] of Object.entries(row)) {
          processedRow[key] = processOracleValue(key, value);
        }
        return processedRow;
      });

      await onBatch(processedBatch);
      totalRows += batch.length;
    }

    return { totalRows, rows: [] }; // Empty rows array for streaming
  } finally {
    if (resultSet) {
      try {
        await resultSet.close();
      } catch (err) {
        console.error("Error closing result set:", err);
      }
    }
    if (connection) {
      try {
        await connection.close();
      } catch (err) {
        console.error("Error closing connection:", err);
      }
    }
  }
}

export { oracle_db, executeOracleQuery, executeOracleQueryStream };
