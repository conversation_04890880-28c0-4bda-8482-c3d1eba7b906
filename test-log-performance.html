<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log Viewer Performance Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .old-viewer {
            background-color: #fff5f5;
            border-color: #fed7d7;
        }
        .new-viewer {
            background-color: #f0fff4;
            border-color: #c6f6d5;
        }
        .log-container {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #1a1a1a;
            color: #fff;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 2px;
            padding: 2px 4px;
            border-radius: 2px;
            transition: all 0.2s ease;
        }
        .log-entry.error {
            color: #ff6b6b;
        }
        .log-entry.warn {
            color: #ffd93d;
        }
        .log-entry.info {
            color: #74c0fc;
        }
        .log-entry.debug {
            color: #adb5bd;
        }
        .controls {
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .stat {
            padding: 5px 10px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        .performance-indicator {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 4px;
            margin-left: 10px;
        }
        .good {
            background-color: #d4edda;
            color: #155724;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .bad {
            background-color: #f8d7da;
            color: #721c24;
        }
        .filter-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        input, select {
            padding: 5px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Log Viewer Performance Comparison</h1>
        <p>This test demonstrates the performance improvements in the optimized LogViewer component.</p>
        
        <div class="test-section old-viewer">
            <h2>❌ Old Implementation (Performance Issues)</h2>
            <div class="stats">
                <div class="stat">Total Logs: <span id="oldLogCount">0</span></div>
                <div class="stat">Rendered Elements: <span id="oldRenderedCount">0</span></div>
                <div class="stat">Render Time: <span id="oldRenderTime">0ms</span></div>
                <span class="performance-indicator" id="oldPerformance">Good</span>
            </div>
            <div class="controls">
                <button onclick="generateLogs('old', 100)">Add 100 Logs</button>
                <button onclick="generateLogs('old', 500)">Add 500 Logs</button>
                <button onclick="generateLogs('old', 1000)">Add 1000 Logs</button>
                <button onclick="clearLogs('old')">Clear</button>
            </div>
            <div class="log-container" id="oldLogContainer">
                <div class="log-entry info">Ready to test old implementation...</div>
            </div>
        </div>

        <div class="test-section new-viewer">
            <h2>✅ New Implementation (Optimized)</h2>
            <div class="stats">
                <div class="stat">Total Logs: <span id="newLogCount">0</span></div>
                <div class="stat">Rendered Elements: <span id="newRenderedCount">0</span></div>
                <div class="stat">Render Time: <span id="newRenderTime">0ms</span></div>
                <span class="performance-indicator" id="newPerformance">Good</span>
            </div>
            <div class="filter-controls">
                <input type="text" id="searchInput" placeholder="Search logs..." onkeyup="filterLogs()">
                <select id="levelFilter" onchange="filterLogs()">
                    <option value="all">All Levels</option>
                    <option value="error">Error</option>
                    <option value="warn">Warning</option>
                    <option value="info">Info</option>
                    <option value="debug">Debug</option>
                </select>
                <select id="limitSelect" onchange="limitLogs()">
                    <option value="100">Last 100</option>
                    <option value="500" selected>Last 500</option>
                    <option value="1000">Last 1000</option>
                    <option value="all">Show All</option>
                </select>
            </div>
            <div class="controls">
                <button onclick="generateLogs('new', 100)">Add 100 Logs</button>
                <button onclick="generateLogs('new', 500)">Add 500 Logs</button>
                <button onclick="generateLogs('new', 1000)">Add 1000 Logs</button>
                <button onclick="clearLogs('new')">Clear</button>
            </div>
            <div class="log-container" id="newLogContainer">
                <div class="log-entry info">Ready to test optimized implementation...</div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 Performance Comparison</h2>
            <div id="comparisonResults">
                <p>Generate logs in both viewers to see performance comparison.</p>
            </div>
        </div>
    </div>

    <script>
        let oldLogs = [];
        let newLogs = [];
        let filteredNewLogs = [];

        const logMessages = [
            "🚀 Starting ADK processing: Processing files from /data/adk_rkakl2025",
            "📁 Processing file: archive_001.zip (ID: 12345)",
            "File details - Dept: 34, Unit: 152, Archive: archive_001.zip",
            "✅ Archive extracted successfully: 15 XML files found",
            "Processed 1250 records from data_001.xml",
            "Processed 890 records from data_002.xml",
            "⚠️ Warning: Duplicate record found in data_003.xml, skipping",
            "Processed 2100 records from data_003.xml",
            "File archive_001.zip marked as PROCESSED",
            "❌ Error processing archive_002.zip: File corrupted",
            "🔄 Retrying archive_002.zip (attempt 2/3)",
            "✅ Archive_002.zip processed successfully on retry",
            "📊 ADK processing completed. Total records processed: 15,430",
            "🎉 Job execution completed successfully in 2,145ms"
        ];

        const logLevels = ['info', 'warn', 'error', 'debug'];

        function generateRandomLog() {
            const timestamp = new Date().toLocaleTimeString();
            const message = logMessages[Math.floor(Math.random() * logMessages.length)];
            const level = logLevels[Math.floor(Math.random() * logLevels.length)];
            return { timestamp, message, level };
        }

        function generateLogs(type, count) {
            const startTime = performance.now();
            
            for (let i = 0; i < count; i++) {
                const log = generateRandomLog();
                if (type === 'old') {
                    oldLogs.push(log);
                } else {
                    newLogs.push(log);
                }
            }
            
            const endTime = performance.now();
            const renderTime = Math.round(endTime - startTime);
            
            if (type === 'old') {
                renderOldLogs();
                updateStats('old', oldLogs.length, oldLogs.length, renderTime);
            } else {
                filterLogs();
                updateStats('new', newLogs.length, filteredNewLogs.length, renderTime);
            }
            
            updateComparison();
        }

        function renderOldLogs() {
            const container = document.getElementById('oldLogContainer');
            container.innerHTML = '';
            
            // Old implementation renders ALL logs (performance issue)
            oldLogs.forEach(log => {
                const div = document.createElement('div');
                div.className = `log-entry ${log.level}`;
                div.innerHTML = `<span style="color: #666;">${log.timestamp}</span> ${log.message}`;
                container.appendChild(div);
            });
            
            container.scrollTop = container.scrollHeight;
        }

        function filterLogs() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const levelFilter = document.getElementById('levelFilter').value;
            const limit = document.getElementById('limitSelect').value;
            
            let filtered = newLogs;
            
            // Apply level filter
            if (levelFilter !== 'all') {
                filtered = filtered.filter(log => log.level === levelFilter);
            }
            
            // Apply search filter
            if (searchTerm) {
                filtered = filtered.filter(log => 
                    log.message.toLowerCase().includes(searchTerm)
                );
            }
            
            // Apply limit (show most recent)
            if (limit !== 'all') {
                const limitNum = parseInt(limit);
                if (filtered.length > limitNum) {
                    filtered = filtered.slice(-limitNum);
                }
            }
            
            filteredNewLogs = filtered;
            renderNewLogs();
        }

        function renderNewLogs() {
            const container = document.getElementById('newLogContainer');
            container.innerHTML = '';
            
            // New implementation renders only filtered/limited logs
            filteredNewLogs.forEach(log => {
                const div = document.createElement('div');
                div.className = `log-entry ${log.level}`;
                div.innerHTML = `<span style="color: #666;">${log.timestamp}</span> ${log.message}`;
                container.appendChild(div);
            });
            
            container.scrollTop = container.scrollHeight;
        }

        function limitLogs() {
            filterLogs();
            updateStats('new', newLogs.length, filteredNewLogs.length, 0);
        }

        function clearLogs(type) {
            if (type === 'old') {
                oldLogs = [];
                document.getElementById('oldLogContainer').innerHTML = 
                    '<div class="log-entry info">Ready to test old implementation...</div>';
                updateStats('old', 0, 0, 0);
            } else {
                newLogs = [];
                filteredNewLogs = [];
                document.getElementById('newLogContainer').innerHTML = 
                    '<div class="log-entry info">Ready to test optimized implementation...</div>';
                updateStats('new', 0, 0, 0);
            }
            updateComparison();
        }

        function updateStats(type, totalLogs, renderedLogs, renderTime) {
            document.getElementById(`${type}LogCount`).textContent = totalLogs;
            document.getElementById(`${type}RenderedCount`).textContent = renderedLogs;
            document.getElementById(`${type}RenderTime`).textContent = `${renderTime}ms`;
            
            const performanceEl = document.getElementById(`${type}Performance`);
            if (renderedLogs < 500) {
                performanceEl.textContent = 'Good';
                performanceEl.className = 'performance-indicator good';
            } else if (renderedLogs < 1000) {
                performanceEl.textContent = 'Warning';
                performanceEl.className = 'performance-indicator warning';
            } else {
                performanceEl.textContent = 'Poor';
                performanceEl.className = 'performance-indicator bad';
            }
        }

        function updateComparison() {
            const oldCount = oldLogs.length;
            const newCount = newLogs.length;
            const oldRendered = oldLogs.length;
            const newRendered = filteredNewLogs.length;
            
            if (oldCount === 0 && newCount === 0) {
                document.getElementById('comparisonResults').innerHTML = 
                    '<p>Generate logs in both viewers to see performance comparison.</p>';
                return;
            }
            
            const improvement = oldRendered > 0 ? Math.round(((oldRendered - newRendered) / oldRendered) * 100) : 0;
            
            document.getElementById('comparisonResults').innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>Old Implementation</h4>
                        <p>• Renders ALL ${oldCount} logs</p>
                        <p>• No filtering or limiting</p>
                        <p>• Performance degrades with log count</p>
                        <p>• Memory usage grows linearly</p>
                    </div>
                    <div>
                        <h4>New Implementation</h4>
                        <p>• Renders only ${newRendered} of ${newCount} logs</p>
                        <p>• Smart filtering and limiting</p>
                        <p>• Consistent performance</p>
                        <p>• Optimized memory usage</p>
                    </div>
                </div>
                ${improvement > 0 ? `
                <div style="margin-top: 15px; padding: 10px; background-color: #d4edda; border-radius: 4px; color: #155724;">
                    <strong>Performance Improvement: ${improvement}% reduction in rendered elements</strong>
                </div>
                ` : ''}
            `;
        }
    </script>
</body>
</html>
