/**
 * Integration test for the modular job runner system
 * This file can be used to verify that all job handlers are properly registered and working
 */

import {
  jobHandlerFactory,
  getSupportedJobTypes,
  validateAllRegisteredHandlers,
  getRegisteredHandlersInfo,
  isJobTypeSupported,
} from "./index";

/**
 * Test the modular job system integration
 */
export async function testJobSystemIntegration(): Promise<{
  success: boolean;
  results: Array<{
    test: string;
    passed: boolean;
    details?: string;
  }>;
}> {
  const results: Array<{
    test: string;
    passed: boolean;
    details?: string;
  }> = [];

  console.log("🧪 Testing modular job runner system integration...");

  // Test 1: Check if all expected job types are registered
  const expectedJobTypes = [
    "oracle",
    "mysql",
    "database_admin",
    "sftp",
    "pdf_dipa",
    "adk_processing",
  ];

  const supportedTypes = getSupportedJobTypes();
  const allTypesRegistered = expectedJobTypes.every((type) =>
    supportedTypes.includes(type)
  );

  results.push({
    test: "All expected job types registered",
    passed: allTypesRegistered,
    details: allTypesRegistered
      ? `All ${expectedJobTypes.length} job types registered`
      : `Missing types: ${expectedJobTypes
          .filter((type) => !supportedTypes.includes(type))
          .join(", ")}`,
  });

  // Test 2: Validate all registered handlers
  const validationResults = validateAllRegisteredHandlers();
  const allHandlersValid = validationResults.every((result) => result.isValid);

  results.push({
    test: "All job handlers are valid",
    passed: allHandlersValid,
    details: allHandlersValid
      ? "All handlers passed validation"
      : `Invalid handlers: ${validationResults
          .filter((r) => !r.isValid)
          .map((r) => `${r.jobType}: ${r.error}`)
          .join("; ")}`,
  });

  // Test 3: Test individual job type support checks
  const supportCheckResults = expectedJobTypes.map((type) => ({
    type,
    supported: isJobTypeSupported(type),
  }));

  const allSupportChecksPass = supportCheckResults.every(
    (result) => result.supported
  );

  results.push({
    test: "Job type support checks work correctly",
    passed: allSupportChecksPass,
    details: allSupportChecksPass
      ? "All job types correctly identified as supported"
      : `Unsupported types: ${supportCheckResults
          .filter((r) => !r.supported)
          .map((r) => r.type)
          .join(", ")}`,
  });

  // Test 4: Test handler instantiation
  let handlerInstantiationPassed = true;
  let instantiationDetails = "";

  try {
    for (const jobType of expectedJobTypes) {
      const handler = jobHandlerFactory.getHandler(jobType);

      if (handler.jobType !== jobType) {
        handlerInstantiationPassed = false;
        instantiationDetails += `Handler for ${jobType} has incorrect jobType: ${handler.jobType}; `;
      }
    }

    if (handlerInstantiationPassed) {
      instantiationDetails =
        "All handlers instantiated correctly with matching job types";
    }
  } catch (error) {
    handlerInstantiationPassed = false;
    instantiationDetails = `Handler instantiation failed: ${
      error instanceof Error ? error.message : String(error)
    }`;
  }

  results.push({
    test: "Handler instantiation works correctly",
    passed: handlerInstantiationPassed,
    details: instantiationDetails,
  });

  // Test 5: Test handler info retrieval
  const handlerInfos = getRegisteredHandlersInfo();
  const allHandlersHaveInfo = expectedJobTypes.every((type) =>
    handlerInfos.some((info) => info.jobType === type && info.isRegistered)
  );

  results.push({
    test: "Handler info retrieval works correctly",
    passed: allHandlersHaveInfo,
    details: allHandlersHaveInfo
      ? "All handlers have correct registration info"
      : "Some handlers missing registration info",
  });

  // Test 6: Test unsupported job type handling
  let unsupportedTypeHandlingPassed = false;
  let unsupportedTypeDetails = "";

  try {
    jobHandlerFactory.getHandler("unsupported_job_type");
    unsupportedTypeHandlingPassed = false;
    unsupportedTypeDetails =
      "Should have thrown error for unsupported job type";
  } catch (_error) {
    unsupportedTypeHandlingPassed = true;
    unsupportedTypeDetails = "Correctly threw error for unsupported job type";
  }

  results.push({
    test: "Unsupported job type handling works correctly",
    passed: unsupportedTypeHandlingPassed,
    details: unsupportedTypeDetails,
  });

  // Calculate overall success
  const allTestsPassed = results.every((result) => result.passed);

  // Log results
  console.log("\n📊 Test Results:");
  results.forEach((result) => {
    const status = result.passed ? "✅" : "❌";
    console.log(`${status} ${result.test}: ${result.details}`);
  });

  console.log(
    `\n🎯 Overall Result: ${
      allTestsPassed ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED"
    }`
  );

  return {
    success: allTestsPassed,
    results,
  };
}

/**
 * Run the integration test if this file is executed directly
 */
if (require.main === module) {
  testJobSystemIntegration()
    .then((result) => {
      process.exit(result.success ? 0 : 1);
    })
    .catch((error) => {
      console.error("❌ Integration test failed with error:", error);
      process.exit(1);
    });
}
