import React from "react";
import { Card, CardBody } from "@heroui/react";
import { Database, Server, Users, ArrowRight } from "lucide-react";
import { motion } from "framer-motion";
import { JobDefinition } from "@/lib/jobManager";
import { SequenceInfo } from "../utils/types";

interface JobCardProps {
  job: JobDefinition;
  index: number;
  isSequenceJob?: boolean;
  sequenceInfo?: SequenceInfo;
  onJobSelect: (job: JobDefinition) => void;
}

export const JobCard: React.FC<JobCardProps> = ({
  job,
  index,
  isSequenceJob = false,
  sequenceInfo,
  onJobSelect,
}) => {
  return (
    <motion.div
      whileHover={{
        scale: 1.01,
        transition: { duration: 0.2 },
      }}
      whileTap={{ scale: 0.98 }}
      className="w-full"
    >
      <Card
        className={`card-modern cursor-pointer w-full ${
          isSequenceJob
            ? "border-l-4 border-l-primary bg-primary/5 hover:bg-primary/10"
            : "border-l-4 border-l-muted-foreground/30 hover:bg-muted/50"
        }`}
        isPressable
        onPress={() => onJobSelect(job)}
      >
        <CardBody className="p-2 sm:p-[var(--spacing-md)]">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-[var(--spacing-sm)]">
            {/* Mobile: Top row with number and data source icon */}
            <div className="flex sm:hidden items-center justify-between w-full">
              <div
                className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center ${
                  isSequenceJob
                    ? "bg-primary/10 border border-primary/20"
                    : "bg-muted border border-border"
                }`}
              >
                <span
                  className={`text-xs font-semibold ${
                    isSequenceJob ? "text-primary" : "text-muted-foreground"
                  }`}
                >
                  {isSequenceJob && sequenceInfo
                    ? sequenceInfo.position
                    : index + 1}
                </span>
              </div>

              {/* Data Source Icon - Mobile */}
              <div className="flex-shrink-0 p-1 rounded-md bg-muted/30">
                {job.dataSource.type === "oracle" ? (
                  <Database className="w-4 h-4 text-primary" />
                ) : (
                  <Server className="w-4 h-4 text-green-600" />
                )}
              </div>
            </div>

            {/* Desktop: Job Number/Index */}
            <div
              className={`hidden sm:flex flex-shrink-0 w-6 h-6 rounded-full items-center justify-center ${
                isSequenceJob
                  ? "bg-primary/10 border border-primary/20"
                  : "bg-muted border border-border"
              }`}
            >
              <span
                className={`text-xs font-semibold ${
                  isSequenceJob ? "text-primary" : "text-muted-foreground"
                }`}
              >
                {isSequenceJob && sequenceInfo
                  ? sequenceInfo.position
                  : index + 1}
              </span>
            </div>

            {/* Job Content */}
            <div className="flex-1 min-w-0 w-full sm:w-auto">
              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-[var(--spacing-xs)] mb-[var(--spacing-xs)]">
                <h4 className="font-semibold text-sm sm:truncate text-foreground">
                  {job.name}
                </h4>
                <div className="flex items-center gap-1 sm:gap-[var(--spacing-xs)] flex-wrap">
                  <span
                    className={`status-indicator ${
                      job.enabled ? "status-success" : "status-error"
                    }`}
                  >
                    {job.enabled ? "ON" : "OFF"}
                  </span>
                  <span
                    className={`status-indicator ${
                      job.dataSource.type === "oracle"
                        ? "bg-primary/10 text-primary"
                        : "bg-secondary/10 text-secondary-foreground"
                    }`}
                  >
                    {job.dataSource.type.toUpperCase()}
                  </span>
                  {isSequenceJob ? (
                    <span className="status-indicator bg-primary/10 text-primary flex items-center">
                      <Users className="w-2.5 h-2.5 mr-1" />
                      {sequenceInfo
                        ? `${sequenceInfo.position}/${sequenceInfo.total}`
                        : "Seq"}
                    </span>
                  ) : (
                    <span className="status-indicator bg-muted text-muted-foreground">
                      Solo
                    </span>
                  )}
                </div>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <p className="text-xs text-muted-foreground sm:truncate flex-1">
                  {job.description || "No description"}
                </p>
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1 sm:gap-[var(--spacing-sm)] text-xs text-muted-foreground">
                  <span className="font-mono bg-muted/50 px-[var(--spacing-xs)] py-[var(--spacing-xs)] rounded-[var(--radius-sm)] text-[10px] sm:text-xs">
                    {job.schedule}
                  </span>
                  <span className="text-[10px] sm:text-[var(--font-size-xs)]">
                    ID: {job.id}
                  </span>
                </div>
              </div>
              {isSequenceJob && sequenceInfo && (
                <div className="flex items-center gap-[var(--spacing-xs)] mt-[var(--spacing-xs)]">
                  <span className="text-xs text-primary font-medium">
                    Sequence: {sequenceInfo.sequenceName}
                  </span>
                  {sequenceInfo.position > 1 && (
                    <ArrowRight className="w-2.5 h-2.5 text-primary/60" />
                  )}
                </div>
              )}
            </div>

            {/* Desktop: Data Source Icon */}
            <div className="hidden sm:flex flex-shrink-0 p-[var(--spacing-xs)] rounded-[var(--radius-md)] bg-muted/30">
              {job.dataSource.type === "oracle" ? (
                <Database className="w-4 h-4 text-primary" />
              ) : (
                <Server className="w-4 h-4 text-green-600" />
              )}
            </div>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};
