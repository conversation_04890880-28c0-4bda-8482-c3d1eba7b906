import { getJobStatus } from "./jobManager";

// Store active connections
const connections = new Set<WritableStreamDefaultWriter>();

// Debouncing mechanism for broadcasts
let broadcastTimeout: NodeJS.Timeout | null = null;
let pendingBroadcast = false;
const BROADCAST_DEBOUNCE_MS = 1000; // 1 second debounce

// Function to add a connection
export function addConnection(writer: WritableStreamDefaultWriter) {
  connections.add(writer);
}

// Function to remove a connection
export function removeConnection(writer: WritableStreamDefaultWriter) {
  connections.delete(writer);
}

// Internal function to actually broadcast updates
async function performBroadcast() {
  if (connections.size === 0) return;

  try {
    const jobs = await getJobStatus();
    const data = JSON.stringify({ type: "jobUpdate", jobs });

    // Send to all connected clients
    const promises = Array.from(connections).map(async (writer) => {
      try {
        await writer.write(`data: ${data}\n\n`);
      } catch {
        // Remove dead connections
        connections.delete(writer);
      }
    });

    await Promise.allSettled(promises);
    pendingBroadcast = false;
  } catch (error) {
    console.error("Error broadcasting job update:", error);
    pendingBroadcast = false;
  }
}

// Function to broadcast updates to all connected clients (debounced)
export async function broadcastJobUpdate() {
  if (connections.size === 0) return;

  // If there's already a pending broadcast, don't schedule another
  if (pendingBroadcast) return;

  pendingBroadcast = true;

  // Clear existing timeout if any
  if (broadcastTimeout) {
    clearTimeout(broadcastTimeout);
  }

  // Schedule debounced broadcast
  broadcastTimeout = setTimeout(async () => {
    await performBroadcast();
    broadcastTimeout = null;
  }, BROADCAST_DEBOUNCE_MS);
}

// Function to broadcast immediately (for critical updates)
export async function broadcastJobUpdateImmediate() {
  if (broadcastTimeout) {
    clearTimeout(broadcastTimeout);
    broadcastTimeout = null;
  }
  pendingBroadcast = true;
  await performBroadcast();
}

// Function to broadcast sequence updates to all connected clients
export async function broadcastSequenceUpdate() {
  if (connections.size === 0) return;

  try {
    // Import sequence loading function
    const { loadJobSequences } = await import("./sequencePersistence");
    const sequences = await loadJobSequences();
    const data = JSON.stringify({ type: "sequenceUpdate", sequences });

    // Send to all connected clients
    const promises = Array.from(connections).map(async (writer) => {
      try {
        await writer.write(`data: ${data}\n\n`);
      } catch {
        // Remove dead connections
        connections.delete(writer);
      }
    });

    await Promise.allSettled(promises);
  } catch (error) {
    console.error("Error broadcasting sequence update:", error);
  }
}

// Function to broadcast both job and sequence updates
export async function broadcastAllUpdates() {
  await Promise.all([broadcastJobUpdate(), broadcastSequenceUpdate()]);
}

// Function to get the number of active connections
export function getConnectionCount(): number {
  return connections.size;
}
