import { NextResponse } from "next/server";
import {
  startDataBotServices,
  stopDataBotServices,
  isDataBotStarted,
} from "@/lib/startup";

export async function GET() {
  try {
    const isStarted = isDataBotStarted();

    return NextResponse.json({
      status: isStarted ? "running" : "stopped",
      message: isStarted
        ? "Data Bot services are running"
        : "Data Bot services are not started",
    });
  } catch (error) {
    console.error("Error checking system status:", error);
    return NextResponse.json(
      { error: "Failed to check system status" },
      { status: 500 }
    );
  }
}

export async function POST() {
  try {
    await startDataBotServices();

    return NextResponse.json({
      status: "started",
      message: "Data Bot services started successfully",
    });
  } catch (error) {
    console.error("Error starting Data Bot services:", error);
    return NextResponse.json(
      { error: "Failed to start Data Bot services" },
      { status: 500 }
    );
  }
}

export async function DELETE() {
  try {
    await stopDataBotServices();

    return NextResponse.json({
      status: "stopped",
      message: "Data Bot services stopped successfully",
    });
  } catch (error) {
    console.error("Error stopping Data Bot services:", error);
    return NextResponse.json(
      { error: "Failed to stop Data Bot services" },
      { status: 500 }
    );
  }
}
