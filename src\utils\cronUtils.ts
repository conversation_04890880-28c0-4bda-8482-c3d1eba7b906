/**
 * Utility functions for working with cron expressions
 */
import { CronExpressionParser } from "cron-parser";

export interface CronValidationResult {
  isValid: boolean;
  error?: string;
  nextRuns?: Date[];
}

/**
 * Basic cron expression validation
 * This is a simplified version - in production you'd use a proper cron parser
 */
export function validateCronExpression(
  expression: string
): CronValidationResult {
  if (!expression || typeof expression !== "string") {
    return {
      isValid: false,
      error: "Cron expression is required",
    };
  }

  const parts = expression.trim().split(/\s+/);

  // Standard cron has 5 parts: minute hour day month weekday
  if (parts.length !== 5) {
    return {
      isValid: false,
      error:
        "Cron expression must have exactly 5 parts: minute hour day month weekday",
    };
  }

  const [minute, hour, day, month, weekday] = parts;

  // Basic validation for each part
  try {
    // Validate minute (0-59)
    if (!isValidCronField(minute, 0, 59)) {
      return {
        isValid: false,
        error: "Invalid minute field (must be 0-59 or valid cron expression)",
      };
    }

    // Validate hour (0-23)
    if (!isValidCronField(hour, 0, 23)) {
      return {
        isValid: false,
        error: "Invalid hour field (must be 0-23 or valid cron expression)",
      };
    }

    // Validate day (1-31)
    if (!isValidCronField(day, 1, 31)) {
      return {
        isValid: false,
        error: "Invalid day field (must be 1-31 or valid cron expression)",
      };
    }

    // Validate month (1-12)
    if (!isValidCronField(month, 1, 12)) {
      return {
        isValid: false,
        error: "Invalid month field (must be 1-12 or valid cron expression)",
      };
    }

    // Validate weekday (0-7, where 0 and 7 are Sunday)
    if (!isValidCronField(weekday, 0, 7)) {
      return {
        isValid: false,
        error: "Invalid weekday field (must be 0-7 or valid cron expression)",
      };
    }

    return {
      isValid: true,
      nextRuns: calculateNextRuns(expression),
    };
  } catch (_error) {
    return {
      isValid: false,
      error: "Invalid cron expression format",
    };
  }
}

/**
 * Validate a single cron field
 */
function isValidCronField(field: string, min: number, max: number): boolean {
  // Allow wildcard
  if (field === "*") return true;

  // Allow step values (e.g., */5)
  if (field.startsWith("*/")) {
    const step = parseInt(field.substring(2));
    return !isNaN(step) && step > 0 && step <= max;
  }

  // Allow ranges (e.g., 1-5)
  if (field.includes("-")) {
    const [start, end] = field.split("-").map((n) => parseInt(n));
    return (
      !isNaN(start) && !isNaN(end) && start >= min && end <= max && start <= end
    );
  }

  // Allow lists (e.g., 1,3,5)
  if (field.includes(",")) {
    const values = field.split(",").map((n) => parseInt(n));
    return values.every((val) => !isNaN(val) && val >= min && val <= max);
  }

  // Single number
  const num = parseInt(field);
  return !isNaN(num) && num >= min && num <= max;
}

/**
 * Calculate next few run times for a cron expression
 * Uses cron-parser for accurate calculation
 */
export function calculateNextRuns(
  expression: string,
  count: number = 3
): Date[] {
  const runs: Date[] = [];

  try {
    // Use cron-parser for accurate calculation
    const interval = CronExpressionParser.parse(expression, {
      currentDate: new Date(),
      tz: process.env.TIMEZONE || "Asia/Jakarta",
    });

    for (let i = 0; i < count; i++) {
      runs.push(interval.next().toDate());
    }
  } catch (_error) {
    // Fallback to simple patterns for demo purposes
    const now = new Date();

    try {
      if (expression === "* * * * *") {
        // Every minute
        for (let i = 1; i <= count; i++) {
          runs.push(new Date(now.getTime() + i * 60 * 1000));
        }
      } else if (expression.startsWith("*/")) {
        // Step patterns
        const minute = expression.split(" ")[0];
        if (minute.startsWith("*/")) {
          const step = parseInt(minute.substring(2));
          for (let i = 1; i <= count; i++) {
            runs.push(new Date(now.getTime() + i * step * 60 * 1000));
          }
        }
      } else if (expression.includes("0 2 * * *")) {
        // Daily at 2 AM
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(2, 0, 0, 0);
        runs.push(tomorrow);

        for (let i = 1; i < count; i++) {
          const nextDay = new Date(tomorrow);
          nextDay.setDate(nextDay.getDate() + i);
          runs.push(nextDay);
        }
      } else if (expression.includes("0 */6 * * *")) {
        // Every 6 hours
        for (let i = 1; i <= count; i++) {
          runs.push(new Date(now.getTime() + i * 6 * 60 * 60 * 1000));
        }
      } else {
        // Default: assume daily
        for (let i = 1; i <= count; i++) {
          const nextRun = new Date(now);
          nextRun.setDate(nextRun.getDate() + i);
          nextRun.setHours(2, 0, 0, 0);
          runs.push(nextRun);
        }
      }
    } catch (_fallbackError) {
      // Return empty array if calculation fails
    }
  }

  return runs;
}

/**
 * Get human-readable description of cron expression
 */
export function describeCronExpression(expression: string): string {
  const commonPatterns: Record<string, string> = {
    "* * * * *": "Every minute",
    "*/5 * * * *": "Every 5 minutes",
    "*/15 * * * *": "Every 15 minutes",
    "*/30 * * * *": "Every 30 minutes",
    "0 * * * *": "Every hour",
    "0 */6 * * *": "Every 6 hours",
    "0 0 * * *": "Daily at midnight",
    "0 2 * * *": "Daily at 2 AM",
    "0 6 * * *": "Daily at 6 AM",
    "0 9 * * 1-5": "Weekdays at 9 AM",
    "0 0 * * 0": "Weekly on Sunday",
    "0 0 1 * *": "Monthly on the 1st",
  };

  return commonPatterns[expression] || "Custom schedule";
}

/**
 * Common cron presets
 */
export const CRON_PRESETS = [
  {
    label: "Every minute",
    value: "* * * * *",
    description: "Runs every minute",
  },
  {
    label: "Every 5 minutes",
    value: "*/5 * * * *",
    description: "Runs every 5 minutes",
  },
  {
    label: "Every 15 minutes",
    value: "*/15 * * * *",
    description: "Runs every 15 minutes",
  },
  {
    label: "Every 30 minutes",
    value: "*/30 * * * *",
    description: "Runs every 30 minutes",
  },
  {
    label: "Every hour",
    value: "0 * * * *",
    description: "Runs at the start of every hour",
  },
  {
    label: "Every 6 hours",
    value: "0 */6 * * *",
    description: "Runs every 6 hours",
  },
  {
    label: "Daily at midnight",
    value: "0 0 * * *",
    description: "Runs daily at midnight",
  },
  {
    label: "Daily at 2:00 AM",
    value: "0 2 * * *",
    description: "Runs daily at 2:00 AM",
  },
  {
    label: "Daily at 6:00 AM",
    value: "0 6 * * *",
    description: "Runs daily at 6:00 AM",
  },
  {
    label: "Weekly (Sunday)",
    value: "0 0 * * 0",
    description: "Runs every Sunday at midnight",
  },
  {
    label: "Monthly (1st)",
    value: "0 0 1 * *",
    description: "Runs on the 1st of every month at midnight",
  },
  {
    label: "Weekdays at 9:00 AM",
    value: "0 9 * * 1-5",
    description: "Runs Monday through Friday at 9:00 AM",
  },
];
