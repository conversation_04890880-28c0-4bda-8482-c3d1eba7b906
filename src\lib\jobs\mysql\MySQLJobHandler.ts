import { <PERSON><PERSON>ob<PERSON>and<PERSON> } from "../base/BaseJobHandler";
import type {
  <PERSON><PERSON><PERSON><PERSON>,
  JobExecutionContext,
  DatabaseOperationResult,
} from "../types";
import type { JobDefinition } from "../../jobManager";
import { logger } from "../../jobManager";
import type { Pool } from "mysql2/promise";

/**
 * Job handler for MySQL database operations
 */
export class MySQLJobHandler extends BaseJobHandler {
  public readonly jobType = "mysql";

  /**
   * Execute MySQL database job
   */
  public async execute(context: JobExecutionContext): Promise<JobResult> {
    const { jobDefinition } = context;
    const mysqlConfig = jobDefinition.dataSource.mysql;

    if (!mysqlConfig) {
      throw new Error("MySQL configuration is required for MySQL data source");
    }

    // Validate configuration
    this.validateMySQLConfig(mysqlConfig);

    await this.logOperationStart(
      context,
      "MySQL database connection",
      `${mysqlConfig.host}:${mysqlConfig.port}/${mysqlConfig.database}`
    );

    try {
      // Import mysql2 for database connection
      const mysql = await import("mysql2/promise");

      // Create connection configuration
      const connectionConfig = {
        host: mysqlConfig.host,
        port: mysqlConfig.port,
        user: mysqlConfig.username,
        password: mysqlConfig.password,
        database: mysqlConfig.database,
        waitForConnections: true,
        connectionLimit: mysqlConfig.connectionLimit || 10,
        queueLimit: 0,
      };

      // Create connection pool
      const pool = mysql.createPool(connectionConfig);

      await context.addLog("Connected to MySQL database successfully");
      await context.addLog("Executing SQL query...");

      try {
        // Check if this is a DDL/DML operation or a SELECT query
        const queryType = mysqlConfig.query.trim().toLowerCase();
        const isDDL =
          queryType.includes("create") ||
          queryType.includes("drop") ||
          queryType.includes("alter") ||
          queryType.includes("set @");

        if (isDDL) {
          return await this.executeDDLOperations(context, pool, mysqlConfig);
        } else {
          return await this.executeSelectQuery(context, pool, mysqlConfig);
        }
      } finally {
        // Always close the pool
        await pool.end();
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown MySQL error";

      await context.addLog(`MySQL connection failed: ${errorMessage}`);

      logger.error(
        `MySQL database connection failed for job ${context.jobId}`,
        {
          jobId: context.jobId,
          error: errorMessage,
          host: mysqlConfig.host,
          port: mysqlConfig.port,
          database: mysqlConfig.database,
        }
      );

      throw new Error(`MySQL database connection failed: ${errorMessage}`);
    }
  }

  /**
   * Execute DDL operations (CREATE, DROP, ALTER, etc.)
   */
  private async executeDDLOperations(
    context: JobExecutionContext,
    pool: Pool,
    mysqlConfig: NonNullable<JobDefinition["dataSource"]["mysql"]>
  ): Promise<JobResult> {
    await context.addLog("Executing DDL/administrative operations...");

    // Split the query by semicolons to handle multiple statements
    const statements = mysqlConfig.query
      .split(";")
      .map((stmt) => stmt.trim())
      .filter((stmt) => stmt.length > 0);

    const results: DatabaseOperationResult[] = [];

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement) {
        await context.checkCancellation();

        await context.addLog(
          `Executing statement ${i + 1}/${statements.length}...`
        );

        const statementStartTime = Date.now();
        const [result] = await pool.execute(statement);
        const executionTime = Date.now() - statementStartTime;

        results.push({
          statementIndex: i + 1,
          sql:
            statement.substring(0, 100) + (statement.length > 100 ? "..." : ""),
          success: true,
          result: result,
          executionTimeMs: executionTime,
        });
      }
    }

    await context.addLog(
      `DDL operations completed successfully. Executed ${statements.length} statements`
    );

    logger.info(
      `MySQL DDL operations executed successfully for job ${context.jobId}`,
      {
        jobId: context.jobId,
        statementCount: statements.length,
        query: mysqlConfig.query.substring(0, 100) + "...",
      }
    );

    return this.processDatabaseResults(results);
  }

  /**
   * Execute SELECT query
   */
  private async executeSelectQuery(
    context: JobExecutionContext,
    pool: Pool,
    mysqlConfig: NonNullable<JobDefinition["dataSource"]["mysql"]>
  ): Promise<JobResult> {
    const [rows] = await pool.execute(mysqlConfig.query);
    const recordCount = Array.isArray(rows) ? rows.length : 0;

    await context.addLog(
      `Query executed successfully. Retrieved ${recordCount} records`
    );

    logger.info(`MySQL query executed successfully for job ${context.jobId}`, {
      jobId: context.jobId,
      recordCount,
      query: mysqlConfig.query.substring(0, 100) + "...",
    });

    return this.createJobResult(recordCount, rows || []);
  }

  /**
   * Validate MySQL job configuration
   */
  public validateConfig(jobDef: JobDefinition): boolean {
    if (!super.validateConfig(jobDef)) {
      return false;
    }

    const mysqlConfig = jobDef.dataSource.mysql;
    if (!mysqlConfig) {
      return false;
    }

    try {
      this.validateMySQLConfig(mysqlConfig);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get required permissions for MySQL operations
   */
  public getRequiredPermissions(): string[] {
    return ["database:mysql:read", "database:mysql:write"];
  }

  /**
   * Validate MySQL configuration fields
   */
  private validateMySQLConfig(
    config: NonNullable<JobDefinition["dataSource"]["mysql"]>
  ): void {
    this.validateRequiredFields(
      config,
      ["host", "port", "database", "username", "password", "query"],
      "MySQL configuration"
    );

    // Additional MySQL-specific validations
    if (
      typeof config.port !== "number" ||
      config.port <= 0 ||
      config.port > 65535
    ) {
      throw new Error("MySQL port must be a valid number between 1 and 65535");
    }

    if (!config.query.trim()) {
      throw new Error("MySQL query cannot be empty");
    }

    if (
      config.connectionLimit &&
      (config.connectionLimit <= 0 || config.connectionLimit > 100)
    ) {
      throw new Error("MySQL connection limit must be between 1 and 100");
    }
  }
}
