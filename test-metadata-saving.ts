import { initializeDatabase } from "./src/lib/database";
import { executeQuery } from "./src/lib/database";

interface FileTransferRecord {
  id: number;
  job_id: string;
  remote_file_name: string;
  file_size_bytes: number;
  transfer_status: string;
  created_at: string;
}

interface ErrorLogRecord {
  execution_id: string;
  log_level: string;
  message: string;
  log_timestamp: string;
}

async function testMetadataSaving() {
  try {
    console.log("🔧 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized successfully");

    // Check if sftp_file_transfers table exists and has data
    console.log("\n📊 Checking sftp_file_transfers table...");
    const transfers = await executeQuery<FileTransferRecord>(
      "SELECT id, job_id, remote_file_name, file_size_bytes, transfer_status, created_at FROM sftp_file_transfers ORDER BY created_at DESC LIMIT 10"
    );

    console.log(`Found ${transfers.length} file transfer records:`);
    transfers.forEach((transfer) => {
      console.log(
        `- ID: ${transfer.id}, Job: ${transfer.job_id}, File: ${transfer.remote_file_name}, Size: ${transfer.file_size_bytes}, Status: ${transfer.transfer_status}, Created: ${transfer.created_at}`
      );
    });

    // Check job_execution_logs for error logs
    console.log("\n📝 Checking job_execution_logs for SFTP errors...");
    const errorLogs = await executeQuery<ErrorLogRecord>(
      "SELECT execution_id, log_level, message, log_timestamp FROM job_execution_logs WHERE log_level = 'error' AND message LIKE '%SFTP%' ORDER BY log_timestamp DESC LIMIT 5"
    );

    console.log(`Found ${errorLogs.length} SFTP error log entries:`);
    errorLogs.forEach((log) => {
      console.log(
        `- ${log.log_timestamp}: [${log.log_level}] ${log.message.substring(
          0,
          100
        )}...`
      );
    });

    console.log("\n✅ Metadata testing completed");
  } catch (error) {
    console.error("❌ Error testing metadata saving:", error);
    if (error instanceof Error) {
      console.error("Stack trace:", error.stack);
    }
  } finally {
    process.exit(0);
  }
}

// Run the test
testMetadataSaving();
