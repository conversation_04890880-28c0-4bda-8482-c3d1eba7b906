import React from "react";
import {
  Input,
  Textarea,
  Select,
  SelectItem,
  Switch,
  Button,
} from "@heroui/react";
import {
  Plus,
  Trash2,
  Settings,
  Clock,
  List,
  GripVertical,
} from "lucide-react";
import { JobDefinition, JobSequence } from "@/lib/jobManager";
import { ViewMode } from "../../utils/types";
import CronExpressionBuilder from "@/components/CronExpressionBuilder";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

interface SequenceFormProps {
  editedSequence: JobSequence;
  setEditedSequence: (sequence: JobSequence) => void;
  viewMode: ViewMode;
  activeTab: string;
  jobs: JobDefinition[];
  onAddJobsToSequence: () => void;
}

interface SortableJobItemProps {
  jobId: string;
  job: JobDefinition | undefined;
  index: number;
  onRemove: () => void;
}

const SortableJobItem: React.FC<SortableJobItemProps> = ({
  jobId,
  job,
  index,
  onRemove,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: jobId });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex items-center gap-3 p-4 rounded-2xl bg-slate-100 border border-slate-200/60 transition-all ${
        isDragging
          ? "shadow-lg z-10 opacity-50 bg-slate-00"
          : "hover:shadow-sm hover:bg-slate-300/50 hover:border-slate-300/60"
      }`}
      {...attributes}
    >
      <div
        {...listeners}
        className="cursor-grab active:cursor-grabbing p-1.5 hover:bg-slate-200/60 rounded-md transition-colors"
      >
        <GripVertical className="w-4 h-4 text-slate-400" />
      </div>
      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
        <span className="text-sm font-semibold text-blue-600">{index + 1}</span>
      </div>
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-slate-900 truncate">
          {job?.name || jobId}
        </h4>
        <p className="text-sm text-slate-600 line-clamp-2">
          {job?.description || "Job not found"}
        </p>
      </div>
      <Button
        size="sm"
        color="danger"
        variant="flat"
        startContent={<Trash2 className="w-4 h-4" />}
        onPress={onRemove}
        className="flex-shrink-0"
      >
        Remove
      </Button>
    </div>
  );
};

export const SequenceForm: React.FC<SequenceFormProps> = ({
  editedSequence,
  setEditedSequence,
  viewMode,
  activeTab,
  jobs,
  onAddJobsToSequence,
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = editedSequence.jobs.indexOf(active.id as string);
      const newIndex = editedSequence.jobs.indexOf(over.id as string);

      const newJobs = arrayMove(editedSequence.jobs, oldIndex, newIndex);
      setEditedSequence({
        ...editedSequence,
        jobs: newJobs,
      });
    }
  };

  const renderBasicTab = () => (
    <div className="form-section animate-fade-in">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center">
          <Settings className="w-4 h-4 text-blue-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Basic Information
          </h3>
          <p className="text-sm text-gray-600">
            Configure sequence identification and behavior
          </p>
        </div>
      </div>

      <div className="form-group">
        {/* Compact two-column layout for ID and Name */}
        <fieldset className="form-row" aria-label="Sequence identification">
          <Input
            label="Sequence ID"
            value={editedSequence.id}
            onChange={(e) =>
              setEditedSequence({
                ...editedSequence,
                id: e.target.value,
              })
            }
            isDisabled={viewMode === "edit-sequence"}
            placeholder="daily-data-pipeline"
            description={
              viewMode === "edit-sequence"
                ? "Cannot be changed"
                : "Unique identifier"
            }
            isRequired
            classNames={{
              input: "text-sm",
              label: "text-sm font-medium text-gray-700",
              description: "text-xs text-gray-500",
            }}
          />
          <Input
            label="Sequence Name"
            value={editedSequence.name}
            onChange={(e) =>
              setEditedSequence({
                ...editedSequence,
                name: e.target.value,
              })
            }
            placeholder="Daily Data Processing Pipeline"
            isRequired
            classNames={{
              input: "text-sm",
              label: "text-sm font-medium text-gray-700",
            }}
          />
        </fieldset>

        <Textarea
          label="Description"
          value={editedSequence.description}
          onChange={(e) =>
            setEditedSequence({
              ...editedSequence,
              description: e.target.value,
            })
          }
          placeholder="Describe what this sequence does..."
          rows={2}
          classNames={{
            input: "text-sm",
            label: "text-sm font-medium text-gray-700",
          }}
        />

        <fieldset className="form-row" aria-label="Failure handling">
          <Select
            label="On Failure"
            selectedKeys={[editedSequence.onFailure]}
            onSelectionChange={(keys) =>
              setEditedSequence({
                ...editedSequence,
                onFailure: Array.from(keys)[0] as "stop" | "continue" | "retry",
              })
            }
            isRequired
            classNames={{
              label: "text-sm font-medium text-gray-700",
              trigger: "border-gray-300 hover:border-gray-400",
            }}
          >
            <SelectItem key="stop">Stop Sequence</SelectItem>
            <SelectItem key="continue">Continue to Next Job</SelectItem>
            <SelectItem key="retry">Retry Failed Job</SelectItem>
          </Select>
          <Input
            label="Max Retries"
            type="number"
            value={editedSequence.maxRetries.toString()}
            onChange={(e) =>
              setEditedSequence({
                ...editedSequence,
                maxRetries: parseInt(e.target.value) || 1,
              })
            }
            min={1}
            max={10}
            isRequired
            classNames={{
              input: "text-sm",
              label: "text-sm font-medium text-gray-700",
            }}
          />
        </fieldset>

        {/* Compact enable switch */}
        <div className="bg-blue-50/50 p-4 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm font-medium text-gray-900">
                Sequence Status
              </span>
              <p className="text-xs text-gray-600 mt-1">
                {editedSequence.enabled
                  ? "Sequence will run according to schedule"
                  : "Sequence is disabled and will not run"}
              </p>
            </div>
            <Switch
              isSelected={editedSequence.enabled}
              onValueChange={(enabled) =>
                setEditedSequence({ ...editedSequence, enabled })
              }
              color={editedSequence.enabled ? "success" : "default"}
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderScheduleTab = () => (
    <div className="form-section animate-fade-in">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 rounded-lg bg-orange-100 flex items-center justify-center">
          <Clock className="w-4 h-4 text-orange-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Schedule Configuration
          </h3>
          <p className="text-sm text-gray-600">
            Configure when this sequence should run automatically
          </p>
        </div>
      </div>

      <div className="form-group">
        <CronExpressionBuilder
          value={editedSequence.schedule || ""}
          onChange={(cronExpression) =>
            setEditedSequence({
              ...editedSequence,
              schedule: cronExpression,
            })
          }
        />

        <div className="bg-blue-50/50 p-4 rounded-lg border border-blue-200">
          <div className="flex items-start gap-2">
            <div className="w-4 h-4 rounded-full bg-blue-500 flex-shrink-0 mt-0.5"></div>
            <div>
              <p className="text-sm font-medium text-blue-900">
                Schedule Information
              </p>
              <p className="text-sm text-blue-700 mt-1">
                Leave empty for manual execution only. The sequence will execute
                all jobs in order according to this schedule.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderJobsTab = () => (
    <div className="form-section animate-fade-in">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center">
          <List className="w-4 h-4 text-green-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Sequence Jobs</h3>
          <p className="text-sm text-gray-600">
            Configure the jobs that will run in this sequence
          </p>
        </div>
      </div>

      <div className="form-group">
        <div className="flex items-center justify-between mb-4">
          <div>
            <p className="text-sm font-medium text-gray-700">
              Job Execution Order
            </p>
            <p className="text-xs text-gray-500">
              Jobs will be executed in the order shown below. Drag to reorder.
            </p>
          </div>
          <Button
            size="sm"
            color="primary"
            startContent={<Plus className="w-4 h-4" />}
            onPress={onAddJobsToSequence}
          >
            Add Job
          </Button>
        </div>

        {editedSequence.jobs.length === 0 ? (
          <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
            <List className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 font-medium">
              No jobs assigned to this sequence yet
            </p>
            <p className="text-sm text-gray-400 mt-1">
              Use the &quot;Add Job&quot; button to assign existing jobs to this
              sequence
            </p>
          </div>
        ) : (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={editedSequence.jobs}
              strategy={verticalListSortingStrategy}
            >
              <div className="space-y-2">
                {editedSequence.jobs.map((jobId, index) => {
                  const job = jobs.find((j) => j.id === jobId);
                  return (
                    <SortableJobItem
                      key={jobId}
                      jobId={jobId}
                      job={job}
                      index={index}
                      onRemove={() => {
                        const updatedJobs = editedSequence.jobs.filter(
                          (_, i) => i !== index
                        );
                        setEditedSequence({
                          ...editedSequence,
                          jobs: updatedJobs,
                        });
                      }}
                    />
                  );
                })}
              </div>
            </SortableContext>
          </DndContext>
        )}
      </div>
    </div>
  );

  return (
    <div className="w-full">
      <div className="space-y-4">
        {activeTab === "basic" && renderBasicTab()}
        {activeTab === "schedule" && renderScheduleTab()}
        {activeTab === "jobs" && renderJobsTab()}
      </div>
    </div>
  );
};
