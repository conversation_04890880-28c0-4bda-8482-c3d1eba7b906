#!/bin/bash

echo "🚀 Setting up Data Bot Dashboard for Oracle & SFTP..."

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please edit it with your actual credentials."
else
    echo "⚠️  .env file already exists. Please update it with new Oracle and SFTP variables."
fi

# Create data directories
echo "📁 Creating data directories..."
mkdir -p data/customer_imports
mkdir -p data/inventory
mkdir -p data/analytics
mkdir -p data/processed
mkdir -p data/downloads
mkdir -p logs

# Set proper permissions
chmod 755 data/
chmod 755 data/*
chmod 755 logs/

echo "✅ Directory structure created."

# Install optional dependencies for production
echo "📦 Installing optional dependencies..."
echo "ℹ️  Run the following commands to install Oracle and SFTP support:"
echo ""
echo "npm install oracledb ssh2-sftp-client fs-extra"
echo "npm install --save-dev @types/ssh2-sftp-client @types/fs-extra"
echo ""

# Install Oracle Instant Client (Linux example)
echo "🔧 Oracle Instant Client Installation:"
echo "For Oracle connectivity, you'll need Oracle Instant Client:"
echo ""
echo "# Linux (Ubuntu/Debian):"
echo "wget https://download.oracle.com/otn_software/linux/instantclient/oracle-instantclient-basic-linuxx64.rpm"
echo "sudo alien --scripts oracle-instantclient-basic-linuxx64.rpm"
echo "sudo dpkg -i oracle-instantclient-basic*.deb"
echo ""
echo "# Or download from: https://www.oracle.com/database/technologies/instant-client/downloads.html"
echo ""

echo "📋 Next Steps:"
echo "1. Edit .env file with your Oracle and SFTP credentials"
echo "2. Install optional dependencies (see above)"
echo "3. Install Oracle Instant Client (if using Oracle)"
echo "4. Run 'npm run migrate' to set up database with production-ready job templates"
echo "5. Run 'npm run dev' to start the development server"
echo "6. Configure and enable specific jobs through the web interface"
echo ""
echo "📖 See ORACLE_SFTP_CONFIG.md for detailed configuration instructions."
echo ""
echo "🎉 Setup complete!"
