// Test running the ADK processing job
import { runDataPullingJob } from './src/lib/jobRunner';
import { initializeDatabase } from './src/lib/database';

async function testAdkJob() {
  try {
    console.log('🧪 Testing ADK Processing Job Execution...\n');
    
    console.log('Initializing database...');
    await initializeDatabase();
    
    const jobId = 'job-1753091554181';
    console.log(`🚀 Running job: ${jobId}`);
    
    const success = await runDataPullingJob(jobId, 'manual');
    
    if (success) {
      console.log('✅ Job executed successfully!');
    } else {
      console.log('❌ Job execution failed');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error testing job:', error);
    process.exit(1);
  }
}

testAdkJob();
