# Log Viewer Performance Optimization

## Overview

The ADK processing job's live execution log display has been significantly optimized to address UI performance issues caused by verbose logging and large log volumes. The solution implements multiple layers of optimization while maintaining real-time visibility and user experience.

## Performance Issues Addressed

### Before Optimization
- **Unlimited log accumulation**: Logs array grew indefinitely during job execution
- **Frequent SSE broadcasts**: Every log entry triggered immediate broadcasts to all clients
- **Expensive animations**: Framer Motion animations on every log entry with staggered delays
- **No virtualization**: All log entries rendered simultaneously in DOM
- **Inefficient re-renders**: Entire logs array re-rendered on every update
- **No filtering**: All log levels displayed regardless of importance

### Performance Impact
- DOM bloat with 1000+ animated elements
- Browser lag from continuous animations and re-renders
- Memory usage growing linearly with log count
- UI freezing during high-volume logging periods

## Optimization Solutions

### 1. Enhanced LogViewer Component

#### **Log Entry Limiting**
- Default display: Last 500 entries
- Configurable limits: 100, 500, 1000, or Show All
- Automatic memory management for large log sets

#### **Efficient Rendering**
- Replaced Framer Motion with CSS transitions
- Memoized components with React.memo
- Optimized re-renders with useMemo and useCallback
- Removed expensive staggered animations

#### **Advanced Filtering**
- **Log Level Filtering**: Error, Warning, Info, Debug
- **Real-time Search**: Highlight matching terms
- **Level Counts**: Display count of each log level
- **Smart Parsing**: Automatic log level detection from content

#### **Virtual Scrolling**
- Automatic activation for logs > 1000 entries
- Manual toggle available in controls
- Renders only visible log entries
- Maintains smooth scrolling performance

### 2. Server-Side Optimizations

#### **Debounced SSE Broadcasts**
- 1-second debounce on log updates
- Batches multiple log additions into single broadcast
- Reduces network traffic and client-side processing
- Immediate broadcast option for critical updates

#### **Memory Management**
- Log rotation: Keep only last 1000 logs in memory
- Prevents unlimited memory growth
- Database persistence maintains full log history
- Configurable rotation limits

### 3. User Experience Enhancements

#### **Smart Controls**
- Collapsible control panel
- Auto-scroll toggle with visual indicator
- Export functionality for full log download
- Performance indicators (Virtual Scrolling badge)

#### **Visual Improvements**
- Color-coded log levels
- Timestamp display
- Search term highlighting
- Hover effects for better interaction

## Usage Guide

### Basic Usage
The LogViewer automatically optimizes performance based on log volume:
- **< 500 logs**: Standard rendering with all features
- **500-1000 logs**: Entry limiting with warning indicator
- **> 1000 logs**: Automatic virtual scrolling activation

### Controls Panel
Click "Show Controls" to access:

#### **Search and Filter**
- **Search Box**: Real-time search through log content
- **Level Filter**: Filter by Error, Warning, Info, Debug levels
- **Level Counts**: See distribution of log levels

#### **Display Options**
- **Entry Limits**: Choose how many recent logs to display
- **Virtual Scrolling**: Manual toggle for performance mode
- **Auto-scroll**: Toggle automatic scrolling to new entries

#### **Export**
- **Export Button**: Download all logs as text file
- **Format**: `timestamp [LEVEL] message`
- **Filename**: `job-{jobId}-logs.txt`

### Performance Indicators
- **Warning Chip**: Shows when logs are limited
- **Virtual Scrolling Chip**: Indicates performance mode is active
- **Level Counts**: Helps identify log distribution

## Technical Implementation

### Component Architecture
```
LogViewer (main container)
├── Controls Panel (filtering, search, export)
├── VirtualizedLogList (for large log sets)
└── LogEntryComponent (memoized individual entries)
```

### Log Entry Structure
```typescript
interface LogEntry {
  id: string;           // Unique identifier
  message: string;      // Cleaned log message
  timestamp: string;    // Extracted timestamp
  level: 'info' | 'warn' | 'error' | 'debug';
}
```

### Performance Optimizations
- **React.memo**: Prevents unnecessary re-renders
- **useMemo**: Caches filtered/parsed log results
- **useCallback**: Stable event handler references
- **Virtual Scrolling**: Renders only visible items
- **Debounced Updates**: Batches server-side broadcasts

## Configuration

### Server-Side Settings
```typescript
// SSE broadcast debounce (in sseManager.ts)
const BROADCAST_DEBOUNCE_MS = 1000; // 1 second

// Memory log rotation (in jobManager.ts)
const MAX_LOGS_IN_MEMORY = 1000; // Keep last 1000 logs
```

### Client-Side Defaults
```typescript
// Default display limits
const DEFAULT_MAX_ENTRIES = 500;
const VIRTUAL_SCROLL_THRESHOLD = 1000;
const CONTAINER_HEIGHT = 256; // pixels
const ITEM_HEIGHT = 24; // pixels per log entry
```

## Benefits

### Performance Improvements
- **90%+ reduction** in DOM elements for large log sets
- **Smooth scrolling** even with 10,000+ log entries
- **Reduced memory usage** through log rotation
- **Faster rendering** with optimized components

### User Experience
- **Real-time filtering** and search capabilities
- **Responsive interface** during high-volume logging
- **Customizable display** options for different use cases
- **Export functionality** for log analysis

### Developer Benefits
- **Maintainable code** with clear component separation
- **Configurable limits** for different deployment needs
- **Backward compatibility** with existing job system
- **Extensible architecture** for future enhancements

## Future Enhancements

### Planned Features
- **Log streaming**: Real-time log tailing for active jobs
- **Advanced filtering**: Regex support, time range filters
- **Log analytics**: Error rate tracking, performance metrics
- **Customizable themes**: Different color schemes for log levels

### Performance Monitoring
- **Metrics collection**: Track rendering performance
- **Memory monitoring**: Alert on excessive memory usage
- **User analytics**: Monitor feature usage patterns
