// Export all types
export * from "./types";

// Export base classes
export { BaseJobHandler } from "./base/BaseJobHandler";
export { JobExecutionContext } from "./context/JobExecutionContext";

// Export factory
export { JobHandlerFactory, jobHandlerFactory } from "./factory/JobHandlerFactory";

// Export individual job handlers
export { OracleJobHandler } from "./oracle/OracleJobHandler";
export { MySQLJobHandler } from "./mysql/MySQLJobHandler";
export { DatabaseAdminJobHandler } from "./database-admin/DatabaseAdminJobHandler";
export { SftpJobHandler } from "./sftp/SftpJobHandler";
export { PdfDipaJobHandler } from "./pdf-dipa/PdfDipaJobHandler";
export { AdkProcessingJobHandler } from "./adk-processing/AdkProcessingJobHandler";

// Import handlers for registration
import { OracleJobHandler } from "./oracle/OracleJobHandler";
import { MySQLJobHandler } from "./mysql/MySQLJobHandler";
import { DatabaseAdminJobHandler } from "./database-admin/DatabaseAdminJobHandler";
import { SftpJobHandler } from "./sftp/SftpJobHandler";
import { PdfDipaJobHandler } from "./pdf-dipa/PdfDipaJobHandler";
import { AdkProcessingJobHandler } from "./adk-processing/AdkProcessingJobHandler";
import { jobHandlerFactory } from "./factory/JobHandlerFactory";

/**
 * Register all job handlers with the factory
 * This function should be called during application initialization
 */
export function registerAllJobHandlers(): void {
  // Register Oracle handler
  jobHandlerFactory.registerHandler("oracle", OracleJobHandler);
  
  // Register MySQL handler
  jobHandlerFactory.registerHandler("mysql", MySQLJobHandler);
  
  // Register Database Admin handler
  jobHandlerFactory.registerHandler("database_admin", DatabaseAdminJobHandler);
  
  // Register SFTP handler
  jobHandlerFactory.registerHandler("sftp", SftpJobHandler);
  
  // Register PDF DIPA handler
  jobHandlerFactory.registerHandler("pdf_dipa", PdfDipaJobHandler);
  
  // Register ADK Processing handler
  jobHandlerFactory.registerHandler("adk_processing", AdkProcessingJobHandler);
}

/**
 * Get information about all registered handlers
 */
export function getRegisteredHandlersInfo(): Array<{
  jobType: string;
  isRegistered: boolean;
  isInstantiated: boolean;
  handlerClass?: string;
}> {
  const supportedTypes = jobHandlerFactory.getSupportedJobTypes();
  return supportedTypes.map(jobType => jobHandlerFactory.getHandlerInfo(jobType));
}

/**
 * Validate all registered handlers
 */
export function validateAllRegisteredHandlers(): Array<{
  jobType: string;
  isValid: boolean;
  error?: string;
}> {
  return jobHandlerFactory.validateAllHandlers();
}

/**
 * Check if a job type is supported
 */
export function isJobTypeSupported(jobType: string): boolean {
  return jobHandlerFactory.isJobTypeSupported(jobType);
}

/**
 * Get all supported job types
 */
export function getSupportedJobTypes(): string[] {
  return jobHandlerFactory.getSupportedJobTypes();
}

// Auto-register all handlers when this module is imported
registerAllJobHandlers();

// Log registration status
console.log("🔧 Job handlers registered:", getSupportedJobTypes().join(", "));

// Validate all handlers on startup
const validationResults = validateAllRegisteredHandlers();
const invalidHandlers = validationResults.filter(result => !result.isValid);

if (invalidHandlers.length > 0) {
  console.error("❌ Invalid job handlers detected:");
  invalidHandlers.forEach(handler => {
    console.error(`  - ${handler.jobType}: ${handler.error}`);
  });
} else {
  console.log("✅ All job handlers validated successfully");
}
