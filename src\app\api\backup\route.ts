import { NextRequest, NextResponse } from "next/server";
import {
  loadJobDefinitions,
  saveJobDefinition,
  loadJobExecutions,
} from "@/lib/jobPersistence";
import {
  loadAllSystemSettings,
  saveSystemSetting,
  loadAllAppConfigs,
  saveAppConfig,
  SystemSetting,
  AppConfig,
} from "@/lib/configPersistence";
import { logger } from "@/lib/jobManager";
import { JobDefinition, JobExecution } from "@/lib/jobManager";

interface BackupData {
  version: string;
  timestamp: string;
  jobDefinitions: JobDefinition[];
  systemSettings: SystemSetting[];
  appConfigs: AppConfig[];
  recentExecutions: JobExecution[];
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeExecutions = searchParams.get("includeExecutions") === "true";
    const executionLimit = parseInt(
      searchParams.get("executionLimit") || "100"
    );

    logger.info("Creating system backup...");

    // Load all data
    const jobDefinitions = await loadJobDefinitions();
    const systemSettings = await loadAllSystemSettings();
    const appConfigs = await loadAllAppConfigs();

    let recentExecutions: JobExecution[] = [];
    if (includeExecutions) {
      recentExecutions = await loadJobExecutions(undefined, executionLimit);
    }

    const backupData: BackupData = {
      version: "1.0.0",
      timestamp: new Date().toISOString(),
      jobDefinitions,
      systemSettings,
      appConfigs,
      recentExecutions,
    };

    logger.info(
      `System backup created with ${jobDefinitions.length} jobs, ${systemSettings.length} settings, and ${recentExecutions.length} executions`
    );

    // Set response headers for file download
    const filename = `sintesa-datapuller-backup-${
      new Date().toISOString().split("T")[0]
    }.json`;

    return new NextResponse(JSON.stringify(backupData, null, 2), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Content-Disposition": `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    logger.error("Failed to create backup:", error);
    return NextResponse.json(
      { error: "Failed to create system backup" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const backupData: BackupData = await request.json();

    if (!backupData.version || !backupData.timestamp) {
      return NextResponse.json(
        { error: "Invalid backup file format" },
        { status: 400 }
      );
    }

    logger.info(`Restoring system backup from ${backupData.timestamp}`);

    let restoredCount = 0;

    // Restore job definitions
    if (backupData.jobDefinitions && Array.isArray(backupData.jobDefinitions)) {
      for (const job of backupData.jobDefinitions) {
        try {
          await saveJobDefinition(job);
          restoredCount++;
        } catch (error) {
          logger.warn(`Failed to restore job definition ${job.id}:`, error);
        }
      }
    }

    // Restore system settings
    if (backupData.systemSettings && Array.isArray(backupData.systemSettings)) {
      for (const setting of backupData.systemSettings) {
        try {
          await saveSystemSetting(
            setting.category,
            setting.key,
            setting.value,
            setting.type,
            setting.description
          );
          restoredCount++;
        } catch (error) {
          logger.warn(
            `Failed to restore system setting ${setting.category}.${setting.key}:`,
            error
          );
        }
      }
    }

    // Restore app configs
    if (backupData.appConfigs && Array.isArray(backupData.appConfigs)) {
      for (const config of backupData.appConfigs) {
        try {
          await saveAppConfig(config.key, config.value, config.description);
          restoredCount++;
        } catch (error) {
          logger.warn(`Failed to restore app config ${config.key}:`, error);
        }
      }
    }

    logger.info(
      `System restore completed. Restored ${restoredCount} items from backup created on ${backupData.timestamp}`
    );

    return NextResponse.json({
      success: true,
      message: `Successfully restored ${restoredCount} items from backup`,
      backupInfo: {
        version: backupData.version,
        timestamp: backupData.timestamp,
        itemsRestored: restoredCount,
      },
    });
  } catch (error) {
    logger.error("Failed to restore backup:", error);
    return NextResponse.json(
      { error: "Failed to restore system backup" },
      { status: 500 }
    );
  }
}
