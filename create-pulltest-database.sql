-- Create the pulltest database and required system tables
-- Run this SQL script in your MySQL client

-- Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS pulltest 
  CHARACTER SET utf8mb4 
  COLLATE utf8mb4_unicode_ci;

-- Use the database
USE pulltest;

-- App configuration table
CREATE TABLE IF NOT EXISTS app_config (
  id INT PRIMARY KEY AUTO_INCREMENT,
  config_key VARCHAR(255) UNIQUE NOT NULL,
  config_value TEXT,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Job definitions table
CREATE TABLE IF NOT EXISTS job_definitions (
  id VARCHAR(36) PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(255) NOT NULL,
  description TEXT,
  schedule_cron VARCHAR(100) NOT NULL,
  enabled BOOLEAN DEFAULT TRUE,
  data_source_type ENUM('oracle', 'sftp', 'mysql', 'database_admin', 'pdf_dipa', 'adk_processing') NOT NULL,
  data_source_config JSON,
  destination_config JSON,
  retry_config JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_job_enabled (enabled),
  INDEX idx_job_type (data_source_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Job executions table
CREATE TABLE IF NOT EXISTS job_executions (
  id VARCHAR(100) PRIMARY KEY,
  job_id VARCHAR(36) NOT NULL,
  status ENUM('running', 'completed', 'failed', 'scheduled', 'stopped') NOT NULL,
  trigger_type ENUM('manual', 'automatic') NOT NULL,
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP NULL,
  duration_seconds INT NULL,
  records_processed INT NULL,
  error_message TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (job_id) REFERENCES job_definitions(id) ON DELETE CASCADE,
  INDEX idx_job_executions_job_id (job_id),
  INDEX idx_job_executions_status (status),
  INDEX idx_job_executions_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Job execution logs table
CREATE TABLE IF NOT EXISTS job_execution_logs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  execution_id VARCHAR(100) NOT NULL,
  log_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  log_level ENUM('info', 'warn', 'error', 'debug') DEFAULT 'info',
  message TEXT NOT NULL,
  FOREIGN KEY (execution_id) REFERENCES job_executions(id) ON DELETE CASCADE,
  INDEX idx_job_logs_execution_id (execution_id),
  INDEX idx_job_logs_timestamp (log_timestamp),
  INDEX idx_job_logs_level (log_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System settings table
CREATE TABLE IF NOT EXISTS system_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  setting_category VARCHAR(100) NOT NULL,
  setting_key VARCHAR(255) NOT NULL,
  setting_value TEXT,
  setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_category_key (setting_category, setting_key),
  INDEX idx_settings_category (setting_category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Job schedule history table
CREATE TABLE IF NOT EXISTS job_schedule_history (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  job_id VARCHAR(36) NOT NULL,
  scheduled_time TIMESTAMP NOT NULL,
  actual_start_time TIMESTAMP NULL,
  execution_id VARCHAR(100) NULL,
  status ENUM('scheduled', 'started', 'skipped', 'failed_to_start') NOT NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (job_id) REFERENCES job_definitions(id) ON DELETE CASCADE,
  FOREIGN KEY (execution_id) REFERENCES job_executions(id) ON DELETE SET NULL,
  INDEX idx_schedule_history_job_id (job_id),
  INDEX idx_schedule_history_scheduled_time (scheduled_time),
  INDEX idx_schedule_history_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- SFTP file transfers table
CREATE TABLE IF NOT EXISTS sftp_file_transfers (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  job_id VARCHAR(36) NOT NULL,
  execution_id VARCHAR(100) NOT NULL,
  remote_file_path VARCHAR(1000) NOT NULL,
  remote_file_name VARCHAR(500) NOT NULL,
  local_file_path VARCHAR(1000) NOT NULL,
  local_directory VARCHAR(500) NOT NULL,
  file_size_bytes BIGINT NULL,
  file_hash VARCHAR(64) NULL,
  transfer_status ENUM('pending', 'downloading', 'completed', 'failed', 'skipped') NOT NULL DEFAULT 'pending',
  transfer_start_time TIMESTAMP NULL,
  transfer_end_time TIMESTAMP NULL,
  transfer_duration_ms INT NULL,
  error_message TEXT NULL,
  file_modified_date TIMESTAMP NULL,
  file_created_date TIMESTAMP NULL,
  processing_status ENUM('not_processed', 'processing', 'processed', 'failed_processing') DEFAULT 'not_processed',
  processing_notes TEXT NULL,
  metadata JSON NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (job_id) REFERENCES job_definitions(id) ON DELETE CASCADE,
  FOREIGN KEY (execution_id) REFERENCES job_executions(id) ON DELETE CASCADE,
  INDEX idx_sftp_transfers_job_id (job_id),
  INDEX idx_sftp_transfers_execution_id (execution_id),
  INDEX idx_sftp_transfers_status (transfer_status),
  INDEX idx_sftp_transfers_processing_status (processing_status),
  INDEX idx_sftp_transfers_remote_file (remote_file_name),
  INDEX idx_sftp_transfers_created_at (created_at),
  UNIQUE KEY unique_execution_remote_file (execution_id, remote_file_path)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Show the created tables
SHOW TABLES;

-- Optional: Insert some default system settings
INSERT IGNORE INTO system_settings (setting_category, setting_key, setting_value, setting_type, description) VALUES
('scheduler', 'enabled', 'true', 'boolean', 'Whether the job scheduler is enabled'),
('scheduler', 'check_interval', '60', 'number', 'Interval in seconds to check for scheduled jobs'),
('system', 'log_retention_days', '30', 'number', 'Number of days to keep job execution logs'),
('database', 'cleanup_enabled', 'true', 'boolean', 'Whether automatic database cleanup is enabled');

-- Show successful creation message
SELECT 'Database pulltest and all required tables have been created successfully!' as message;
