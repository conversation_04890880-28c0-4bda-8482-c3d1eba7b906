# Sintesa Data Puller

A powerful, enterprise-grade data extraction and synchronization platform with advanced job management, real-time monitoring, and comprehensive automation capabilities. Built with Next.js, TypeScript, and MySQL for modern data integration workflows.

## 🌟 Key Features

### 🚀 Advanced Job Management

- **Multiple Data Source Support**: Oracle, MySQL, SFTP, Database Administration, PDF DIPA, and ADK Processing
- **Job Sequencing**: Execute jobs in defined sequences with dependency management and error handling
- **Smart Scheduling**: Cron-based scheduling with timezone support and local time configuration
- **Real-time Dashboard**: Live monitoring with Server-Sent Events for instant status updates
- **Configurable File Tracking**: Track downloaded files with customizable database storage

### 🔄 Data Sources & Processing

- **Oracle Database**: Advanced query support with connection pooling and schema management
- **MySQL Database**: Native MySQL connectivity with optimized performance
- **SFTP Servers**: Secure file transfer with pattern matching and directory management
- **Database Administration**: Complex table operations, data migrations, and maintenance tasks
- **PDF DIPA Processing**: Specialized PDF document processing and data extraction
- **ADK Processing**: Automated extraction and processing of compressed ADK archives

### 🎯 Destinations & Storage

- **Database Destinations**: Oracle, MySQL, PostgreSQL with automatic table creation
- **Local File System**: Organized file storage with directory management
- **File Tracking**: Configurable metadata tracking for audit trails and processing history

### 🛡️ Enterprise Features

- **Persistent Storage**: All configurations and execution history stored in MySQL
- **Crash Recovery**: Automatic state restoration after system restarts
- **Backup & Restore**: Complete system backup with configuration and history
- **Performance Monitoring**: Detailed execution metrics, success rates, and resource usage
- **Comprehensive Logging**: Structured logging with configurable levels and rotation

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18.0+ and npm
- **MySQL** 5.7+ or MariaDB 10.3+
- **Git** for repository cloning
- **Oracle Instant Client** (optional, for Oracle data sources)

### Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript, HeroUI, Tailwind CSS
- **Backend**: Node.js, MySQL2, Winston logging
- **Real-time**: Server-Sent Events (SSE)
- **Scheduling**: Node-cron with timezone support
- **Data Processing**: PDF parsing, XML processing, SFTP client

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd sintesa-datapuller
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment setup**

   Copy the example environment file and configure your settings:

   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` with your database and service credentials.

4. **Database initialization**

   ```bash
   npm run migrate
   ```

   This creates all necessary tables and production-ready default job configurations.

5. **Start the application**

   ```bash
   # Development mode
   npm run dev

   # Production mode
   npm run build && npm start
   ```

6. **Access the dashboard**

   Open [http://localhost:3000](http://localhost:3000) in your browser.

For detailed installation instructions, see [INSTALLATION.md](INSTALLATION.md).

## 🗄️ Database Architecture

The application uses a comprehensive MySQL database schema designed for scalability and reliability:

### Core Job Management Tables

- **`job_definitions`**: Complete job configurations including data sources, destinations, schedules, and sequence assignments
- **`job_executions`**: Execution history with status tracking, performance metrics, and error details
- **`job_execution_logs`**: Detailed step-by-step logs for debugging and monitoring
- **`job_schedule_history`**: Scheduled vs actual execution times for performance analysis

### Sequence Management Tables

- **`job_sequences`**: Sequence definitions with failure handling strategies and retry configurations
- **`job_sequence_executions`**: Sequence execution tracking with current job status and progress

### Configuration & Settings Tables

- **`system_settings`**: Categorized application settings (app, logging, notifications, database, scheduler)
- **`app_config`**: Application-specific configuration values and feature flags

### Supported Job Types

The system supports six distinct job types with specialized configurations:

1. **Oracle Database** (`oracle`) - Enterprise database connectivity
2. **MySQL Database** (`mysql`) - Native MySQL data extraction
3. **SFTP** (`sftp`) - Secure file transfer with tracking
4. **Database Administration** (`database_admin`) - Complex database operations
5. **PDF DIPA** (`pdf_dipa`) - PDF document processing
6. **ADK Processing** (`adk_processing`) - Compressed archive extraction

## 🎨 Dashboard & User Interface

### Modern Dashboard Design

- **Real-time Status Updates**: Live job monitoring with Server-Sent Events
- **Responsive Layout**: Optimized for desktop and mobile devices
- **Dark/Light Theme**: Automatic theme switching based on system preferences
- **Compact Design**: 4-column grid layout for efficient space utilization

### Job Management Interface

- **Individual Jobs Table**: Dedicated view for standalone jobs
- **Sequence Tables**: Separate tables for each job sequence with ordered execution
- **Quick Actions**: Run, stop, start, and cancel jobs with one-click actions
- **Status Indicators**: Color-coded status chips with real-time updates

### Advanced Features

- **Job Administration Modal**: Comprehensive job creation and editing interface
- **Sequence Management**: Visual sequence builder with drag-and-drop job ordering
- **Settings Panel**: Categorized configuration management with backup/restore
- **Performance Metrics**: Execution time tracking and success rate monitoring

### User Experience Enhancements

- **Text Truncation**: Smart text truncation with hover tooltips for long job names
- **Loading States**: Smooth loading animations and skeleton screens
- **Error Handling**: User-friendly error messages with actionable suggestions
- **Keyboard Navigation**: Full keyboard accessibility support

## 🔧 Configuration Management

### System Settings Categories

#### Application Settings (`app`)

- `max_concurrent_jobs`: Maximum concurrent job executions
- `job_timeout_minutes`: Default job execution timeout
- `enable_auto_cleanup`: Enable automatic cleanup of old data
- `cleanup_days_to_keep`: Days to retain execution history

#### Logging Settings (`logging`)

- `log_level`: Default logging level (debug, info, warn, error)
- `max_log_files`: Maximum number of log files to keep
- `max_log_size_mb`: Maximum size of each log file

#### Notification Settings (`notifications`)

- `enable_email_alerts`: Enable email notifications for failures
- `email_recipients`: List of email addresses for notifications
- `slack_webhook_url`: Slack webhook for notifications

#### Database Settings (`database`)

- `connection_timeout`: Database connection timeout
- `max_connections`: Maximum database connection pool size
- `retry_attempts`: Number of retry attempts for database operations

#### Scheduler Settings (`scheduler`)

- `enable_scheduler`: Enable/disable the job scheduler
- `timezone`: Timezone for cron scheduling
- `max_missed_runs`: Maximum missed runs before disabling a job

### Managing Settings

#### Web Interface

1. Click the **Settings** (⚙️) button in the dashboard header
2. Navigate through the different setting categories
3. Modify values as needed
4. Click **Save All Changes**
5. Restart the application for changes to take effect

#### API Endpoints

- `GET /api/settings`: Get all settings or by category
- `POST /api/settings`: Create or update a setting
- `DELETE /api/settings`: Delete a setting
- `PATCH /api/settings`: Batch update multiple settings

## 🔗 Advanced Job Sequencing

### Overview

Job Sequencing enables sophisticated workflow orchestration where jobs execute in predefined order with dependency management. Perfect for ETL pipelines, data processing workflows, and complex automation scenarios.

### Enhanced Features

- **Sequential Execution**: Jobs run in strict order with dependency validation
- **Smart Error Handling**: Configurable failure strategies (stop, continue, retry)
- **Flexible Scheduling**: Independent sequence scheduling with cron expressions
- **Real-time Progress Tracking**: Live monitoring of sequence execution status
- **Dynamic Job Assignment**: Add/remove jobs from sequences without downtime
- **Backward Compatibility**: Existing standalone jobs remain fully functional

### Dashboard Integration

- **Separate Tables**: Individual jobs and sequence jobs displayed in dedicated sections
- **Sequence Status**: First job shows next scheduled time instead of dependency status
- **Progress Indicators**: Visual progress tracking with current job highlighting
- **Quick Actions**: Start, stop, and monitor sequences directly from dashboard

### Creating and Managing Sequences

1. **Access Sequence Management**

   - Open Job Administration modal
   - Navigate to "Sequences" tab

2. **Create New Sequence**

   - Define sequence metadata (name, description, schedule)
   - Configure failure handling:
     - **Stop**: Halt sequence on any job failure (recommended for critical workflows)
     - **Continue**: Proceed to next job despite failures (for non-critical processing)
     - **Retry**: Retry failed jobs with configurable retry limits

3. **Job Assignment**
   - Assign jobs to sequences via API or UI
   - Jobs automatically transition between individual and sequence modes
   - Real-time dashboard updates reflect changes immediately

### API Integration

```javascript
// Create a sequence
const sequence = {
  id: "etl-pipeline-v2",
  name: "Enhanced ETL Pipeline",
  description: "Daily data processing with error recovery",
  schedule: "0 2 * * *", // Daily at 2 AM
  onFailure: "retry",
  maxRetries: 2,
  enabled: true,
};

// Assign jobs to sequence with ordering
await fetch("/api/admin/jobs/extract-job/sequence", {
  method: "PUT",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({ sequenceId: "etl-pipeline-v2", order: 1 }),
});
```

For comprehensive documentation, see [Job Sequencing Guide](docs/JOB_SEQUENCING.md).

## 🔌 Supported Job Types

### 1. Oracle Database Jobs

- **Purpose**: Extract data from Oracle databases with complex queries
- **Features**: Connection pooling, schema support, transaction management
- **Configuration**: Host, port, service name, credentials, SQL queries
- **Use Cases**: Enterprise data warehousing, legacy system integration

### 2. MySQL Database Jobs

- **Purpose**: Native MySQL data extraction and synchronization
- **Features**: Optimized MySQL connectivity, connection limits, timeout configuration
- **Configuration**: Database connection details, custom queries, performance tuning
- **Use Cases**: Application database synchronization, reporting data extraction

### 3. SFTP File Transfer Jobs

- **Purpose**: Secure file download and processing from remote servers
- **Features**: Pattern matching, directory management, configurable file tracking
- **Configuration**: SSH credentials, remote paths, file patterns, local destinations
- **Use Cases**: Daily file imports, automated report downloads, data file processing

### 4. Database Administration Jobs

- **Purpose**: Complex database operations and maintenance tasks
- **Features**: Table management, data migrations, bulk operations
- **Configuration**: Operation modes, SQL scripts, table management settings
- **Use Cases**: Database maintenance, data cleanup, schema migrations

### 5. PDF DIPA Processing Jobs

- **Purpose**: Specialized PDF document processing and data extraction
- **Features**: PDF parsing, content extraction, structured data output
- **Configuration**: PDF source paths, extraction rules, output formats
- **Use Cases**: Document processing workflows, automated data entry

### 6. ADK Processing Jobs

- **Purpose**: Extract and process XML files from compressed ADK archives
- **Features**: Archive extraction, XML processing, file filtering
- **Configuration**: Source directories, extraction tools, file filters
- **Use Cases**: Government data processing, bulk archive handling

## 💾 Backup & Restore

### Creating Backups

#### Web Interface

1. Click the **Settings** (⚙️) button in the dashboard
2. Click **Export Backup** in the modal header
3. A JSON file will be downloaded with the current date

#### API Endpoint

```bash
curl -X GET "http://localhost:3000/api/backup?includeExecutions=false" \
     -H "Accept: application/json" \
     -o backup-$(date +%Y-%m-%d).json
```

### Restoring Backups

#### Web Interface

1. Click the **Settings** (⚙️) button in the dashboard
2. Click **Import Backup** and select your backup file
3. Confirm the restore operation

#### API Endpoint

```bash
curl -X POST "http://localhost:3000/api/backup" \
     -H "Content-Type: application/json" \
     -d @backup-2025-01-15.json
```

### Backup Contents

- Job definitions and configurations
- System settings and application config
- Optionally: Recent job execution history

## 🔄 Job Management

### Job States

- **Running**: Currently executing
- **Scheduled**: Enabled and waiting for next run
- **Stopped**: Disabled or manually stopped
- **Completed**: Last execution finished successfully
- **Failed**: Last execution encountered an error

### Job Actions

- **Run Now**: Execute job immediately
- **Stop**: Disable job scheduling
- **Start**: Enable job scheduling
- **Cancel**: Terminate running job
- **View Details**: See execution logs and history

### Job Configuration

Each job includes:

- **Basic Info**: Name, description, enabled status
- **Schedule**: Cron expression for timing
- **Data Source**: Connection details and queries/file patterns
- **Destination**: Where to store the extracted data
- **Retry Config**: Error handling and retry logic

## �️ Crash Recovery

The application automatically recovers from crashes by:

1. **State Persistence**: All job states are saved to the database
2. **Automatic Restoration**: On startup, the app loads previous state
3. **Execution Tracking**: Running jobs are properly marked as failed if interrupted
4. **Log Continuity**: All logs are preserved in the database
5. **Setting Restoration**: Application settings are maintained across restarts

### Recovery Process

1. Application starts up
2. Database connection is established
3. Job definitions are loaded from persistent storage
4. Recent execution history is restored
5. System settings are applied
6. Scheduler is restarted with previous configuration

## � API Reference

### Jobs API

- `GET /api/jobs`: Get all job statuses
- `POST /api/jobs`: Execute job actions (run, stop, start, cancel)
- `GET /api/jobs/events`: Server-Sent Events for real-time updates

### Job History API

- `GET /api/jobs/history`: Get job execution history
- `DELETE /api/jobs/history`: Cleanup old job data

### Settings API

- `GET /api/settings`: Get system settings
- `POST /api/settings`: Create/update settings
- `DELETE /api/settings`: Delete settings
- `PATCH /api/settings`: Batch update settings

### Backup API

- `GET /api/backup`: Export system backup
- `POST /api/backup`: Import/restore system backup

### System API

- `GET /api/system`: Get system status
- `POST /api/system`: Start system services
- `DELETE /api/system`: Stop system services

## 🔍 Monitoring & Troubleshooting

### Log Files

- `logs/combined.log`: All application logs
- `logs/error.log`: Error logs only

### Real-time Monitoring

- Dashboard shows live job status updates
- Server-Sent Events provide instant notifications
- Connection status indicator shows real-time connectivity

### Performance Metrics

- Job execution duration
- Success/failure rates
- Records processed per job
- System resource usage

## 🛠️ Development

### Project Architecture

```
src/
├── app/                    # Next.js 15 App Router
│   ├── api/               # RESTful API endpoints
│   │   ├── admin/         # Job administration APIs
│   │   ├── backup/        # Backup/restore functionality
│   │   ├── jobs/          # Job management APIs
│   │   ├── settings/      # Configuration APIs
│   │   └── system/        # System status APIs
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout component
│   ├── page.tsx           # Main dashboard
│   └── providers.tsx      # Context providers
├── components/            # React components
│   ├── JobAdminModal/     # Job administration interface
│   ├── SettingsModal/     # Settings management
│   ├── EnhancedJobsDisplay.tsx # Advanced job display
│   ├── SchedulerControls.tsx   # Job control interface
│   └── StatusChip.tsx     # Status indicators
├── lib/                   # Core business logic
│   ├── database.ts        # MySQL connection & schema
│   ├── jobManager.ts      # Job orchestration
│   ├── jobPersistence.ts  # Data access layer
│   ├── jobSequenceManager.ts # Sequence management
│   ├── cronScheduler.ts   # Scheduling engine
│   ├── configPersistence.ts # Settings management
│   └── startup.ts         # Application initialization
├── hooks/                 # Custom React hooks
│   ├── useJobsSSE.ts     # Server-Sent Events
│   └── useSystemStatus.ts # System monitoring
└── types/                 # TypeScript definitions
    └── job.ts            # Job-related types
```

### Core Components

- **JobManager**: Centralized job orchestration and execution
- **JobSequenceManager**: Advanced sequence workflow management
- **CronScheduler**: Timezone-aware job scheduling
- **Database Layer**: Optimized MySQL operations with connection pooling
- **Real-time Engine**: Server-Sent Events for live updates
- **Settings Framework**: Categorized configuration management

### Adding New Job Types

1. **Extend Type Definitions**

   ```typescript
   // Add to JobDefinition interface in lib/jobManager.ts
   dataSource: {
     type: "oracle" | "sftp" | "mysql" | "your_new_type";
     your_new_type?: {
       // Configuration properties
     };
   }
   ```

2. **Update Database Schema**

   ```sql
   -- Add to job_definitions table enum
   ALTER TABLE job_definitions
   MODIFY data_source_type ENUM('oracle', 'sftp', 'mysql', 'database_admin', 'pdf_dipa', 'adk_processing', 'your_new_type');
   ```

3. **Implement Job Runner Logic**

   ```typescript
   // Add case in lib/jobRunner.ts
   case "your_new_type":
     return await executeYourNewTypeJob(jobDefinition, execution);
   ```

4. **Create UI Components**
   - Add configuration form in `components/JobAdminModal/`
   - Update job type selection dropdown
   - Add validation and help text

## 🤝 Contributing

We welcome contributions to improve the Sintesa Data Puller! Here's how to get started:

### Development Setup

1. **Fork and Clone**

   ```bash
   git fork <repository-url>
   git clone <your-fork-url>
   cd sintesa-datapuller
   ```

2. **Install Dependencies**

   ```bash
   npm install
   ```

3. **Setup Development Environment**

   ```bash
   cp .env.example .env.local
   # Edit .env.local with your development database credentials
   npm run migrate
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

### Contribution Guidelines

- **Code Style**: Follow TypeScript best practices and use Prettier for formatting
- **Testing**: Add unit tests for new features and ensure existing tests pass
- **Documentation**: Update README.md and relevant documentation for new features
- **Commit Messages**: Use conventional commit format (feat:, fix:, docs:, etc.)
- **Pull Requests**: Provide clear description of changes and link related issues

### Areas for Contribution

- **New Job Types**: Add support for additional data sources (PostgreSQL, MongoDB, APIs)
- **UI Improvements**: Enhance dashboard design and user experience
- **Performance**: Optimize database queries and job execution
- **Testing**: Improve test coverage and add integration tests
- **Documentation**: Expand guides and add video tutorials

## 📞 Support & Troubleshooting

### Getting Help

1. **Check Documentation**

   - [Installation Guide](INSTALLATION.md)
   - [Job Configuration](ORACLE_SFTP_CONFIG.md)
   - [Job Sequencing Guide](docs/JOB_SEQUENCING.md)

2. **Review Logs**

   - Application logs: `logs/combined.log`
   - Error logs: `logs/error.log`
   - Database connection issues in startup logs

3. **Verify Configuration**

   - Environment variables in `.env.local`
   - Database connectivity and permissions
   - Job configurations in admin panel

4. **Use Built-in Tools**
   - Backup/restore for configuration recovery
   - Real-time connection status in dashboard
   - System status API for health checks

### Common Issues

- **Database Connection**: Verify MySQL service and credentials
- **Job Failures**: Check job logs and configuration settings
- **Performance**: Monitor system resources and optimize queries
- **UI Issues**: Clear browser cache and check console errors

### Reporting Issues

When reporting issues, please include:

- Application version and environment details
- Error messages from logs
- Steps to reproduce the issue
- Expected vs actual behavior

## � License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
