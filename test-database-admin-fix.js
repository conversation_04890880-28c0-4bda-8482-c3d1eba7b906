// Test script to verify database admin job execution fix
const { executeJob } = require('./src/lib/jobRunner');

// Test job definition with the old problematic configuration
const testJobWithOldConfig = {
  id: "test-job-old-config",
  name: "Test Database Admin Job - Old Config",
  description: "Test job with old local destination config",
  schedule: "0 3 * * *",
  enabled: true,
  dataSource: {
    type: "database_admin",
    database_admin: {
      host: "localhost",
      port: 3306,
      database: "monev2025",
      username: "root",
      password: "",
      connectionLimit: 5,
      acquireTimeout: 60000,
      timeout: 120000,
      // Missing operationMode - should default to handling DDL operations
      operations: [],
      rawSql: "SELECT 1 as test_result;"
    }
  },
  destination: {
    type: "local",
    localPath: "" // Empty path - this was causing the error
  },
  retryConfig: {
    maxRetries: 2,
    retryDelay: 60
  }
};

// Test job definition with the new correct configuration
const testJobWithNewConfig = {
  id: "test-job-new-config",
  name: "Test Database Admin Job - New Config",
  description: "Test job with correct table_management config",
  schedule: "0 3 * * *",
  enabled: true,
  dataSource: {
    type: "database_admin",
    database_admin: {
      host: "localhost",
      port: 3306,
      database: "monev2025",
      username: "root",
      password: "",
      connectionLimit: 5,
      acquireTimeout: 60000,
      timeout: 120000,
      operationMode: "table_management",
      operations: [],
      rawSql: "SELECT 1 as test_result;"
    }
  },
  destination: {
    type: "database",
    database: {
      type: "mysql",
      host: "localhost",
      port: 3306,
      database: "monev2025",
      username: "root",
      password: "",
      table: "job_results"
    }
  },
  retryConfig: {
    maxRetries: 2,
    retryDelay: 60
  }
};

async function testDatabaseAdminFix() {
  console.log("Testing database admin job execution fix...");
  
  try {
    console.log("\n1. Testing job with old problematic configuration...");
    const result1 = await executeJob(testJobWithOldConfig);
    console.log("✅ Old config job executed successfully:", result1);
  } catch (error) {
    console.log("❌ Old config job failed:", error.message);
  }
  
  try {
    console.log("\n2. Testing job with new correct configuration...");
    const result2 = await executeJob(testJobWithNewConfig);
    console.log("✅ New config job executed successfully:", result2);
  } catch (error) {
    console.log("❌ New config job failed:", error.message);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testDatabaseAdminFix().catch(console.error);
}

module.exports = { testDatabaseAdminFix };
