import {
  getJobDefinition,
  createJ<PERSON><PERSON>xecution,
  updateJob<PERSON>tatus,
  addJob<PERSON><PERSON>,
  logger,
  JobDefinition,
  // ✅ REMOVED: Unused types - moved to modular handlers
} from "./jobManager";
import { isDataBotStarted } from "./startup";
import JobSequenceManager from "./jobSequenceManager";
import type { ChildProcess } from "child_process";
// ✅ REMOVED: Unused imports - functionality moved to modular handlers
// ✅ REMOVED: Unused imports - functionality moved to modular handlers

// Global tracking for running jobs and cancellation requests
const runningJobs = new Map<
  string,
  { executionId: string; cancelled: boolean }
>();
const jobCancellationRequests = new Set<string>();

// Global tracking for running processes that can be cancelled
const runningProcesses = new Map<string, ChildProcess>();

// Functions to allow job handlers to register their processes for cancellation
export function registerRunningProcess(
  jobId: string,
  process: ChildProcess
): void {
  runningProcesses.set(jobId, process);
  logger.info(`Registered running process for job ${jobId}`);
}

export function unregisterRunningProcess(jobId: string): void {
  runningProcesses.delete(jobId);
  logger.info(`Unregistered running process for job ${jobId}`);
}

// Cancellation support functions
export function requestJobCancellation(jobId: string): boolean {
  logger.info(`Cancellation requested for job ${jobId}`);
  jobCancellationRequests.add(jobId);

  // Kill any running processes for this job
  const process = runningProcesses.get(jobId);
  if (process) {
    try {
      // On Windows, this will forcefully terminate the process
      process.kill("SIGKILL");
      logger.info(`Killed running process for job ${jobId}`);
    } catch (error) {
      logger.error(`Failed to kill process for job ${jobId}`, error);
    }
    runningProcesses.delete(jobId);
  }

  // Mark the job as cancelled if it's currently running
  const runningJob = runningJobs.get(jobId);
  if (runningJob) {
    runningJob.cancelled = true;
    logger.info(`Job ${jobId} marked for cancellation`, {
      executionId: runningJob.executionId,
    });
    return true;
  }

  return false;
}

export function isJobCancelled(jobId: string): boolean {
  const runningJob = runningJobs.get(jobId);
  return runningJob?.cancelled === true || jobCancellationRequests.has(jobId);
}

export function clearJobCancellation(jobId: string): void {
  jobCancellationRequests.delete(jobId);
  runningJobs.delete(jobId);
  runningProcesses.delete(jobId);
}

// Clear all running job states (used when system stops)
export function clearAllRunningJobs(): void {
  logger.info(`Clearing ${runningJobs.size} running jobs due to system stop`);

  // Kill any running processes
  for (const [jobId, process] of runningProcesses) {
    try {
      process.kill("SIGKILL");
      logger.info(`Killed running process for job ${jobId} during system stop`);
    } catch (error) {
      logger.error(
        `Failed to kill process for job ${jobId} during system stop`,
        error
      );
    }
  }

  // Clear all tracking maps
  runningJobs.clear();
  jobCancellationRequests.clear();
  runningProcesses.clear();
}

// Get debug information about running jobs
export function getRunningJobsDebugInfo(): {
  runningJobCount: number;
  runningJobs: Array<{
    jobId: string;
    executionId: string;
    cancelled: boolean;
  }>;
  cancellationRequestCount: number;
  runningProcessCount: number;
} {
  const runningJobsArray = Array.from(runningJobs.entries()).map(
    ([jobId, jobInfo]) => ({
      jobId,
      executionId: jobInfo.executionId,
      cancelled: jobInfo.cancelled,
    })
  );

  return {
    runningJobCount: runningJobs.size,
    runningJobs: runningJobsArray,
    cancellationRequestCount: jobCancellationRequests.size,
    runningProcessCount: runningProcesses.size,
  };
}

// Helper function to check for cancellation during job execution
async function checkForCancellation(jobId: string): Promise<void> {
  if (isJobCancelled(jobId)) {
    await addJobLog(jobId, "Job cancellation requested by user");
    throw new Error("Job was cancelled by user");
  }
}

export async function runDataPullingJob(
  jobId: string,
  triggerType: "manual" | "automatic" = "automatic"
): Promise<boolean> {
  // Check if the system is started before executing any job
  if (!isDataBotStarted()) {
    logger.info(`System is not started, skipping job execution for ${jobId}`, {
      jobId,
      triggerType,
    });
    return false;
  }

  const jobDef = await getJobDefinition(jobId);

  if (!jobDef) {
    logger.error(`Job definition not found for jobId: ${jobId}`);
    return false;
  }

  if (!jobDef.enabled) {
    logger.info(`Job ${jobId} is disabled, skipping execution`);
    return false;
  }

  // Check if the job is already running
  if (runningJobs.has(jobId)) {
    const runningJob = runningJobs.get(jobId);
    logger.warn(`Job ${jobId} is already running, skipping execution`, {
      jobId,
      triggerType,
      runningExecutionId: runningJob?.executionId,
      runningJobCancelled: runningJob?.cancelled,
      totalRunningJobs: runningJobs.size,
    });
    return false;
  }

  // Clear any previous cancellation requests
  clearJobCancellation(jobId);

  const executionId = await createJobExecution(jobId, triggerType);

  // Track the running job
  runningJobs.set(jobId, { executionId, cancelled: false });

  // Track job completion status for sequence manager notification
  let jobSucceeded = false;
  let jobWasCancelled = false;

  try {
    logger.info(`Starting job execution for ${jobDef.name}`, {
      jobId,
      executionId,
    });
    await addJobLog(jobId, `Starting ${jobDef.name}...`);

    // Check for cancellation before starting
    await checkForCancellation(jobId);

    // Execute the data pulling job based on its configuration
    const result = await executeJob(jobDef);

    // Final cancellation check before marking as completed
    await checkForCancellation(jobId);

    await updateJobStatus(jobId, "completed", {
      recordsProcessed: result.recordsProcessed,
    });

    await addJobLog(
      jobId,
      `Job completed successfully. Processed ${result.recordsProcessed} records.`
    );

    logger.info(`Job ${jobId} completed successfully`, {
      jobId,
      executionId,
      recordsProcessed: result.recordsProcessed,
    });

    // Mark job as succeeded for sequence manager notification
    jobSucceeded = true;
    return true;
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error occurred";

    // Check if this was a cancellation
    jobWasCancelled = errorMessage.includes("cancelled by user");
    const status = jobWasCancelled ? "stopped" : "failed";

    // Always update the status - the updateJobStatus function will handle duplicates gracefully
    await updateJobStatus(jobId, status, {
      errorMessage: jobWasCancelled ? "Job cancelled by user" : errorMessage,
    });

    await addJobLog(
      jobId,
      jobWasCancelled ? "Job cancelled by user" : `Job failed: ${errorMessage}`
    );

    logger.info(`Job ${jobId} ${jobWasCancelled ? "cancelled" : "failed"}`, {
      jobId,
      executionId,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return false;
  } finally {
    // Always clean up the job tracking FIRST to prevent race conditions
    clearJobCancellation(jobId);

    logger.debug(`Job ${jobId} cleanup completed`, {
      jobId,
      executionId,
      runningJobsCount: runningJobs.size,
      jobSucceeded,
      jobWasCancelled,
    });

    // Notify sequence manager AFTER cleanup to prevent race conditions
    try {
      const sequenceManager = JobSequenceManager.getInstance();
      if (jobSucceeded) {
        await sequenceManager.onJobCompleted(jobId, true);
      } else if (!jobWasCancelled) {
        // Only notify for failures, not cancellations
        await sequenceManager.onJobCompleted(jobId, false);
      }
    } catch (seqError) {
      logger.error(
        `Failed to notify sequence manager of job completion:`,
        seqError
      );
      // Don't affect the job's completion status
    }
  }
}

interface JobResult {
  recordsProcessed: number;
  data?: unknown;
}

async function executeJob(jobDef: JobDefinition): Promise<JobResult> {
  // Import the modular job system
  const { jobHandlerFactory, JobExecutionContext } = await import("./jobs");

  await addJobLog(
    jobDef.id,
    `Connecting to ${jobDef.dataSource.type} data source...`
  );

  // Check for cancellation
  await checkForCancellation(jobDef.id);

  try {
    // Get the appropriate job handler
    const handler = jobHandlerFactory.getHandler(jobDef.dataSource.type);

    // Validate the job configuration
    if (!handler.validateConfig(jobDef)) {
      throw new Error(
        `Invalid configuration for job type: ${jobDef.dataSource.type}`
      );
    }

    // Create execution context
    const context = new JobExecutionContext(
      jobDef.id,
      `exec-${Date.now()}`,
      jobDef
    );

    await addJobLog(
      jobDef.id,
      `Using modular job handler: ${handler.constructor.name}`
    );

    // Execute the job using the handler
    const result = await handler.execute(context);

    await addJobLog(
      jobDef.id,
      `Job completed successfully. Records processed: ${result.recordsProcessed}`
    );

    // Handle destination saving based on job type
    if (result.data !== undefined && result.data !== null) {
      // For database_admin jobs, check if they should skip destination saving
      if (jobDef.dataSource.type === "database_admin") {
        const dbAdminConfig = jobDef.dataSource.database_admin;

        // Skip destination saving for table_management mode OR if destination is misconfigured
        if (
          dbAdminConfig?.operationMode === "table_management" ||
          (jobDef.destination.type === "local" && !jobDef.destination.localPath)
        ) {
          await addJobLog(
            jobDef.id,
            "DDL operations completed successfully - no data export required"
          );
        } else {
          // Save to destination for data extraction operations
          await saveToDestination(jobDef, result.data);
          await addJobLog(jobDef.id, "Data saved to destination successfully");
        }
      } else if (jobDef.dataSource.type === "pdf_dipa") {
        // PDF DIPA jobs handle their own destination saving
        await addJobLog(
          jobDef.id,
          "PDF DIPA extraction completed - data saved directly to destination"
        );
      } else if (jobDef.dataSource.type === "adk_processing") {
        // ADK processing jobs handle their own database operations directly
        await addJobLog(
          jobDef.id,
          "ADK processing completed - data saved directly to database tables"
        );
      } else if (
        jobDef.dataSource.type === "oracle" &&
        (result as { alreadySavedToDestination?: boolean })
          .alreadySavedToDestination
      ) {
        // Oracle streaming jobs handle their own destination saving
        await addJobLog(
          jobDef.id,
          "Oracle streaming completed - data saved directly to destination during processing"
        );
      } else {
        // For other job types, always save to destination
        await saveToDestination(jobDef, result.data);
        await addJobLog(jobDef.id, "Data saved to destination successfully");
      }
    } else {
      await addJobLog(jobDef.id, "No data to save to destination");
    }

    return result;
  } catch (error) {
    // Re-throw the error directly since legacy fallback has been removed
    const errorMessage = error instanceof Error ? error.message : String(error);

    await addJobLog(jobDef.id, `❌ Job execution failed: ${errorMessage}`);

    // Re-throw the original error to be handled by the calling function
    throw error;
  }
}

// ✅ REMOVED: pullFromOracle() - Now handled by OracleJobHandler

// ✅ REMOVED: pullFromMySQL() - Now handled by MySQLJobHandler

// ✅ REMOVED: analyzeSQLStatement() - Now handled by DatabaseAdminJobHandler

// ✅ REMOVED: checkTableExists() and getTableInfo() - Now handled by DatabaseAdminJobHandler

// ✅ REMOVED: executeDatabaseAdmin() - Now handled by DatabaseAdminJobHandler
// This function was ~600 lines and handled complex database operations
// All functionality moved to src/lib/jobs/database-admin/DatabaseAdminJobHandler.ts
// Function body removed - all functionality moved to DatabaseAdminJobHandler

// ✅ REMOVED: sortOperationsByDependencies() - Now handled by DatabaseAdminJobHandler

// ✅ REMOVED: generateSQLFromVisualQuery() - Now handled by DatabaseAdminJobHandler

// ✅ REMOVED: pullFromSFTP() - Now handled by SftpJobHandler
// This function was ~400 lines and handled SFTP operations
// All functionality moved to src/lib/jobs/sftp/SftpJobHandler.ts

// ✅ REMOVED: pullFromSFTP() - Now handled by SftpJobHandler
// Function body removed - all functionality moved to SftpJobHandler

async function saveToDestination(
  jobDef: JobDefinition,
  data: unknown
): Promise<void> {
  await addJobLog(jobDef.id, `Saving data to ${jobDef.destination.type}...`);

  switch (jobDef.destination.type) {
    case "database":
      await saveToDatabase(jobDef, data);
      break;
    case "file":
      await saveToFile(jobDef, data);
      break;
    case "local":
      await saveToLocal(jobDef, data);
      break;
    default:
      throw new Error(
        `Unsupported destination type: ${jobDef.destination.type}`
      );
  }
}

async function saveToDatabase(
  jobDef: JobDefinition,
  data: unknown
): Promise<void> {
  if (!jobDef.destination.database) {
    throw new Error(
      "Database configuration is required for database destination"
    );
  }

  const dbConfig = jobDef.destination.database;

  await addJobLog(
    jobDef.id,
    `Connecting to ${dbConfig.type} database at ${dbConfig.host}:${dbConfig.port}...`
  );

  if (dbConfig.type === "mysql") {
    await saveToMySQL(jobDef, data, dbConfig);
  } else {
    // For other database types, use the existing simulation
    await new Promise((resolve) => setTimeout(resolve, 1000));
    await addJobLog(
      jobDef.id,
      "Connected to destination database successfully"
    );

    const recordCount = Array.isArray(data) ? data.length : 1;
    await addJobLog(
      jobDef.id,
      `Inserting ${recordCount} records into ${dbConfig.table}...`
    );

    await new Promise((resolve) => setTimeout(resolve, 1500));

    await addJobLog(
      jobDef.id,
      `Data inserted into ${dbConfig.table} successfully`
    );
  }
}

async function saveToMySQL(
  jobDef: JobDefinition,
  data: unknown,
  dbConfig: NonNullable<JobDefinition["destination"]["database"]>
): Promise<void> {
  const mysql2 = await import("mysql2/promise");

  try {
    // Create connection
    const connection = await mysql2.createConnection({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.username,
      password: dbConfig.password,
      database: dbConfig.database,
    });

    await addJobLog(jobDef.id, "Connected to MySQL database successfully");

    // Check if table exists and create/recreate if needed
    await addJobLog(jobDef.id, `Checking if table ${dbConfig.table} exists...`);

    const [tableExists] = await connection.execute(
      `SELECT COUNT(*) as count FROM information_schema.tables 
       WHERE table_schema = ? AND table_name = ?`,
      [dbConfig.database, dbConfig.table]
    );

    interface TableExistsResult {
      count: number;
    }

    const tableExistsResult = (tableExists as TableExistsResult[])[0].count > 0;

    if (tableExistsResult) {
      // Table exists, truncate it to clear all data
      await addJobLog(
        jobDef.id,
        `Table ${dbConfig.table} exists, truncating to clear existing data...`
      );
      await connection.execute(`TRUNCATE TABLE \`${dbConfig.table}\``);
      await addJobLog(
        jobDef.id,
        `Table ${dbConfig.table} truncated successfully`
      );
    } else {
      await addJobLog(
        jobDef.id,
        `Table ${dbConfig.table} does not exist, creating...`
      );

      // Create table with generic schema based on first data row
      let createTableSQL = "";

      if (Array.isArray(data) && data.length > 0) {
        const firstRow = data[0] as Record<string, unknown>;
        const columns = Object.keys(firstRow)
          .map((key) => {
            // Simplified type detection: VARCHAR for most fields, DECIMAL for financial, DATETIME for time
            const value = firstRow[key];
            let sqlType = "VARCHAR(255)"; // Default to VARCHAR for most fields

            // Check if this is a financial/currency field
            const isFinancialField =
              /^(pagu|anggaran|budget|amount|nilai|harga|biaya|cost|rupiah)$/i.test(
                key
              );

            // Check if this is a time-related field
            const isTimeField =
              /^(date|time|created|updated|timestamp|tgl|tanggal)$/i.test(
                key
              ) || value instanceof Date;

            if (isFinancialField) {
              // Financial fields need higher precision: DECIMAL(20,2)
              sqlType = "DECIMAL(20,2)";
            } else if (isTimeField) {
              sqlType = "DATETIME";
            } else if (typeof value === "string" && value.length > 255) {
              // Only use TEXT/LONGTEXT for very long strings
              if (value.length < 65535) {
                sqlType = "TEXT";
              } else {
                sqlType = "LONGTEXT";
              }
            }
            // All other fields (numbers, short strings, etc.) default to VARCHAR(255)

            return `\`${key}\` ${sqlType}`;
          })
          .join(", ");

        createTableSQL = `CREATE TABLE \`${dbConfig.table}\` (
          id INT AUTO_INCREMENT PRIMARY KEY,
          ${columns},
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`;
      } else {
        // Default table structure if no data
        createTableSQL = `CREATE TABLE \`${dbConfig.table}\` (
          id INT AUTO_INCREMENT PRIMARY KEY,
          data JSON,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`;
      }

      await connection.execute(createTableSQL);
      await addJobLog(
        jobDef.id,
        `Table ${dbConfig.table} created successfully`
      );
    }

    // Insert data
    if (Array.isArray(data) && data.length > 0) {
      const recordCount = data.length;
      await addJobLog(
        jobDef.id,
        `Inserting ${recordCount} records into ${dbConfig.table}...`
      );

      const firstRow = data[0] as Record<string, unknown>;
      const columns = Object.keys(firstRow);

      // Choose insertion method based on dataset size
      if (recordCount > 10000) {
        await addJobLog(
          jobDef.id,
          `Large dataset detected (${recordCount} records). Using batch insertion for better performance...`
        );
        await insertDataInBatches(
          connection,
          dbConfig.table,
          columns,
          data,
          jobDef.id
        );
      } else {
        await addJobLog(
          jobDef.id,
          `Using row-by-row insertion for ${recordCount} records...`
        );
        await insertDataRowByRow(
          connection,
          dbConfig.table,
          columns,
          data,
          jobDef.id
        );
      }

      await addJobLog(
        jobDef.id,
        `Successfully inserted ${recordCount} records`
      );
    } else {
      await addJobLog(jobDef.id, "No data to insert");
    }

    await connection.end();
    await addJobLog(jobDef.id, "MySQL connection closed");
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown MySQL error";
    await addJobLog(jobDef.id, `MySQL operation failed: ${errorMessage}`);
    throw new Error(`MySQL database operation failed: ${errorMessage}`);
  }
}

async function saveToFile(jobDef: JobDefinition, data: unknown): Promise<void> {
  const filePath = jobDef.destination.localPath || "./data/output/";

  // In a real implementation, you would write to file system
  await new Promise((resolve) => setTimeout(resolve, 500));

  const recordCount = Array.isArray(data) ? data.length : 1;
  await addJobLog(jobDef.id, `Writing ${recordCount} records to file...`);

  await addJobLog(jobDef.id, `Data written to ${filePath} successfully`);
}

async function saveToLocal(
  jobDef: JobDefinition,
  data: unknown
): Promise<void> {
  if (!jobDef.destination.localPath) {
    throw new Error("Local path is required for local file destination");
  }

  const localPath = jobDef.destination.localPath;

  await addJobLog(jobDef.id, `Saving files to local directory: ${localPath}`);

  // Handle ADK RKAKL bulk download result
  if (jobDef.id === "4" && data && typeof data === "object" && "type" in data) {
    const bulkResult = data as {
      type: string;
      unitsProcessed: number;
      status: string;
      downloadPath: string;
      filesDownloaded: number;
      errors: string[];
    };

    if (bulkResult.type === "bulk_download") {
      await addJobLog(
        jobDef.id,
        `ADK RKAKL auto-discovery download completed: Found ${bulkResult.filesDownloaded} files total`
      );

      await addJobLog(
        jobDef.id,
        `Files have been downloaded to their respective organizational folders under: ${bulkResult.downloadPath}`
      );

      if (bulkResult.errors.length > 0) {
        await addJobLog(
          jobDef.id,
          `Encountered ${bulkResult.errors.length} errors during download`
        );
      } else {
        await addJobLog(
          jobDef.id,
          "All files downloaded successfully without errors"
        );
      }

      return;
    }
  }

  // In a real implementation, you would save files to local filesystem
  // This would involve creating directories if they don't exist,
  // and writing the downloaded files or processed data

  await new Promise((resolve) => setTimeout(resolve, 800));

  const fileCount = Array.isArray(data) ? data.length : 1;
  await addJobLog(
    jobDef.id,
    `Saved ${fileCount} files to ${localPath} successfully`
  );
}

// ✅ REMOVED: pullFromPdfDipa() - Now handled by PdfDipaJobHandler
// This function was ~130 lines and handled PDF processing
// All functionality moved to src/lib/jobs/pdf-dipa/PdfDipaJobHandler.ts
// Function body removed - all functionality moved to PdfDipaJobHandler

// ✅ REMOVED: parsePdfFile() - Now handled by PdfDipaJobHandler

// ✅ REMOVED: PDF helper functions and interfaces - Now handled by PdfDipaJobHandler

// ✅ REMOVED: saveErrorLog() - Now handled by PdfDipaJobHandler

// ✅ REMOVED: AdkFileListResult interface - Now handled by AdkProcessingJobHandler

// ✅ REMOVED: pullFromAdkProcessing() - Now handled by AdkProcessingJobHandler
// This function was ~570 lines and handled ADK XML processing
// All functionality moved to src/lib/jobs/adk-processing/AdkProcessingJobHandler.ts

// ✅ REMOVED: browseFolderRecursively() - Now handled by SftpJobHandler

// ✅ REMOVED: createSftpFileMetadata() - Now handled by SftpJobHandler

/**
 * Insert data using batch/bulk insertion for better performance with large datasets
 */
async function insertDataInBatches(
  connection: import("mysql2/promise").Connection,
  tableName: string,
  columns: string[],
  data: unknown[],
  jobId: string
): Promise<void> {
  const batchSize = 1000; // Insert 1000 records per batch
  const totalRecords = data.length;
  let insertedCount = 0;

  // Prepare the base INSERT statement
  const placeholders = columns.map(() => "?").join(", ");
  const baseInsertSQL = `INSERT INTO \`${tableName}\` (\`${columns.join(
    "`, `"
  )}\`) VALUES `;

  for (let i = 0; i < totalRecords; i += batchSize) {
    const batch = data.slice(i, i + batchSize);
    const batchValues: unknown[] = [];
    const valuePlaceholders: string[] = [];

    // Build batch INSERT statement
    for (const row of batch) {
      const rowData = row as Record<string, unknown>;
      const processedValues = columns.map((col) =>
        processOracleValue(rowData[col])
      );
      batchValues.push(...processedValues);
      valuePlaceholders.push(`(${placeholders})`);
    }

    const batchInsertSQL = baseInsertSQL + valuePlaceholders.join(", ");

    try {
      await connection.execute(batchInsertSQL, batchValues);
      insertedCount += batch.length;

      const percentage = ((insertedCount / totalRecords) * 100).toFixed(1);
      await addJobLog(
        jobId,
        `Batch progress: ${insertedCount}/${totalRecords} records inserted (${percentage}%)`
      );
    } catch (_error) {
      // If batch fails, fall back to row-by-row for this batch
      await addJobLog(
        jobId,
        `Batch insertion failed, falling back to row-by-row for ${batch.length} records...`
      );

      for (const row of batch) {
        const rowData = row as Record<string, unknown>;
        const values = columns.map((col) => processOracleValue(rowData[col]));
        const singleInsertSQL = `INSERT INTO \`${tableName}\` (\`${columns.join(
          "`, `"
        )}\`) VALUES (${placeholders})`;

        try {
          await connection.execute(singleInsertSQL, values);
          insertedCount++;
        } catch (rowError) {
          console.error(
            `Failed to insert row: ${JSON.stringify(row)}`,
            rowError
          );
          // Continue with next row instead of failing entire job
        }
      }
    }
  }
}

/**
 * Insert data row by row (for smaller datasets)
 */
async function insertDataRowByRow(
  connection: import("mysql2/promise").Connection,
  tableName: string,
  columns: string[],
  data: unknown[],
  jobId: string
): Promise<void> {
  const placeholders = columns.map(() => "?").join(", ");
  const insertSQL = `INSERT INTO \`${tableName}\` (\`${columns.join(
    "`, `"
  )}\`) VALUES (${placeholders})`;

  let insertedCount = 0;
  const progressInterval = 1000;
  const totalRecords = data.length;

  for (const row of data) {
    const rowData = row as Record<string, unknown>;
    const values = columns.map((col) => processOracleValue(rowData[col]));

    try {
      await connection.execute(insertSQL, values);
      insertedCount++;

      // Log progress every 1000 records
      if (insertedCount % progressInterval === 0) {
        const percentage = ((insertedCount / totalRecords) * 100).toFixed(1);
        await addJobLog(
          jobId,
          `Progress: ${insertedCount}/${totalRecords} records inserted (${percentage}%)`
        );
      }
    } catch (_error) {
      console.error(`Error inserting row: ${JSON.stringify(row)}`, _error);
      // Continue with next row instead of failing entire job
    }
  }
}

/**
 * Process Oracle values for MySQL insertion
 */
function processOracleValue(value: unknown): unknown {
  // Handle null values
  if (value === null || value === undefined) {
    return null;
  }

  // Handle Date objects properly for MySQL DATETIME columns
  if (value instanceof Date) {
    return value.toISOString().slice(0, 19).replace("T", " ");
  }

  // Handle string dates that might be in JavaScript Date format
  if (
    typeof value === "string" &&
    /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun)/.test(value)
  ) {
    try {
      const dateObj = new Date(value);
      if (!isNaN(dateObj.getTime())) {
        return dateObj.toISOString().slice(0, 19).replace("T", " ");
      }
    } catch (error) {
      console.warn(`Failed to parse date string: ${value}`);
    }
  }

  return value;
}
