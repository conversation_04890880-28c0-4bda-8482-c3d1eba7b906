# Upgrade Guide - Configurable File Tracking System

This guide helps existing users upgrade to the new configurable file tracking system.

## 🔄 What Changed

### Before (Legacy System)
- ❌ Hardcoded `monev2024.file_metadata` database
- ❌ Fixed table structure
- ❌ No per-job configuration
- ❌ Legacy `metadataDatabase.ts` system

### After (New System)
- ✅ Configurable database per job
- ✅ Custom table names
- ✅ Optional file tracking
- ✅ Cleaner architecture
- ✅ Better performance

## 🚀 Upgrade Steps

### 1. Backup Your Data

```sql
-- Backup existing file metadata
CREATE TABLE file_metadata_backup AS SELECT * FROM monev2024.file_metadata;

-- Backup job definitions
mysqldump -u root -p sintesa_datapuller job_definitions > job_definitions_backup.sql
```

### 2. Update Your Code

```bash
# Pull the latest changes
git pull origin main

# Install any new dependencies
npm install
```

### 3. Update Existing Jobs

#### Option A: Use Job Administration UI
1. Go to **Job Administration**
2. Edit your existing SFTP jobs
3. Enable **File Tracking** toggle
4. Configure database settings:
   ```
   Database Host: localhost
   Database Port: 3306
   Database Name: monev2024
   Username: your_db_user
   Password: your_db_password
   Table Name: file_metadata
   ```

#### Option B: Update Database Directly

```sql
-- Update Job 4 to use new configurable file tracking
UPDATE job_definitions 
SET destination_config = JSON_SET(
  destination_config,
  '$.fileTracking', JSON_OBJECT(
    'enabled', true,
    'database', JSON_OBJECT(
      'host', 'localhost',
      'port', 3306,
      'username', 'root',
      'password', 'your_password',
      'database', 'monev2024',
      'table', 'file_metadata'
    )
  )
)
WHERE id = '4';
```

### 4. Test the Upgrade

1. **Start the application**:
   ```bash
   npm run dev
   ```

2. **Test SFTP job with file tracking**:
   - Go to Dashboard
   - Run Job 4 (or your SFTP job)
   - Verify files are tracked in your configured database

3. **Check logs**:
   ```bash
   tail -f logs/combined.log
   ```

## 🔧 Migration Script Updates

The migration script has been updated to create jobs with the new system:

### New Migration Output
```
🎉 Migration completed successfully!

📋 Summary:
   - Created 6 database tables
   - Initialized default system settings
   - Created 2 sample job definitions:
     • Job 1: Oracle data pulling (Tarik Pagu Real)
     • Job 4: SFTP bulk download with configurable file tracking

🔧 File Tracking:
   - Job 4 configured with monev2024.file_metadata tracking
   - File tracking is fully configurable per job
   - Create custom tracking databases as needed
```

### For Fresh Installations
New installations will automatically get the updated system. No additional steps needed.

## 🛠️ Troubleshooting

### Issue: Job not tracking files
**Solution**: Check job configuration has file tracking enabled:
```sql
SELECT id, name, JSON_EXTRACT(destination_config, '$.fileTracking.enabled') as tracking_enabled 
FROM job_definitions 
WHERE data_source_type = 'sftp';
```

### Issue: Database connection errors
**Solution**: Verify database credentials in job configuration:
```sql
SELECT id, name, JSON_EXTRACT(destination_config, '$.fileTracking.database') as db_config 
FROM job_definitions 
WHERE id = '4';
```

### Issue: Table not created automatically
**Solution**: Check database permissions:
```sql
-- Grant table creation permissions
GRANT CREATE ON monev2024.* TO 'your_user'@'localhost';
FLUSH PRIVILEGES;
```

## 📊 Benefits After Upgrade

### Flexibility
- ✅ **Multiple databases** - Different jobs can use different tracking databases
- ✅ **Custom schemas** - Use your own table naming conventions
- ✅ **Optional tracking** - Disable tracking for jobs that don't need it

### Performance
- ✅ **Connection pooling** - Efficient database connections
- ✅ **Reduced overhead** - No unnecessary connections
- ✅ **Better error handling** - Graceful failure recovery

### Maintenance
- ✅ **Cleaner code** - Removed legacy systems
- ✅ **Easier debugging** - Clear configuration paths
- ✅ **Future-proof** - Easy to extend and modify

## 🔮 Future Enhancements

The new system enables future features:
- **Multi-database support** - PostgreSQL, SQL Server support
- **Cloud database integration** - AWS RDS, Azure SQL, etc.
- **Advanced tracking** - File processing status, custom metadata
- **Audit trails** - Complete file lifecycle tracking

## 📞 Support

If you encounter issues during the upgrade:

1. **Check logs**: `logs/error.log` and `logs/combined.log`
2. **Verify configuration**: Use Job Administration UI
3. **Test connections**: Use built-in health checks
4. **Restore backup**: If needed, restore from your backup

## ✅ Upgrade Checklist

- [ ] Backup existing data
- [ ] Update code to latest version
- [ ] Configure file tracking for existing SFTP jobs
- [ ] Test job execution with file tracking
- [ ] Verify file metadata is being saved
- [ ] Check application logs for errors
- [ ] Update any custom scripts or integrations

---

**🎉 Upgrade Complete!** Your Sintesa Data Puller now uses the new configurable file tracking system.
