# Oracle and SFTP Data Pulling Configuration

This document explains how to configure your data pulling jobs to work with Oracle databases and SFTP servers.

## Prerequisites

For production use, you'll need to install the following packages:

```bash
# For Oracle database connectivity
npm install oracledb

# For SFTP connectivity
npm install ssh2-sftp-client

# For additional file system operations
npm install fs-extra
```

## Job Configuration Schema

### Oracle Database Source

```typescript
{
  dataSource: {
    type: "oracle",
    oracle: {
      host: "your-oracle-host.com",
      port: 1521,
      serviceName: "XEPDB1", // or SID
      username: "your_username",
      password: "your_password",
      query: "SELECT * FROM your_table WHERE condition",
      schema: "SCHEMA_NAME" // optional
    }
  }
}
```

### SFTP Source

```typescript
{
  dataSource: {
    type: "sftp",
    sftp: {
      host: "sftp.yourcompany.com",
      port: 22,
      username: "your_username",
      password: "your_password", // or use privateKey
      privateKey: "/path/to/private/key", // alternative to password
      remotePath: "/data/exports/",
      filePattern: "*.csv" // optional file pattern
    }
  }
}
```

### Destination Types

#### Database Destination

```typescript
{
  destination: {
    type: "database",
    database: {
      type: "oracle", // or "postgres", "mysql"
      host: "target-db-host.com",
      port: 1521,
      database: "target_database",
      username: "target_user",
      password: "target_password",
      table: "target_table",
      schema: "TARGET_SCHEMA" // optional
    }
  }
}
```

#### Local File Destination

```typescript
{
  destination: {
    type: "local",
    localPath: "./data/downloads/",
    options: {
      createDirectories: true,
      preserveTimestamp: true
    }
  }
}
```

#### File Destination (Processed Data)

```typescript
{
  destination: {
    type: "file",
    localPath: "./data/processed/",
    options: {
      format: "json", // or "csv", "xml"
      timestampFiles: true
    }
  }
}
```

## Environment Variables

Create a `.env` file based on `.env.example` with your actual credentials:

### Oracle Database Variables

- `ORACLE_HOST` - Oracle database hostname
- `ORACLE_PORT` - Oracle database port (default: 1521)
- `ORACLE_SERVICE_NAME` - Oracle service name or SID
- `ORACLE_USERNAME` - Database username
- `ORACLE_PASSWORD` - Database password

### SFTP Variables

- `SFTP_HOST` - SFTP server hostname
- `SFTP_PORT` - SFTP server port (default: 22)
- `SFTP_USERNAME` - SFTP username
- `SFTP_PASSWORD` - SFTP password
- `SFTP_PRIVATE_KEY_PATH` - Path to SSH private key (alternative to password)

## Example Job Definitions

### Daily Sales Data from Oracle

```typescript
{
  id: "daily-sales",
  name: "Daily Sales Data",
  description: "Pull daily sales data from Oracle CRM database",
  schedule: "0 2 * * *", // Daily at 2 AM
  enabled: true,
  dataSource: {
    type: "oracle",
    oracle: {
      host: process.env.ORACLE_HOST,
      port: parseInt(process.env.ORACLE_PORT || "1521"),
      serviceName: process.env.ORACLE_SERVICE_NAME,
      username: process.env.ORACLE_USERNAME,
      password: process.env.ORACLE_PASSWORD,
      query: `
        SELECT order_id, customer_id, order_date, total_amount, status
        FROM sales_orders
        WHERE order_date >= TRUNC(SYSDATE) - 1
        AND order_date < TRUNC(SYSDATE)
      `,
      schema: "CRM_SCHEMA"
    }
  },
  destination: {
    type: "database",
    database: {
      type: "oracle",
      host: process.env.TARGET_ORACLE_HOST,
      port: parseInt(process.env.TARGET_ORACLE_PORT || "1521"),
      database: process.env.TARGET_ORACLE_SERVICE,
      username: process.env.TARGET_ORACLE_USER,
      password: process.env.TARGET_ORACLE_PASS,
      table: "daily_sales",
      schema: "DW_SCHEMA"
    }
  },
  retryConfig: {
    maxRetries: 3,
    retryDelay: 300
  }
}
```

### Customer Files from SFTP

```typescript
{
  id: "customer-files",
  name: "Customer Data Files",
  description: "Download customer data files from SFTP server",
  schedule: "30 3 * * *", // Daily at 3:30 AM
  enabled: true,
  dataSource: {
    type: "sftp",
    sftp: {
      host: process.env.SFTP_HOST,
      port: parseInt(process.env.SFTP_PORT || "22"),
      username: process.env.SFTP_USERNAME,
      password: process.env.SFTP_PASSWORD,
      remotePath: "/exports/customer_data/",
      filePattern: "customer_*.csv"
    }
  },
  destination: {
    type: "local",
    localPath: "./data/customer_imports/",
    options: {
      createDirectories: true,
      preserveTimestamp: true
    }
  },
  retryConfig: {
    maxRetries: 2,
    retryDelay: 600
  }
}
```

## Security Best Practices

1. **Never commit credentials to version control**
2. **Use environment variables for all sensitive data**
3. **Consider using SSH keys instead of passwords for SFTP**
4. **Implement proper network security (VPN, firewall rules)**
5. **Use database roles with minimal required permissions**
6. **Enable Oracle database encryption in transit (TLS/SSL)**
7. **Regularly rotate passwords and keys**

## Cron Schedule Examples

- `0 2 * * *` - Daily at 2:00 AM
- `30 3 * * *` - Daily at 3:30 AM
- `*/15 * * * *` - Every 15 minutes
- `0 */6 * * *` - Every 6 hours
- `0 2 * * 1` - Every Monday at 2:00 AM
- `0 0 1 * *` - First day of every month at midnight

## Troubleshooting

### Oracle Connection Issues

1. Verify Oracle TNS configuration
2. Check network connectivity (`telnet oracle-host 1521`)
3. Ensure Oracle Instant Client is installed
4. Verify service name vs SID usage
5. Check Oracle database logs

### SFTP Connection Issues

1. Test SSH connectivity (`ssh user@sftp-host`)
2. Verify SFTP service is running
3. Check SSH key permissions (600 for private keys)
4. Ensure user has access to remote directory
5. Verify file patterns match actual files

### General Issues

1. Check application logs in `logs/` directory
2. Verify environment variables are loaded
3. Test with manual job execution
4. Check system resources (disk space, memory)
5. Validate cron expressions

## Production Implementation

To use real Oracle and SFTP connections instead of mock data:

1. Install required packages:

   ```bash
   npm install oracledb ssh2-sftp-client fs-extra
   ```

2. Uncomment the actual implementation code in `jobRunner.ts`

3. Update the Oracle connection code:

   ```typescript
   import oracledb from "oracledb";

   const connection = await oracledb.getConnection({
     user: oracleConfig.username,
     password: oracleConfig.password,
     connectString: `${oracleConfig.host}:${oracleConfig.port}/${oracleConfig.serviceName}`,
   });
   ```

4. Update the SFTP connection code:

   ```typescript
   import Client from "ssh2-sftp-client";

   const sftp = new Client();
   await sftp.connect({
     host: sftpConfig.host,
     port: sftpConfig.port,
     username: sftpConfig.username,
     password: sftpConfig.password,
   });
   ```

The current implementation uses mock data for demonstration and development purposes.
