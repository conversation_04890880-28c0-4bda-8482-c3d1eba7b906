import { useState, useEffect, useCallback } from "react";
import { ViewMode } from "../utils/types";

export const useModalState = (isOpen: boolean) => {
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [activeTab, setActiveTab] = useState("basic");
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [independentJobsExpanded, setIndependentJobsExpanded] = useState(true);
  const [sequenceJobsExpanded, setSequenceJobsExpanded] = useState(true);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setViewMode("list");
      setActiveTab("basic");
    }
  }, [isOpen]);

  const resetModalState = useCallback(() => {
    setViewMode("list");
    setActiveTab("basic");
  }, []);

  const switchToCreateJob = () => {
    setViewMode("create");
    setActiveTab("basic");
  };

  const switchToEditJob = () => {
    setViewMode("edit");
    setActiveTab("basic");
  };

  const switchToSequences = () => {
    setViewMode("sequences");
    setActiveTab("basic");
  };

  const switchToCreateSequence = () => {
    setViewMode("create-sequence");
    setActiveTab("basic");
  };

  const switchToEditSequence = () => {
    setViewMode("edit-sequence");
    setActiveTab("basic");
  };

  const switchToList = () => {
    setViewMode("list");
    setActiveTab("basic");
  };

  return {
    // State
    viewMode,
    activeTab,
    isFullscreen,
    independentJobsExpanded,
    sequenceJobsExpanded,

    // Setters
    setViewMode,
    setActiveTab,
    setIsFullscreen,
    setIndependentJobsExpanded,
    setSequenceJobsExpanded,

    // Actions
    resetModalState,
    switchToCreateJob,
    switchToEditJob,
    switchToSequences,
    switchToCreateSequence,
    switchToEditSequence,
    switchToList,
  };
};
