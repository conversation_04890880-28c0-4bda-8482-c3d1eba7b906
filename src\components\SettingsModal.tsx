// ============================================================================
// IMPORTS
// ============================================================================

// React imports
import React, { useState, useEffect } from "react";

// UI library imports
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
  Input,
  Select,
  SelectItem,
  Textarea,
  Tabs,
  Tab,
  Card,
  CardBody,
  CardHeader,
  Chip,
  Spinner,
  addToast,
} from "@heroui/react";

// Icon imports
import {
  Save,
  Download,
  Upload,
  AlertTriangle,
  Settings,
  Database,
  RefreshCw,
  AlertCircle,
} from "lucide-react";

// Type imports
import { JobDefinition } from "@/lib/jobManager";

// Interface for batch job update response
interface BatchJobUpdateResult {
  jobId: string;
  success: boolean;
  error?: string;
}

interface BatchJobUpdateResponse {
  message: string;
  results: BatchJobUpdateResult[];
  schedulerUpdates: number;
  atomic?: boolean;
  rollback?: boolean;
}

// Configuration options for different job types
interface JobConfigOption {
  key: string;
  label: string;
  type: "string" | "boolean" | "number" | "array";
  placeholder?: string;
  description?: string;
  validation?: (value: string | boolean | number | string[]) => string | null;
}

interface JobConfigSection {
  title: string;
  description: string;
  options: JobConfigOption[];
}

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface SystemSetting {
  category: string;
  key: string;
  value: string;
  type: "string" | "number" | "boolean" | "json";
  description?: string;
}

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// ============================================================================
// CONSTANTS
// ============================================================================

const SETTING_CATEGORIES = {
  app: "Application",
  logging: "Logging",
  notifications: "Notifications",
  database: "Database",
  scheduler: "Scheduler",
  jobs: "Jobs",
  system: "System",
};

// Helper function to get configurable options for a job
const getJobConfigOptions = (job: JobDefinition): JobConfigSection[] => {
  const sections: JobConfigSection[] = [];

  // SFTP jobs with auto-discovery (like job 4)
  if (job.dataSource.type === "sftp" && job.dataSource.options) {
    sections.push({
      title: "Test Path Configuration",
      description:
        "Configure a specific path to test with instead of processing all folders. Leave empty to process all organizational folders.",
      options: [
        {
          key: "testPath",
          label: "Test Path",
          type: "string",
          placeholder: "e.g., 34/152/2025 or 34 or 34/152",
          description: "Format: 2digit/3digit/year (e.g., 34/152/2025)",
          validation: (value: string | boolean | number | string[]) => {
            if (!value) return null; // Empty is valid
            if (typeof value !== "string") return "Value must be a string";
            const pathParts = value.split("/");
            if (pathParts.length > 3) return "Path can have at most 3 parts";
            if (pathParts[0] && !/^[0-9]{2}$/.test(pathParts[0])) {
              return "First part must be a 2-digit number";
            }
            if (pathParts[1] && !/^[0-9]{3}$/.test(pathParts[1])) {
              return "Second part must be a 3-digit number";
            }
            if (pathParts[2] && !/^[0-9]{4}$/.test(pathParts[2])) {
              return "Third part must be a 4-digit year";
            }
            return null;
          },
        },
      ],
    });

    // Legacy organizational units (if they exist)
    if (job.dataSource.options.organisationalUnits) {
      sections.push({
        title: "Legacy Organizational Units",
        description: "No longer used - folders are auto-discovered",
        options: [
          {
            key: "organisationalUnits",
            label: "Organizational Units",
            type: "array",
            description:
              "Configure which organizational units to download from the SFTP server",
          },
        ],
      });
    }
  }

  return sections;
};

// Helper function to check if a job has configurable options
// const _isJobConfigurable = (job: JobDefinition): boolean => {
//   return getJobConfigOptions(job).length > 0;
// };

// Helper function to get the value of a configuration option
const getConfigValue = (job: JobDefinition, key: string): unknown => {
  return job.dataSource.options?.[key];
};

// Helper function to update a configuration value
const updateConfigValue = (
  editingJobs: Record<string, JobDefinition>,
  setEditingJobs: React.Dispatch<
    React.SetStateAction<Record<string, JobDefinition>>
  >,
  jobId: string,
  key: string,
  value: unknown
) => {
  setEditingJobs((prev) => ({
    ...prev,
    [jobId]: {
      ...prev[jobId],
      dataSource: {
        ...prev[jobId]?.dataSource,
        options: {
          ...prev[jobId]?.dataSource?.options,
          [key]: value || undefined,
        },
      },
    },
  }));
};

export function SettingsModal({ isOpen, onClose }: SettingsModalProps) {
  const [settings, setSettings] = useState<SystemSetting[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("app");
  const [error, setError] = useState<string | null>(null);
  const [jobsError, setJobsError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [lastLoadTime, setLastLoadTime] = useState<Date | null>(null);
  const [editingSettings, setEditingSettings] = useState<
    Record<string, SystemSetting>
  >({});
  const [editingJobs, setEditingJobs] = useState<Record<string, JobDefinition>>(
    {}
  );
  const [resetting, setResetting] = useState(false);
  const [showResetConfirm, setShowResetConfirm] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadSettings();
      loadJobs();
    }
  }, [isOpen]);

  const loadSettings = async (isRetry = false) => {
    setLoading(true);
    setError(null);

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch("/api/settings", {
        signal: controller.signal,
        headers: {
          "Cache-Control": "no-cache",
          Pragma: "no-cache",
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorMessage = `Failed to load settings (${response.status})`;

        if (response.status === 404) {
          errorMessage =
            "Settings endpoint not found. Please check if the server is running correctly.";
        } else if (response.status === 500) {
          errorMessage =
            "Server error while loading settings. Please try again or contact support.";
        } else if (response.status === 403) {
          errorMessage =
            "Access denied. You may not have permission to view settings.";
        } else if (response.status >= 400 && response.status < 500) {
          errorMessage =
            "Client error while loading settings. Please refresh and try again.";
        } else if (response.status >= 500) {
          errorMessage =
            "Server error while loading settings. Please try again later.";
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();

      if (!data || typeof data !== "object") {
        throw new Error("Invalid response format from settings API");
      }

      if (!Array.isArray(data.settings)) {
        throw new Error("Settings data is not in expected format");
      }

      setSettings(data.settings);
      setLastLoadTime(new Date());

      // Initialize editing state
      const editingState: Record<string, SystemSetting> = {};
      data.settings.forEach((setting: SystemSetting) => {
        if (setting && setting.category && setting.key) {
          editingState[`${setting.category}.${setting.key}`] = { ...setting };
        }
      });
      setEditingSettings(editingState);

      // Reset retry count on success
      setRetryCount(0);

      if (isRetry) {
        addToast({
          title: "Settings Refreshed",
          description: "Settings loaded successfully after retry!",
          color: "success",
          timeout: 3000,
        });
      }
    } catch (error) {
      console.error("Failed to load settings:", error);

      let errorMessage = "Failed to load settings. Please try again.";

      if (error instanceof Error) {
        if (error.name === "AbortError") {
          errorMessage =
            "Request timed out. Please check your connection and try again.";
        } else if (error.message.includes("fetch")) {
          errorMessage =
            "Network error. Please check your connection and try again.";
        } else {
          errorMessage = error.message;
        }
      }

      setError(errorMessage);
      setRetryCount((prev) => prev + 1);
    } finally {
      setLoading(false);
    }
  };

  const loadJobs = async (isRetry = false) => {
    setJobsError(null);

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch("/api/admin/jobs", {
        signal: controller.signal,
        headers: {
          "Cache-Control": "no-cache",
          Pragma: "no-cache",
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorMessage = `Failed to load jobs (${response.status})`;

        if (response.status === 404) {
          errorMessage =
            "Jobs endpoint not found. Please check if the server is running correctly.";
        } else if (response.status === 500) {
          errorMessage =
            "Server error while loading jobs. Please try again or contact support.";
        } else if (response.status === 403) {
          errorMessage =
            "Access denied. You may not have permission to view jobs.";
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();

      if (!data || typeof data !== "object") {
        throw new Error("Invalid response format from jobs API");
      }

      if (!Array.isArray(data.jobs)) {
        throw new Error("Jobs data is not in expected format");
      }

      // Initialize editing state for jobs
      const editingJobState: Record<string, JobDefinition> = {};
      data.jobs.forEach((job: JobDefinition) => {
        if (job && job.id) {
          editingJobState[job.id] = { ...job };
        }
      });
      setEditingJobs(editingJobState);

      if (isRetry) {
        addToast({
          title: "Jobs Refreshed",
          description: "Jobs loaded successfully after retry!",
          color: "success",
          timeout: 3000,
        });
      }
    } catch (error) {
      console.error("Failed to load jobs:", error);

      let errorMessage =
        "Failed to load jobs. Some job configurations may not be available.";

      if (error instanceof Error) {
        if (error.name === "AbortError") {
          errorMessage =
            "Jobs request timed out. Please check your connection and try again.";
        } else if (error.message.includes("fetch")) {
          errorMessage =
            "Network error while loading jobs. Please check your connection.";
        } else {
          errorMessage = error.message;
        }
      }

      setJobsError(errorMessage);
    }
  };

  const retryLoad = async () => {
    await Promise.all([loadSettings(true), loadJobs(true)]);
  };

  const saveSettings = async () => {
    setSaving(true);
    setError(null);

    try {
      // Save system settings
      const settingsToSave = Object.values(editingSettings);
      const settingsResponse = await fetch("/api/settings", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ settings: settingsToSave }),
      });

      if (!settingsResponse.ok) {
        const errorText = await settingsResponse.text();
        throw new Error(
          `Failed to save settings (${settingsResponse.status}): ${errorText}`
        );
      }

      // Save job changes using batch endpoint
      const jobsToSave = Object.values(editingJobs);
      if (jobsToSave.length > 0) {
        const jobsResponse = await fetch("/api/admin/jobs", {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ jobs: jobsToSave }),
        });

        if (!jobsResponse.ok) {
          const errorData =
            (await jobsResponse.json()) as BatchJobUpdateResponse & {
              error?: string;
              details?: string[];
            };

          // Handle atomic transaction failures
          if (errorData.atomic && errorData.rollback) {
            const failedJobs =
              errorData.results?.filter((r) => !r.success) || [];
            const errorDetails =
              failedJobs.length > 0
                ? ` First failure: ${failedJobs[0].error}`
                : "";
            throw new Error(
              `All job updates were rolled back due to transaction failure.${errorDetails}`
            );
          } else {
            // Handle other types of failures
            const errorMessage =
              errorData.error || errorData.message || "Unknown error";
            const details = errorData.details
              ? ` Details: ${errorData.details.join("; ")}`
              : "";
            throw new Error(
              `Failed to save jobs (${jobsResponse.status}): ${errorMessage}${details}`
            );
          }
        }
      }

      // Refresh data
      await loadSettings();
      await loadJobs();

      addToast({
        title: "Settings Saved",
        description: "All settings saved successfully!",
        color: "success",
        timeout: 3000,
      });
    } catch (error) {
      console.error("Failed to save settings:", error);

      let errorMessage = "Failed to save settings. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (
    category: string,
    key: string,
    field: keyof SystemSetting,
    value: string
  ) => {
    const settingKey = `${category}.${key}`;
    setEditingSettings((prev) => ({
      ...prev,
      [settingKey]: {
        ...prev[settingKey],
        [field]: value,
      },
    }));
  };

  const updateJobOrganizationalUnits = (jobId: string, units: string[]) => {
    setEditingJobs((prev) => ({
      ...prev,
      [jobId]: {
        ...prev[jobId],
        dataSource: {
          ...prev[jobId]?.dataSource,
          options: {
            ...prev[jobId]?.dataSource?.options,
            organisationalUnits: units,
          },
        },
      },
    }));
  };

  const addOrganizationalUnit = (jobId: string, unit: string) => {
    const job = editingJobs[jobId];
    if (!job?.dataSource?.options) return;

    const currentUnits =
      (job.dataSource.options?.organisationalUnits as string[]) || [];
    if (!currentUnits.includes(unit) && unit.trim()) {
      updateJobOrganizationalUnits(jobId, [...currentUnits, unit.trim()]);
    }
  };

  const removeOrganizationalUnit = (jobId: string, unitToRemove: string) => {
    const job = editingJobs[jobId];
    if (!job?.dataSource?.options) return;

    const currentUnits =
      (job.dataSource.options?.organisationalUnits as string[]) || [];
    updateJobOrganizationalUnits(
      jobId,
      currentUnits.filter((unit: string) => unit !== unitToRemove)
    );
  };

  const exportBackup = async () => {
    try {
      const response = await fetch("/api/backup?includeExecutions=false");
      if (!response.ok) {
        throw new Error(
          `Export failed (${response.status}): ${response.statusText}`
        );
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `sintesa-datapuller-backup-${
        new Date().toISOString().split("T")[0]
      }.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      addToast({
        title: "Export Successful",
        description: "Backup exported successfully!",
        color: "success",
        timeout: 3000,
      });
    } catch (error) {
      console.error("Failed to export backup:", error);

      let errorMessage = "Failed to export backup. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    }
  };

  const importBackup = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      if (!file.name.endsWith(".json")) {
        throw new Error("Please select a valid JSON backup file.");
      }

      const text = await file.text();

      let backupData;
      try {
        backupData = JSON.parse(text);
      } catch {
        throw new Error("Invalid JSON format. Please check the backup file.");
      }

      if (!backupData || typeof backupData !== "object") {
        throw new Error("Invalid backup file format.");
      }

      const response = await fetch("/api/backup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(backupData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Restore failed (${response.status}): ${errorText}`);
      }

      await loadSettings();
      await loadJobs();

      addToast({
        title: "Import Successful",
        description: "Backup restored successfully!",
        color: "success",
        timeout: 3000,
      });
    } catch (error) {
      console.error("Failed to import backup:", error);

      let errorMessage =
        "Failed to restore backup. Please check the file format.";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    }

    // Reset the file input
    event.target.value = "";
  };

  const resetToDefaults = async () => {
    setResetting(true);
    setError(null);

    try {
      const response = await fetch("/api/settings/reset", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Reset failed (${response.status}): ${errorText}`);
      }

      await loadSettings();
      await loadJobs();

      addToast({
        title: "Reset Successful",
        description: "Settings reset to defaults successfully!",
        color: "success",
        timeout: 3000,
      });
      setShowResetConfirm(false);
    } catch (error) {
      console.error("Failed to reset settings:", error);

      let errorMessage = "Failed to reset settings. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setResetting(false);
    }
  };

  const getSettingsByCategory = (category: string) => {
    return settings.filter((setting) => setting.category === category);
  };

  const renderSettingInput = (setting: SystemSetting) => {
    const settingKey = `${setting.category}.${setting.key}`;
    const editingSetting = editingSettings[settingKey] || setting;

    switch (setting.type) {
      case "boolean":
        return (
          <Select
            selectedKeys={[editingSetting.value]}
            onSelectionChange={(keys) => {
              const value = Array.from(keys)[0] as string;
              updateSetting(setting.category, setting.key, "value", value);
            }}
          >
            <SelectItem key="true">True</SelectItem>
            <SelectItem key="false">False</SelectItem>
          </Select>
        );
      case "number":
        return (
          <Input
            type="number"
            value={editingSetting.value}
            onChange={(e) =>
              updateSetting(
                setting.category,
                setting.key,
                "value",
                e.target.value
              )
            }
          />
        );
      case "json":
        return (
          <Textarea
            value={editingSetting.value}
            onChange={(e) =>
              updateSetting(
                setting.category,
                setting.key,
                "value",
                e.target.value
              )
            }
            placeholder="Enter valid JSON"
            rows={3}
          />
        );
      default:
        return (
          <Input
            value={editingSetting.value}
            onChange={(e) =>
              updateSetting(
                setting.category,
                setting.key,
                "value",
                e.target.value
              )
            }
          />
        );
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="5xl"
      scrollBehavior="inside"
      backdrop="blur"
      hideCloseButton={true}
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-3 pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              <span>System Settings</span>
              {lastLoadTime && (
                <span className="text-xs text-gray-500 ml-3">
                  Last updated: {lastLoadTime.toLocaleString()}
                </span>
              )}
            </div>
            <Button
              size="sm"
              variant="flat"
              startContent={<RefreshCw className="w-4 h-4" />}
              onPress={retryLoad}
              isLoading={loading}
            >
              Refresh
            </Button>
          </div>

          <div className="flex items-center gap-2 text-amber-600 text-sm">
            <AlertTriangle size={14} />
            <span>Changes require application restart to take effect</span>
          </div>

          {/* Tabs moved to header */}
          <Tabs
            selectedKey={activeTab}
            onSelectionChange={(key) => setActiveTab(key as string)}
            className="w-full"
            classNames={{
              tabList: "w-full",
              tab: "flex-1",
            }}
          >
            {Object.entries(SETTING_CATEGORIES).map(([key, label]) => (
              <Tab key={key} title={label} />
            ))}
          </Tabs>
        </ModalHeader>
        <ModalBody className="gap-6">
          {/* Error Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <h4 className="text-red-800 font-medium mb-1">
                    Settings Error
                  </h4>
                  <p className="text-red-700 text-sm">{error}</p>
                  {retryCount > 0 && (
                    <p className="text-red-600 text-xs mt-1">
                      Retry attempt: {retryCount}
                    </p>
                  )}
                  <div className="flex gap-2 mt-3">
                    <Button
                      size="sm"
                      color="danger"
                      variant="flat"
                      onPress={retryLoad}
                      isLoading={loading}
                    >
                      Retry
                    </Button>
                    <Button
                      size="sm"
                      variant="flat"
                      onPress={() => setError(null)}
                    >
                      Dismiss
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Jobs Error Messages */}
          {jobsError && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-amber-500 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <h4 className="text-amber-800 font-medium mb-1">
                    Jobs Warning
                  </h4>
                  <p className="text-amber-700 text-sm">{jobsError}</p>
                  <div className="flex gap-2 mt-3">
                    <Button
                      size="sm"
                      color="warning"
                      variant="flat"
                      onPress={() => loadJobs(true)}
                    >
                      Retry Jobs
                    </Button>
                    <Button
                      size="sm"
                      variant="flat"
                      onPress={() => setJobsError(null)}
                    >
                      Dismiss
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Spinner size="lg" />
              <span className="ml-2">Loading settings...</span>
              {lastLoadTime && (
                <span className="ml-2 text-sm text-gray-500">
                  (Last loaded: {lastLoadTime.toLocaleTimeString()})
                </span>
              )}
            </div>
          ) : (
            <>
              {/* Tab content */}
              {activeTab === "system" ? (
                <div className="space-y-4">
                  {/* Export Configuration */}
                  <Card>
                    <CardBody className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Download className="w-5 h-5 text-blue-500" />
                          <div>
                            <h4 className="font-medium">
                              Export Configuration
                            </h4>
                            <p className="text-xs text-gray-500">
                              Download backup of settings and jobs
                            </p>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          color="primary"
                          startContent={<Download size={14} />}
                          onPress={exportBackup}
                        >
                          Export
                        </Button>
                      </div>
                      <div className="p-2 bg-blue-50 rounded text-xs text-blue-700">
                        Excludes execution history for smaller file size
                      </div>
                    </CardBody>
                  </Card>

                  {/* Import Configuration */}
                  <Card>
                    <CardBody className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Upload className="w-5 h-5 text-green-500" />
                          <div>
                            <h4 className="font-medium">
                              Import Configuration
                            </h4>
                            <p className="text-xs text-gray-500">
                              Restore settings from backup file
                            </p>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          color="success"
                          startContent={<Upload size={14} />}
                          onPress={() =>
                            document.getElementById("backup-import")?.click()
                          }
                        >
                          Import
                        </Button>
                        <input
                          id="backup-import"
                          type="file"
                          accept=".json"
                          style={{ display: "none" }}
                          onChange={importBackup}
                        />
                      </div>
                      <div className="p-2 bg-amber-50 rounded text-xs text-amber-700">
                        <strong>Warning:</strong> Will overwrite all current
                        settings
                      </div>
                    </CardBody>
                  </Card>

                  {/* Reset to Defaults */}
                  <Card>
                    <CardBody className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <RefreshCw className="w-5 h-5 text-red-500" />
                          <div>
                            <h4 className="font-medium">Reset to Defaults</h4>
                            <p className="text-xs text-gray-500">
                              Restore original system configuration
                            </p>
                          </div>
                        </div>
                        {!showResetConfirm && (
                          <Button
                            size="sm"
                            color="danger"
                            variant="flat"
                            startContent={<RefreshCw size={14} />}
                            onPress={() => setShowResetConfirm(true)}
                          >
                            Reset
                          </Button>
                        )}
                      </div>

                      {showResetConfirm && (
                        <div className="space-y-2">
                          <div className="p-3 bg-red-50 border border-red-200 rounded">
                            <div className="flex items-start gap-2">
                              <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                              <div>
                                <p className="text-red-700 text-xs">
                                  <strong>Confirm:</strong> Reset all settings
                                  to defaults? This cannot be undone and will
                                  permanently delete all custom configurations.
                                </p>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              color="danger"
                              startContent={<RefreshCw size={14} />}
                              onPress={resetToDefaults}
                              isLoading={resetting}
                            >
                              Yes, Reset All
                            </Button>
                            <Button
                              size="sm"
                              variant="flat"
                              onPress={() => setShowResetConfirm(false)}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      )}

                      <div className="p-2 bg-red-50 rounded text-xs text-red-700">
                        <strong>Danger:</strong> Irreversible action - export
                        backup first
                      </div>
                    </CardBody>
                  </Card>
                </div>
              ) : activeTab === "jobs" ? (
                <div className="space-y-4">
                  {/* Dynamic Job Configuration */}
                  {Object.values(editingJobs)
                    .filter((job) => getJobConfigOptions(job).length > 0)
                    .flatMap((job) => {
                      const configSections = getJobConfigOptions(job);
                      return configSections.map((section, sectionIndex) => (
                        <Card key={`${job.id}-${sectionIndex}`}>
                          <CardHeader>
                            <div className="flex items-center justify-between w-full">
                              <div className="flex items-center gap-2">
                                <Database className="w-5 h-5 text-green-500" />
                                <div>
                                  <h4 className="font-semibold text-lg">
                                    {job.name}
                                  </h4>
                                  <p className="text-sm text-gray-600">
                                    {section.title}
                                  </p>
                                </div>
                              </div>
                              <Chip size="sm" variant="flat" color="primary">
                                Job #{job.id}
                              </Chip>
                            </div>
                          </CardHeader>
                          <CardBody className="space-y-4">
                            <p className="text-sm text-gray-600">
                              {section.description}
                            </p>

                            <div className="space-y-3">
                              {section.options.map((option) => {
                                const currentValue = getConfigValue(
                                  job,
                                  option.key
                                );

                                if (option.type === "string") {
                                  return (
                                    <div
                                      key={option.key}
                                      className="flex items-center gap-2"
                                    >
                                      <Input
                                        label={option.label}
                                        placeholder={option.placeholder}
                                        value={String(currentValue || "")}
                                        onChange={(e) => {
                                          const value = e.target.value;
                                          updateConfigValue(
                                            editingJobs,
                                            setEditingJobs,
                                            job.id,
                                            option.key,
                                            value
                                          );
                                        }}
                                        className="flex-1"
                                        description={option.description}
                                      />
                                      <Button
                                        size="sm"
                                        color="warning"
                                        variant="flat"
                                        onPress={() => {
                                          updateConfigValue(
                                            editingJobs,
                                            setEditingJobs,
                                            job.id,
                                            option.key,
                                            undefined
                                          );
                                        }}
                                      >
                                        Clear
                                      </Button>
                                    </div>
                                  );
                                }

                                if (
                                  option.type === "array" &&
                                  option.key === "organisationalUnits"
                                ) {
                                  const units =
                                    (currentValue as string[]) || [];
                                  return (
                                    <div key={option.key} className="space-y-3">
                                      <div className="flex items-center gap-2">
                                        <Input
                                          id={`new-unit-${job.id}`}
                                          placeholder="Enter organizational unit code (e.g., 341522024)"
                                          onKeyDown={(e) => {
                                            if (e.key === "Enter") {
                                              const input =
                                                e.target as HTMLInputElement;
                                              addOrganizationalUnit(
                                                job.id,
                                                input.value
                                              );
                                              input.value = "";
                                            }
                                          }}
                                          className="flex-1"
                                        />
                                        <Button
                                          size="sm"
                                          color="primary"
                                          onPress={() => {
                                            const input =
                                              document.getElementById(
                                                `new-unit-${job.id}`
                                              ) as HTMLInputElement;
                                            if (input?.value) {
                                              addOrganizationalUnit(
                                                job.id,
                                                input.value
                                              );
                                              input.value = "";
                                            }
                                          }}
                                        >
                                          Add Unit
                                        </Button>
                                      </div>

                                      <div className="space-y-2">
                                        <p className="text-sm font-medium">
                                          Current Units ({units.length}):
                                        </p>
                                        <div className="flex flex-wrap gap-2 max-h-64 overflow-y-auto">
                                          {units.map((unit, index) => (
                                            <Chip
                                              key={index}
                                              variant="flat"
                                              color="secondary"
                                              onClose={() =>
                                                removeOrganizationalUnit(
                                                  job.id,
                                                  unit
                                                )
                                              }
                                              className="text-sm"
                                            >
                                              {unit}
                                            </Chip>
                                          ))}
                                        </div>
                                        {units.length === 0 && (
                                          <p className="text-sm text-gray-500 italic">
                                            No organizational units configured.
                                            Add units above to start downloading
                                            data.
                                          </p>
                                        )}
                                      </div>

                                      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                                        <p className="text-sm text-blue-800">
                                          <strong>Note:</strong> Each
                                          organizational unit represents a
                                          directory on the SFTP server. The
                                          system will attempt to download all
                                          files from each specified unit
                                          directory.
                                        </p>
                                      </div>
                                    </div>
                                  );
                                }

                                return null;
                              })}

                              {/* Status indicator for test path */}
                              {section.options.some(
                                (opt) => opt.key === "testPath"
                              ) && (
                                <div className="mt-4 p-3 bg-green-50 rounded-lg">
                                  <p className="text-sm text-green-800">
                                    <strong>Test Mode:</strong>{" "}
                                    {getConfigValue(job, "testPath")
                                      ? `Currently testing path: ${getConfigValue(
                                          job,
                                          "testPath"
                                        )}`
                                      : "Disabled - will process all organizational folders"}
                                  </p>
                                  <p className="text-sm text-green-700 mt-1">
                                    Use test mode to limit processing to a
                                    specific organizational path for testing
                                    purposes.
                                  </p>
                                </div>
                              )}
                            </div>
                          </CardBody>
                        </Card>
                      ));
                    })}

                  {/* Fallback when no configurable jobs are found */}
                  {Object.values(editingJobs).filter(
                    (job) => getJobConfigOptions(job).length > 0
                  ).length === 0 && (
                    <Card>
                      <CardBody className="text-center py-8">
                        <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <h4 className="font-semibold text-gray-700 mb-2">
                          No Configurable Jobs Found
                        </h4>
                        <p className="text-gray-500 mb-4">
                          No jobs with configurable options are currently
                          available.
                        </p>
                        <p className="text-sm text-gray-400">
                          Jobs may be loading or may not have user-configurable
                          settings.
                        </p>
                      </CardBody>
                    </Card>
                  )}
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {getSettingsByCategory(activeTab).map((setting) => (
                    <Card
                      key={`${setting.category}.${setting.key}`}
                      className="h-fit"
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center gap-2">
                            <Settings className="w-4 h-4 text-gray-500" />
                            <div>
                              <h4 className="font-medium text-sm">
                                {setting.key}
                              </h4>
                              <p className="text-xs text-gray-500">
                                {setting.category}.{setting.key}
                              </p>
                            </div>
                          </div>
                          <Chip
                            size="sm"
                            variant="flat"
                            color="primary"
                            className="text-xs"
                          >
                            {setting.type}
                          </Chip>
                        </div>
                      </CardHeader>
                      <CardBody className="pt-0 space-y-2">
                        {setting.description && (
                          <p className="text-xs text-gray-600">
                            {setting.description}
                          </p>
                        )}
                        {renderSettingInput(setting)}
                      </CardBody>
                    </Card>
                  ))}
                  {getSettingsByCategory(activeTab).length === 0 && (
                    <div className="col-span-full">
                      <Card>
                        <CardBody className="text-center py-8">
                          <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <h4 className="font-semibold text-gray-700 mb-2">
                            No Settings Found
                          </h4>
                          <p className="text-gray-500">
                            No settings are configured for the{" "}
                            {
                              SETTING_CATEGORIES[
                                activeTab as keyof typeof SETTING_CATEGORIES
                              ]
                            }{" "}
                            category.
                          </p>
                        </CardBody>
                      </Card>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </ModalBody>
        <ModalFooter>
          <div className="flex gap-2 w-full justify-end">
            <Button variant="flat" onPress={onClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              startContent={<Save size={16} />}
              onPress={saveSettings}
              isLoading={saving}
            >
              Save All Changes
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
