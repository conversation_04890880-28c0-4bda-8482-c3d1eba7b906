import { initializeDatabase, executeQuery } from "./src/lib/database";

// Simple test to check if we can save one file's metadata
async function testSingleFileMetadata() {
  try {
    console.log("🔧 Initializing database...");
    await initializeDatabase();

    // Test saving metadata for one fake file (simulating what happens during download)
    const { executeUpdate } = await import("./src/lib/database");

    const testJobId = "4"; // Use existing job 4
    const testExecutionId = `${testJobId}-${Date.now()}`;
    const testFilename = "test-file.pdf";
    const testSize = 1024;
    const testPath = `adk_rkakl2024/01/001/2024/${testFilename}`;

    console.log("� Creating job execution record first...");
    // Create a job execution record first (required by foreign key)
    await executeUpdate(
      `INSERT INTO job_executions (id, job_id, status, start_time, created_at, updated_at) 
       VALUES (?, ?, ?, NOW(), NOW(), NOW())`,
      [testExecutionId, testJobId, "running"]
    );

    console.log("�💾 Saving test file metadata...");
    await executeUpdate(
      `INSERT INTO sftp_file_transfers (
        job_id, execution_id, remote_file_path, remote_file_name, 
        local_file_path, local_directory, file_size_bytes, 
        transfer_status, file_modified_date, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        testJobId,
        testExecutionId,
        testPath,
        testFilename,
        `C:/test/${testFilename}`,
        "01/001",
        testSize,
        "completed",
        new Date(),
      ]
    );

    console.log("✅ Test metadata saved!");

    // Check if it was saved
    console.log("🔍 Checking saved metadata...");
    const result = await executeQuery(
      "SELECT remote_file_name, file_size_bytes, transfer_status, created_at FROM sftp_file_transfers WHERE job_id = ? ORDER BY created_at DESC LIMIT 1",
      [testJobId]
    );

    if (result.length > 0) {
      const file = result[0] as {
        remote_file_name: string;
        file_size_bytes: number;
        transfer_status: string;
        created_at: string;
      };
      console.log(
        `✅ SUCCESS! Found saved file: ${file.remote_file_name}, Size: ${file.file_size_bytes}, Status: ${file.transfer_status}`
      );
      console.log(
        "\n📍 To check your metadata, query the 'sftp_file_transfers' table in your database:"
      );
      console.log(
        `   SELECT * FROM sftp_file_transfers WHERE job_id = '4' ORDER BY created_at DESC;`
      );
    } else {
      console.log("❌ No metadata found");
    }
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    process.exit(0);
  }
}

testSingleFileMetadata();
