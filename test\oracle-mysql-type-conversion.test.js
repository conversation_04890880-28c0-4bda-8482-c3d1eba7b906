/**
 * Test suite for Oracle-to-MySQL type conversion enhancements
 * Specifically tests the PAGU column and large financial value handling
 */

const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals');
const { saveToMySQL } = require('../src/lib/jobRunner');

// Mock data simulating Oracle query results with large PAGU values
const mockOracleData = [
  {
    id: 1,
    nama_kegiatan: 'Pembangunan Jalan Raya',
    pagu: 15000000000, // 15 billion - exceeds INT range
    anggaran: 12500000000, // 12.5 billion
    tahun: 2024,
    kode_satker: '123456',
    deskripsi: 'Pembangunan infrastruktur jalan sepanjang 50 km'
  },
  {
    id: 2,
    nama_kegiatan: 'Renovasi Gedung Perkantoran',
    pagu: 2500000000.50, // 2.5 billion with decimal
    anggaran: 2300000000.75,
    tahun: 2024,
    kode_satker: '789012',
    deskripsi: 'Renovasi gedung kantor pusat'
  },
  {
    id: 3,
    nama_kegiatan: 'Pengadaan Peralatan IT',
    pagu: 500000000, // 500 million - within BIGINT range
    anggaran: 450000000,
    tahun: 2024,
    kode_satker: '345678',
    deskripsi: 'Pengadaan server dan perangkat jaringan'
  }
];

// Mock MySQL connection
const mockConnection = {
  execute: jest.fn(),
  query: jest.fn()
};

// Mock job definition
const mockJobDef = {
  id: 'test-oracle-mysql-conversion',
  destination: {
    mysql: {
      table: 'test_pagu_data'
    }
  }
};

describe('Oracle-to-MySQL Type Conversion', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock table existence check to return false (table doesn't exist)
    mockConnection.query.mockResolvedValueOnce([[]]);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should create MySQL table with appropriate column types for large PAGU values', async () => {
    // Mock successful table creation and data insertion
    mockConnection.execute.mockResolvedValue({ affectedRows: 1 });
    
    await saveToMySQL(mockOracleData, mockJobDef, mockConnection);
    
    // Verify table creation SQL
    const createTableCall = mockConnection.execute.mock.calls.find(call => 
      call[0].includes('CREATE TABLE')
    );
    
    expect(createTableCall).toBeDefined();
    const createTableSQL = createTableCall[0];
    
    // Check that PAGU column uses DECIMAL(20,2) for financial data
    expect(createTableSQL).toMatch(/`pagu`\s+DECIMAL\(20,2\)/);
    expect(createTableSQL).toMatch(/`anggaran`\s+DECIMAL\(20,2\)/);
    
    // Check that ID uses appropriate integer type
    expect(createTableSQL).toMatch(/`id`\s+(INT|BIGINT)/);
    
    // Check that year uses INT (within range)
    expect(createTableSQL).toMatch(/`tahun`\s+INT/);
    
    // Check that text fields use appropriate VARCHAR/TEXT
    expect(createTableSQL).toMatch(/`nama_kegiatan`\s+(VARCHAR\(255\)|TEXT)/);
    expect(createTableSQL).toMatch(/`deskripsi`\s+(VARCHAR\(255\)|TEXT)/);
  });

  it('should handle large PAGU values during data insertion', async () => {
    // Mock successful operations
    mockConnection.execute.mockResolvedValue({ affectedRows: 1 });
    
    await saveToMySQL(mockOracleData, mockJobDef, mockConnection);
    
    // Find INSERT statements
    const insertCalls = mockConnection.execute.mock.calls.filter(call => 
      call[0].includes('INSERT INTO')
    );
    
    expect(insertCalls).toHaveLength(3); // One for each data row
    
    // Check that large PAGU values are properly handled
    const firstInsert = insertCalls[0];
    const firstValues = firstInsert[1];
    
    // PAGU value should be properly formatted
    const paguIndex = firstValues.findIndex(val => val === 15000000000 || val === '15000000000');
    expect(paguIndex).toBeGreaterThan(-1);
  });

  it('should handle data type conversion errors gracefully', async () => {
    // Mock table creation success but insertion failure due to data range
    mockConnection.execute
      .mockResolvedValueOnce({ affectedRows: 1 }) // Table creation
      .mockRejectedValueOnce({ // First insert fails
        code: 'ER_WARN_DATA_OUT_OF_RANGE',
        message: 'Data out of range for column pagu'
      })
      .mockResolvedValueOnce({ affectedRows: 1 }); // Retry with string conversion succeeds
    
    // Should not throw error, should handle gracefully
    await expect(saveToMySQL([mockOracleData[0]], mockJobDef, mockConnection))
      .resolves.not.toThrow();
    
    // Should have attempted retry with string conversion
    expect(mockConnection.execute).toHaveBeenCalledTimes(3);
  });

  it('should properly identify financial fields by name pattern', () => {
    const testCases = [
      { field: 'pagu', expected: true },
      { field: 'PAGU', expected: true },
      { field: 'anggaran', expected: true },
      { field: 'ANGGARAN', expected: true },
      { field: 'budget', expected: true },
      { field: 'amount', expected: true },
      { field: 'nilai', expected: true },
      { field: 'harga', expected: true },
      { field: 'biaya', expected: true },
      { field: 'cost', expected: true },
      { field: 'rupiah', expected: true },
      { field: 'RUPIAH', expected: true },
      { field: 'nama_kegiatan', expected: false },
      { field: 'tahun', expected: false },
      { field: 'id', expected: false }
    ];
    
    testCases.forEach(({ field, expected }) => {
      const isFinancialField = /^(pagu|anggaran|budget|amount|nilai|harga|biaya|cost|rupiah)$/i.test(field);
      expect(isFinancialField).toBe(expected);
    });
  });

  it('should handle mixed data types in the same column', async () => {
    const mixedData = [
      { id: 1, pagu: 15000000000 }, // Large integer
      { id: 2, pagu: 2500000000.50 }, // Decimal
      { id: 3, pagu: '3000000000' }, // String representation
      { id: 4, pagu: null } // Null value
    ];
    
    mockConnection.execute.mockResolvedValue({ affectedRows: 1 });
    
    await expect(saveToMySQL(mixedData, mockJobDef, mockConnection))
      .resolves.not.toThrow();
    
    // Should handle all variations without errors
    expect(mockConnection.execute).toHaveBeenCalled();
  });
});

// Export for potential integration testing
module.exports = {
  mockOracleData,
  mockJobDef
};