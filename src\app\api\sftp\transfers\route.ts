import { NextRequest, NextResponse } from "next/server";
import {
  getSftpFileTransfersByExecution,
  getSftpFileTransfersByJob,
  getSftpTransferSummary,
  getFilesForProcessing,
} from "@/lib/sftpFileManager";
import { logger } from "@/lib/jobManager";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");
    const executionId = searchParams.get("executionId");
    const jobId = searchParams.get("jobId");
    const limit = searchParams.get("limit");

    switch (action) {
      case "by-execution":
        if (!executionId) {
          return NextResponse.json(
            { error: "executionId is required for by-execution action" },
            { status: 400 }
          );
        }
        const transfersByExecution = await getSftpFileTransfersByExecution(
          executionId
        );
        return NextResponse.json({
          success: true,
          data: transfersByExecution,
        });

      case "by-job":
        if (!jobId) {
          return NextResponse.json(
            { error: "jobId is required for by-job action" },
            { status: 400 }
          );
        }
        const limitNumber = limit ? parseInt(limit) : 100;
        const transfersByJob = await getSftpFileTransfersByJob(
          jobId,
          limitNumber
        );
        return NextResponse.json({
          success: true,
          data: transfersByJob,
        });

      case "summary":
        if (!executionId) {
          return NextResponse.json(
            { error: "executionId is required for summary action" },
            { status: 400 }
          );
        }
        const summary = await getSftpTransferSummary(executionId);
        return NextResponse.json({
          success: true,
          data: summary,
        });

      case "for-processing":
        const limitForProcessing = limit ? parseInt(limit) : 50;
        const filesForProcessing = await getFilesForProcessing(
          jobId || undefined,
          limitForProcessing
        );
        return NextResponse.json({
          success: true,
          data: filesForProcessing,
        });

      default:
        return NextResponse.json(
          {
            error:
              "Invalid action. Supported actions: by-execution, by-job, summary, for-processing",
          },
          { status: 400 }
        );
    }
  } catch (error) {
    logger.error("Failed to handle SFTP file transfers request:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
