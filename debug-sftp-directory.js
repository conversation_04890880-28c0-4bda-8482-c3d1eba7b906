const SftpClient = require('ssh2-sftp-client');

async function debugSftpDirectory() {
  const sftp = new SftpClient();
  
  const config = {
    host: 'aksesdata-anggaran.kemenkeu.go.id',
    port: 54321,
    username: 'PA_DJPBN',
    password: '<PERSON><PERSON><PERSON>100Persen'
  };

  try {
    console.log('🔗 Connecting to SFTP server...');
    await sftp.connect(config);
    console.log('✅ Connected successfully!');

    // Test different directory paths
    const directoriesToTest = [
      'adk_rkakl2025',
      '/adk_rkakl2025',
      './adk_rkakl2025',
      'ADK_RKAKL2025',
      '/ADK_RKAKL2025'
    ];

    for (const dir of directoriesToTest) {
      console.log(`\n📁 Testing directory: "${dir}"`);
      try {
        const fileList = await sftp.list(dir);
        console.log(`✅ Directory exists! Found ${fileList.length} items:`);
        
        if (fileList.length > 0) {
          console.log('\n📋 Directory contents:');
          fileList.forEach((item, index) => {
            console.log(`${index + 1}. Name: "${item.name}"`);
            console.log(`   Type: "${item.type}" (${item.type === '-' ? 'file' : item.type === 'd' ? 'directory' : 'other'})`);
            console.log(`   Size: ${item.size} bytes`);
            console.log(`   Modified: ${new Date(item.modifyTime)}`);
            console.log(`   Permissions: ${item.rights ? item.rights.toString() : 'N/A'}`);
            console.log('');
          });

          // Show filtering results
          const regularFiles = fileList.filter(file => file.type === '-');
          console.log(`🔍 Regular files (type === '-'): ${regularFiles.length}`);
          if (regularFiles.length > 0) {
            console.log('Regular files found:');
            regularFiles.forEach(file => console.log(`  - ${file.name}`));
          }

          const directories = fileList.filter(file => file.type === 'd');
          console.log(`📂 Directories (type === 'd'): ${directories.length}`);
          if (directories.length > 0) {
            console.log('Directories found:');
            directories.forEach(dir => console.log(`  - ${dir.name}/`));
          }

          // If we found directories, let's peek into the first few
          if (directories.length > 0) {
            console.log('\n🔍 Checking contents of subdirectories...');
            for (let i = 0; i < Math.min(3, directories.length); i++) {
              const subDir = directories[i];
              const subDirPath = `${dir}/${subDir.name}`;
              try {
                console.log(`\n📁 Checking subdirectory: ${subDirPath}`);
                const subFileList = await sftp.list(subDirPath);
                console.log(`  Found ${subFileList.length} items in ${subDir.name}/`);
                
                const subFiles = subFileList.filter(file => file.type === '-');
                if (subFiles.length > 0) {
                  console.log(`  📄 Files in ${subDir.name}/:`);
                  subFiles.slice(0, 5).forEach(file => console.log(`    - ${file.name}`));
                  if (subFiles.length > 5) {
                    console.log(`    ... and ${subFiles.length - 5} more files`);
                  }
                }
              } catch (subDirError) {
                console.log(`  ❌ Cannot access subdirectory: ${subDirError.message}`);
              }
            }
          }
        } else {
          console.log('📭 Directory is empty');
        }
        
        // If this directory worked, we can stop testing others
        if (fileList.length > 0) {
          console.log(`\n🎯 Found working directory: "${dir}"`);
          break;
        }
        
      } catch (error) {
        console.log(`❌ Cannot access directory: ${error.message}`);
      }
    }

    // Also try listing the root directory to see what's available
    console.log('\n🏠 Checking root directory contents...');
    try {
      const rootList = await sftp.list('.');
      console.log(`Root directory contains ${rootList.length} items:`);
      rootList.forEach(item => {
        console.log(`  ${item.type === 'd' ? '📁' : '📄'} ${item.name}`);
      });
    } catch (rootError) {
      console.log(`❌ Cannot list root directory: ${rootError.message}`);
    }

  } catch (error) {
    console.error('❌ SFTP Error:', error.message);
    if (error.code) {
      console.error('Error code:', error.code);
    }
  } finally {
    try {
      await sftp.end();
      console.log('\n🔌 SFTP connection closed');
    } catch (closeError) {
      console.error('Error closing connection:', closeError.message);
    }
  }
}

// Run the diagnostic
debugSftpDirectory().catch(console.error);
