import React from "react";
import { <PERSON>, CardBody, Chip } from "@heroui/react";
import { Database, FileText, CheckCircle, AlertCircle } from "lucide-react";

interface AdkTableMappingDisplayProps {
  className?: string;
}

export const AdkTableMappingDisplay: React.FC<AdkTableMappingDisplayProps> = ({
  className = "",
}) => {
  // ADK table mapping configuration based on the implementation
  const tableMappings = [
    {
      xmlPattern: "d_akun",
      tableName: "d_akun",
      description: "Account master data",
      dataPath: "VFPData.d_akun",
      status: "active",
      recordCount: "~1,000",
    },
    {
      xmlPattern: "d_cttakun",
      tableName: "d_cttakun",
      description: "Account notes and descriptions",
      dataPath: "VFPData.d_cttakun",
      status: "active",
      recordCount: "~800",
    },
    {
      xmlPattern: "d_item",
      tableName: "d_item",
      description: "Item master data (largest dataset)",
      dataPath: "VFPData.d_item",
      status: "active",
      recordCount: "~20,000",
    },
    {
      xmlPattern: "d_kmpnen",
      tableName: "d_kmpnen",
      description: "Component data",
      dataPath: "VFPData.d_kmpnen",
      status: "active",
      recordCount: "~1,500",
    },
    {
      xmlPattern: "d_kpa",
      tableName: "d_kpa",
      description: "KPA (Key Performance Area) data",
      dataPath: "VFPData.d_kpa",
      status: "active",
      recordCount: "~200",
    },
    {
      xmlPattern: "d_kpjm",
      tableName: "d_kpjm",
      description: "KPJM data",
      dataPath: "VFPData.d_kpjm",
      status: "active",
      recordCount: "~300",
    },
    {
      xmlPattern: "d_output",
      tableName: "d_output",
      description: "Output data",
      dataPath: "VFPData.d_output",
      status: "active",
      recordCount: "~500",
    },
    {
      xmlPattern: "d_pdpt",
      tableName: "d_pdpt",
      description: "Revenue (Pendapatan) data",
      dataPath: "VFPData.d_pdpt",
      status: "active",
      recordCount: "~200",
    },
    {
      xmlPattern: "d_pgj",
      tableName: "d_pgj",
      description: "PGJ data (schema differences)",
      dataPath: "VFPData.d_pgj",
      status: "warning",
      recordCount: "~100",
    },
    {
      xmlPattern: "d_polri",
      tableName: "d_polri",
      description: "Police data",
      dataPath: "VFPData.d_polri",
      status: "active",
      recordCount: "~50",
    },
    {
      xmlPattern: "d_skmpnen",
      tableName: "d_skmpnen",
      description: "Sub-component data",
      dataPath: "VFPData.d_skmpnen",
      status: "active",
      recordCount: "~2,000",
    },
    {
      xmlPattern: "d_soutput",
      tableName: "d_soutput",
      description: "Sub-output data",
      dataPath: "VFPData.d_soutput",
      status: "active",
      recordCount: "~800",
    },
    {
      xmlPattern: "d_trktrm",
      tableName: "d_trktrm",
      description: "Transfer data",
      dataPath: "VFPData.d_trktrm",
      status: "active",
      recordCount: "~400",
    },
    {
      xmlPattern: "d_valas",
      tableName: "d_valas",
      description: "Foreign exchange data",
      dataPath: "VFPData.d_valas",
      status: "active",
      recordCount: "~100",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "success";
      case "warning":
        return "warning";
      default:
        return "default";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="w-3 h-3" />;
      case "warning":
        return <AlertCircle className="w-3 h-3" />;
      default:
        return <Database className="w-3 h-3" />;
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center">
          <Database className="w-4 h-4 text-blue-600" />
        </div>
        <div>
          <h4 className="text-lg font-semibold text-gray-900">
            ADK Table Mapping Configuration
          </h4>
          <p className="text-sm text-gray-600">
            Automatic routing of XML files to database tables
          </p>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4">
        <Card className="bg-green-50 border-green-200">
          <CardBody className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <div className="text-2xl font-bold text-green-700">13</div>
                <div className="text-xs text-green-600">Active Tables</div>
              </div>
            </div>
          </CardBody>
        </Card>
        <Card className="bg-yellow-50 border-yellow-200">
          <CardBody className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-yellow-600" />
              <div>
                <div className="text-2xl font-bold text-yellow-700">1</div>
                <div className="text-xs text-yellow-600">Schema Issues</div>
              </div>
            </div>
          </CardBody>
        </Card>
        <Card className="bg-blue-50 border-blue-200">
          <CardBody className="p-4">
            <div className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-blue-600" />
              <div>
                <div className="text-2xl font-bold text-blue-700">~67K</div>
                <div className="text-xs text-blue-600">Total Records</div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Table Mappings Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {tableMappings.map((mapping) => (
          <Card
            key={mapping.tableName}
            className={`border ${
              mapping.status === "warning"
                ? "border-yellow-200 bg-yellow-50"
                : "border-gray-200 hover:border-blue-300"
            } transition-colors`}
          >
            <CardBody className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    {getStatusIcon(mapping.status)}
                    <code className="text-sm font-mono text-blue-700 bg-blue-100 px-2 py-1 rounded">
                      {mapping.tableName}
                    </code>
                    <Chip
                      size="sm"
                      color={getStatusColor(mapping.status)}
                      variant="flat"
                    >
                      {mapping.recordCount}
                    </Chip>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">
                    {mapping.description}
                  </p>
                  <div className="text-xs text-gray-500">
                    <div>
                      <strong>XML Pattern:</strong> {mapping.xmlPattern}.xml
                    </div>
                    <div>
                      <strong>Data Path:</strong> {mapping.dataPath}
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Additional Info */}
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h6 className="text-sm font-semibold text-gray-700 mb-2">
          📋 Processing Notes
        </h6>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>
            • <strong>Automatic Routing:</strong> XML files are automatically
            routed to tables based on filename patterns
          </li>
          <li>
            • <strong>Dynamic Schema:</strong> Tables adapt to XML structure
            automatically
          </li>
          <li>
            • <strong>Error Handling:</strong> Processing continues even if
            individual files fail
          </li>
          <li>
            • <strong>d_pgj Warning:</strong> Has schema differences that may
            cause insertion failures
          </li>
        </ul>
      </div>
    </div>
  );
};
