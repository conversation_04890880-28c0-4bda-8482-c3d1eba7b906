import { JobDefinition, JobSequence } from "@/lib/jobManager";
import { JobStats, JobsByType } from "./types";

/**
 * Separates jobs into independent and sequence jobs
 */
export const separateJobsByType = (jobs: JobDefinition[]): JobsByType => {
  const independentJobs = jobs.filter((job) => !job.sequenceConfig);
  const sequenceJobs = jobs.filter((job) => job.sequenceConfig);
  return { independentJobs, sequenceJobs };
};

/**
 * Groups sequence jobs by their sequence ID
 */
export const groupJobsBySequence = (jobs: JobDefinition[]): Map<string, JobDefinition[]> => {
  const { sequenceJobs } = separateJobsByType(jobs);
  const grouped = new Map<string, JobDefinition[]>();

  sequenceJobs.forEach((job) => {
    if (job.sequenceConfig) {
      const sequenceId = job.sequenceConfig.sequenceId;
      if (!grouped.has(sequenceId)) {
        grouped.set(sequenceId, []);
      }
      grouped.get(sequenceId)!.push(job);
    }
  });

  // Sort jobs within each sequence by order
  grouped.forEach((jobs) => {
    jobs.sort((a, b) => {
      const orderA = a.sequenceConfig?.order || 0;
      const orderB = b.sequenceConfig?.order || 0;
      return orderA - orderB;
    });
  });

  return grouped;
};

/**
 * Gets sequence information by sequence ID
 */
export const getSequenceInfo = (sequenceId: string, sequences: JobSequence[]): JobSequence | undefined => {
  return sequences.find((seq) => seq.id === sequenceId);
};

/**
 * Gets available jobs for sequence assignment (independent jobs only)
 */
export const getAvailableJobsForSequence = (jobs: JobDefinition[]): JobDefinition[] => {
  return jobs.filter((job) => !job.sequenceConfig);
};

/**
 * Calculates job statistics for display
 */
export const getJobStats = (jobs: JobDefinition[]): JobStats => {
  const total = jobs.length;
  const active = jobs.filter((job) => job.enabled).length;
  const disabled = jobs.filter((job) => !job.enabled).length;
  const oracle = jobs.filter((job) => job.dataSource.type === "oracle").length;
  const sftp = jobs.filter((job) => job.dataSource.type === "sftp").length;

  return { total, active, disabled, oracle, sftp };
};
