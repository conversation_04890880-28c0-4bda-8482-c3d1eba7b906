/**
 * Utility functions for time formatting and timezone handling
 */

// Server timezone for cron scheduling (configurable via environment)
export const SERVER_TIMEZONE = process.env.TIMEZONE || "Asia/Jakarta";

/**
 * Get the user's local timezone
 */
export function getUserTimezone(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch {
    return "UTC"; // Fallback to UTC if detection fails
  }
}

/**
 * Get timezone display name (short format)
 */
export function getTimezoneDisplayName(timezone?: string): string {
  const tz = timezone || getUserTimezone();
  try {
    // Get short timezone name (e.g., "WIB" for Indonesia, "EST" for Eastern)
    const shortName = new Intl.DateTimeFormat("en", {
      timeZone: tz,
      timeZoneName: "short",
    })
      .formatToParts(new Date())
      .find((part) => part.type === "timeZoneName")?.value;

    return shortName || tz.split("/").pop() || tz;
  } catch {
    return tz.split("/").pop() || tz;
  }
}

/**
 * Format a date in the user's local timezone
 */
export function formatInLocalTimezone(
  date: Date,
  options: Intl.DateTimeFormatOptions = {}
): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    timeZone: getUserTimezone(),
    year: "numeric",
    month: "short",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    hour12: false, // Use 24-hour format
    ...options,
  };

  return date.toLocaleString("en-US", defaultOptions);
}

/**
 * Format a date in server timezone (for admin/debugging)
 */
export function formatInServerTimezone(
  date: Date,
  options: Intl.DateTimeFormatOptions = {}
): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    timeZone: SERVER_TIMEZONE,
    year: "numeric",
    month: "short",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    hour12: false, // Use 24-hour format
    ...options,
  };

  return date.toLocaleString("en-US", defaultOptions);
}

/**
 * Format a date for table display (short format) in user's local timezone
 */
export function formatTableDate(date: Date): string {
  return formatInLocalTimezone(date, {
    month: "short",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    hour12: false, // Use 24-hour format
  });
}

/**
 * Format a date for detailed display (with seconds) in user's local timezone
 */
export function formatDetailedDate(date: Date): string {
  return formatInLocalTimezone(date, {
    year: "numeric",
    month: "short",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false, // Use 24-hour format
  });
}

/**
 * Get the current time in user's local timezone
 */
export function getCurrentTimeInLocalTimezone(): string {
  return formatInLocalTimezone(new Date(), {
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false, // Use 24-hour format
  });
}

/**
 * Get the current date and time in user's local timezone
 */
export function getCurrentDateTimeInLocalTimezone(): string {
  return formatDetailedDate(new Date());
}

/**
 * Get UTC offset string (e.g., "UTC+7", "UTC-5")
 */
export function getUTCOffsetString(): string {
  const offset = new Date().getTimezoneOffset();
  const hours = Math.floor(Math.abs(offset) / 60);
  const minutes = Math.abs(offset) % 60;
  const sign = offset <= 0 ? "+" : "-";

  if (minutes === 0) {
    return `UTC${sign}${hours}`;
  } else {
    return `UTC${sign}${hours}:${minutes.toString().padStart(2, "0")}`;
  }
}
