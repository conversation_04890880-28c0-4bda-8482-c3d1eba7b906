import { JobDefinition } from "@/lib/jobManager";

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface AdkValidationResult extends ValidationResult {
  fieldErrors: Record<string, string>;
}

/**
 * Comprehensive validation for ADK processing configuration
 */
export const validateAdkConfiguration = (
  job: JobDefinition
): AdkValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const fieldErrors: Record<string, string> = {};

  // Only validate if this is an ADK processing job
  if (job.dataSource.type !== "adk_processing") {
    return {
      isValid: true,
      errors: [],
      warnings: [],
      fieldErrors: {},
    };
  }

  const adkConfig = job.dataSource.adk_processing;

  // Validate required file paths
  if (!adkConfig?.sourceDirectory?.trim()) {
    errors.push("Source directory is required for ADK processing");
    fieldErrors.sourceDirectory = "This field is required";
  } else {
    // Basic path validation
    const sourcePath = adkConfig.sourceDirectory.trim();
    if (!isValidWindowsPath(sourcePath)) {
      errors.push("Source directory must be a valid Windows path");
      fieldErrors.sourceDirectory = "Invalid Windows path format";
    }
  }

  if (!adkConfig?.extractionPath?.trim()) {
    errors.push("Extraction path is required for ADK processing");
    fieldErrors.extractionPath = "This field is required";
  } else {
    const extractionPath = adkConfig.extractionPath.trim();
    if (!isValidWindowsPath(extractionPath)) {
      errors.push("Extraction path must be a valid Windows path");
      fieldErrors.extractionPath = "Invalid Windows path format";
    }
  }

  if (!adkConfig?.rarToolPath?.trim()) {
    errors.push("RAR tool path is required for ADK processing");
    fieldErrors.rarToolPath = "This field is required";
  } else {
    const rarPath = adkConfig.rarToolPath.trim();
    if (!isValidWindowsPath(rarPath)) {
      errors.push("RAR tool path must be a valid Windows path");
      fieldErrors.rarToolPath = "Invalid Windows path format";
    } else if (!rarPath.toLowerCase().endsWith(".exe")) {
      warnings.push("RAR tool path should point to an executable (.exe) file");
      fieldErrors.rarToolPath = "Should be an executable file";
    }
  }

  // Validate file filtering configuration
  const fileFilter = adkConfig?.fileFilter;
  if (!fileFilter?.startsWith || fileFilter.startsWith.length === 0) {
    errors.push("File name prefixes are required for filtering");
    fieldErrors.fileNamePrefixes = "At least one prefix is required";
  } else {
    // Validate prefixes
    const invalidPrefixes = fileFilter.startsWith.filter(
      (prefix) => !prefix || prefix.trim().length === 0
    );
    if (invalidPrefixes.length > 0) {
      warnings.push("Empty prefixes will be ignored");
    }
  }

  // Validate exclude extensions (optional but should be valid if provided)
  if (
    fileFilter?.excludeExtensions &&
    fileFilter.excludeExtensions.length > 0
  ) {
    const invalidExtensions = fileFilter.excludeExtensions.filter(
      (ext) => ext && !isValidFileExtension(ext)
    );
    if (invalidExtensions.length > 0) {
      warnings.push(`Invalid file extensions: ${invalidExtensions.join(", ")}`);
    }
  }

  // Validate processing options
  const processingOptions = adkConfig?.processingOptions;
  if (processingOptions?.batchSize !== undefined) {
    if (
      processingOptions.batchSize <= 0 ||
      !Number.isInteger(processingOptions.batchSize)
    ) {
      errors.push("Batch size must be a positive integer");
      fieldErrors.batchSize = "Must be a positive integer";
    } else if (processingOptions.batchSize > 1000) {
      warnings.push("Large batch sizes may impact performance");
    }
  }

  // Validate destination configuration for ADK processing
  if (job.destination.type !== "database") {
    errors.push("ADK processing requires database destination");
    fieldErrors.destinationType = "Database destination is required";
  } else {
    // Validate database connection
    const dbConfig = job.destination.database;
    if (!dbConfig?.host?.trim()) {
      errors.push("Database host is required");
      fieldErrors.databaseHost = "This field is required";
    }
    if (!dbConfig?.database?.trim()) {
      errors.push("Database name is required");
      fieldErrors.databaseName = "This field is required";
    }
    if (!dbConfig?.username?.trim()) {
      errors.push("Database username is required");
      fieldErrors.databaseUsername = "This field is required";
    }
    if (!dbConfig?.password?.trim()) {
      warnings.push("Database password is recommended");
    }
  }

  // ADK-specific recommendations
  if (processingOptions?.continueOnError !== true) {
    warnings.push(
      "Consider enabling 'Continue on Error' for robust ADK processing"
    );
  }

  if (processingOptions?.deleteOldXmlFiles !== true) {
    warnings.push(
      "Consider enabling 'Clean extraction directory' to prevent conflicts"
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fieldErrors,
  };
};

/**
 * Validate Windows file path format
 */
const isValidWindowsPath = (path: string): boolean => {
  // Basic Windows path validation
  const windowsPathRegex = /^[a-zA-Z]:\\(?:[^<>:"|?*\r\n]+\\)*[^<>:"|?*\r\n]*$/;
  return windowsPathRegex.test(path) || path.startsWith("\\\\"); // UNC paths
};

/**
 * Validate file extension format
 */
const isValidFileExtension = (extension: string): boolean => {
  // Should not start with dot, contain only alphanumeric characters
  const extRegex = /^[a-zA-Z0-9]+$/;
  return extRegex.test(extension);
};

/**
 * Get validation status color for UI display
 */
export const getValidationStatusColor = (
  result: AdkValidationResult
): "success" | "warning" | "danger" => {
  if (!result.isValid) return "danger";
  if (result.warnings.length > 0) return "warning";
  return "success";
};

/**
 * Get validation status message for UI display
 */
export const getValidationStatusMessage = (
  result: AdkValidationResult
): string => {
  if (!result.isValid) {
    return `${result.errors.length} error${
      result.errors.length > 1 ? "s" : ""
    } found`;
  }
  if (result.warnings.length > 0) {
    return `${result.warnings.length} warning${
      result.warnings.length > 1 ? "s" : ""
    } found`;
  }
  return "Configuration is valid";
};

/**
 * Check if ADK processing is properly configured
 */
export const isAdkConfigurationComplete = (job: JobDefinition): boolean => {
  if (job.dataSource.type !== "adk_processing") return true;

  const validation = validateAdkConfiguration(job);
  return validation.isValid;
};

/**
 * Get ADK configuration completeness percentage
 */
export const getAdkConfigurationCompleteness = (job: JobDefinition): number => {
  if (job.dataSource.type !== "adk_processing") return 100;

  const adkConfig = job.dataSource.adk_processing;
  let completedFields = 0;
  const totalFields = 7; // sourceDirectory, extractionPath, rarToolPath, fileFilter.startsWith, destination, etc.

  if (adkConfig?.sourceDirectory?.trim()) completedFields++;
  if (adkConfig?.extractionPath?.trim()) completedFields++;
  if (adkConfig?.rarToolPath?.trim()) completedFields++;
  if (
    adkConfig?.fileFilter?.startsWith?.length &&
    adkConfig.fileFilter.startsWith.length > 0
  )
    completedFields++;
  if (job.destination.type === "database") completedFields++;
  if (job.destination.database?.host?.trim()) completedFields++;
  if (job.destination.database?.database?.trim()) completedFields++;

  return Math.round((completedFields / totalFields) * 100);
};
