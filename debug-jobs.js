// Debug script to check job definitions
import { loadJobDefinitions } from "./src/lib/jobPersistence";
import { initializeDatabase } from "./src/lib/database";

async function debugJobs() {
  try {
    console.log("Initializing database...");
    await initializeDatabase();

    console.log("Loading job definitions...");
    const jobs = await loadJobDefinitions();

    console.log(`Found ${jobs.length} jobs:`);
    jobs.forEach((job) => {
      console.log(
        `- ID: ${job.id}, Name: ${job.name}, Enabled: ${job.enabled}`
      );
    });

    process.exit(0);
  } catch (error) {
    console.error("Error:", error);
    process.exit(1);
  }
}

debugJobs();
