import { executeQuery, executeUpdate } from "./database";
import { logger } from "./jobManager";

export interface SftpFileTransfer {
  id?: number;
  jobId: string;
  executionId: string;
  remoteFilePath: string;
  remoteFileName: string;
  localFilePath: string;
  localDirectory: string;
  fileSizeBytes?: number;
  fileHash?: string;
  transferStatus:
    | "pending"
    | "downloading"
    | "completed"
    | "failed"
    | "skipped";
  transferStartTime?: Date;
  transferEndTime?: Date;
  transferDurationMs?: number;
  errorMessage?: string;
  fileModifiedDate?: Date;
  fileCreatedDate?: Date;
  processingStatus:
    | "not_processed"
    | "processing"
    | "processed"
    | "failed_processing";
  processingNotes?: string;
  metadata?: Record<string, unknown>;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface SftpFileTransferSummary {
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  totalSizeBytes: number;
  averageTransferTimeMs: number;
  processingStats: {
    notProcessed: number;
    processing: number;
    processed: number;
    failedProcessing: number;
  };
}

interface SftpFileTransferRow {
  id: number;
  job_id: string;
  execution_id: string;
  remote_file_path: string;
  remote_file_name: string;
  local_file_path: string;
  local_directory: string;
  file_size_bytes?: number;
  file_hash?: string;
  transfer_status: string;
  transfer_start_time?: Date;
  transfer_end_time?: Date;
  transfer_duration_ms?: number;
  error_message?: string;
  file_modified_date?: Date;
  file_created_date?: Date;
  processing_status: string;
  processing_notes?: string;
  metadata?: string;
  created_at: Date;
  updated_at: Date;
}

interface SftpTransferSummaryRow {
  total_files: number;
  completed_files: number;
  failed_files: number;
  total_size_bytes: number;
  avg_transfer_time_ms: number;
  not_processed: number;
  processing: number;
  processed: number;
  failed_processing: number;
}

/**
 * Create a new SFTP file transfer record
 */
export async function createSftpFileTransfer(
  transfer: Omit<SftpFileTransfer, "id" | "createdAt" | "updatedAt">
): Promise<number> {
  try {
    const query = `
      INSERT INTO sftp_file_transfers (
        job_id, execution_id, remote_file_path, remote_file_name,
        local_file_path, local_directory, file_size_bytes, file_hash,
        transfer_status, transfer_start_time, transfer_end_time, transfer_duration_ms,
        error_message, file_modified_date, file_created_date,
        processing_status, processing_notes, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      transfer.jobId,
      transfer.executionId,
      transfer.remoteFilePath,
      transfer.remoteFileName,
      transfer.localFilePath,
      transfer.localDirectory,
      transfer.fileSizeBytes || null,
      transfer.fileHash || null,
      transfer.transferStatus,
      transfer.transferStartTime || null,
      transfer.transferEndTime || null,
      transfer.transferDurationMs || null,
      transfer.errorMessage || null,
      transfer.fileModifiedDate || null,
      transfer.fileCreatedDate || null,
      transfer.processingStatus,
      transfer.processingNotes || null,
      transfer.metadata ? JSON.stringify(transfer.metadata) : null,
    ];

    const result = await executeUpdate(query, params);

    if (!result.insertId) {
      throw new Error("Failed to create SFTP file transfer record");
    }

    logger.info(
      `Created SFTP file transfer record for ${transfer.remoteFileName}`,
      {
        transferId: result.insertId,
        jobId: transfer.jobId,
        executionId: transfer.executionId,
      }
    );

    return result.insertId;
  } catch (error) {
    logger.error("Failed to create SFTP file transfer record:", error);
    throw error;
  }
}

/**
 * Update an existing SFTP file transfer record
 */
export async function updateSftpFileTransfer(
  id: number,
  updates: Partial<SftpFileTransfer>
): Promise<void> {
  try {
    const setClause: string[] = [];
    const params: (string | number | Date | null)[] = [];

    // Build dynamic SET clause based on provided updates
    if (updates.transferStatus !== undefined) {
      setClause.push("transfer_status = ?");
      params.push(updates.transferStatus);
    }
    if (updates.transferStartTime !== undefined) {
      setClause.push("transfer_start_time = ?");
      params.push(updates.transferStartTime);
    }
    if (updates.transferEndTime !== undefined) {
      setClause.push("transfer_end_time = ?");
      params.push(updates.transferEndTime);
    }
    if (updates.transferDurationMs !== undefined) {
      setClause.push("transfer_duration_ms = ?");
      params.push(updates.transferDurationMs);
    }
    if (updates.fileSizeBytes !== undefined) {
      setClause.push("file_size_bytes = ?");
      params.push(updates.fileSizeBytes);
    }
    if (updates.fileHash !== undefined) {
      setClause.push("file_hash = ?");
      params.push(updates.fileHash);
    }
    if (updates.errorMessage !== undefined) {
      setClause.push("error_message = ?");
      params.push(updates.errorMessage);
    }
    if (updates.processingStatus !== undefined) {
      setClause.push("processing_status = ?");
      params.push(updates.processingStatus);
    }
    if (updates.processingNotes !== undefined) {
      setClause.push("processing_notes = ?");
      params.push(updates.processingNotes);
    }
    if (updates.metadata !== undefined) {
      setClause.push("metadata = ?");
      params.push(updates.metadata ? JSON.stringify(updates.metadata) : null);
    }

    if (setClause.length === 0) {
      logger.warn("No updates provided for SFTP file transfer");
      return;
    }

    params.push(id);

    const query = `
      UPDATE sftp_file_transfers 
      SET ${setClause.join(", ")}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const result = await executeUpdate(query, params);

    if (result.affectedRows === 0) {
      throw new Error(`SFTP file transfer record with ID ${id} not found`);
    }

    logger.info(`Updated SFTP file transfer record ${id}`, {
      transferId: id,
      updatedFields: Object.keys(updates),
    });
  } catch (error) {
    logger.error(`Failed to update SFTP file transfer record ${id}:`, error);
    throw error;
  }
}

/**
 * Get SFTP file transfers by execution ID
 */
export async function getSftpFileTransfersByExecution(
  executionId: string
): Promise<SftpFileTransfer[]> {
  try {
    const query = `
      SELECT * FROM sftp_file_transfers 
      WHERE execution_id = ? 
      ORDER BY created_at DESC
    `;

    const rows = await executeQuery<SftpFileTransferRow>(query, [executionId]);

    return rows.map((row) => ({
      id: row.id,
      jobId: row.job_id,
      executionId: row.execution_id,
      remoteFilePath: row.remote_file_path,
      remoteFileName: row.remote_file_name,
      localFilePath: row.local_file_path,
      localDirectory: row.local_directory,
      fileSizeBytes: row.file_size_bytes,
      fileHash: row.file_hash,
      transferStatus: row.transfer_status as SftpFileTransfer["transferStatus"],
      transferStartTime: row.transfer_start_time,
      transferEndTime: row.transfer_end_time,
      transferDurationMs: row.transfer_duration_ms,
      errorMessage: row.error_message,
      fileModifiedDate: row.file_modified_date,
      fileCreatedDate: row.file_created_date,
      processingStatus:
        row.processing_status as SftpFileTransfer["processingStatus"],
      processingNotes: row.processing_notes,
      metadata: row.metadata ? JSON.parse(row.metadata) : null,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    }));
  } catch (error) {
    logger.error(
      `Failed to get SFTP file transfers for execution ${executionId}:`,
      error
    );
    throw error;
  }
}

/**
 * Get SFTP file transfers by job ID
 */
export async function getSftpFileTransfersByJob(
  jobId: string,
  limit: number = 100
): Promise<SftpFileTransfer[]> {
  try {
    // Validate limit to prevent SQL injection
    const safeLimit = Math.max(1, Math.min(1000, Math.floor(limit)));
    
    const query = `
      SELECT * FROM sftp_file_transfers 
      WHERE job_id = ? 
      ORDER BY created_at DESC 
      LIMIT ${safeLimit}
    `;

    const rows = await executeQuery<SftpFileTransferRow>(query, [jobId]);

    return rows.map((row) => ({
      id: row.id,
      jobId: row.job_id,
      executionId: row.execution_id,
      remoteFilePath: row.remote_file_path,
      remoteFileName: row.remote_file_name,
      localFilePath: row.local_file_path,
      localDirectory: row.local_directory,
      fileSizeBytes: row.file_size_bytes,
      fileHash: row.file_hash,
      transferStatus: row.transfer_status as SftpFileTransfer["transferStatus"],
      transferStartTime: row.transfer_start_time,
      transferEndTime: row.transfer_end_time,
      transferDurationMs: row.transfer_duration_ms,
      errorMessage: row.error_message,
      fileModifiedDate: row.file_modified_date,
      fileCreatedDate: row.file_created_date,
      processingStatus:
        row.processing_status as SftpFileTransfer["processingStatus"],
      processingNotes: row.processing_notes,
      metadata: row.metadata ? JSON.parse(row.metadata) : null,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    }));
  } catch (error) {
    logger.error(`Failed to get SFTP file transfers for job ${jobId}:`, error);
    throw error;
  }
}

/**
 * Get summary statistics for SFTP file transfers by execution
 */
export async function getSftpTransferSummary(
  executionId: string
): Promise<SftpFileTransferSummary> {
  try {
    const query = `
      SELECT 
        COUNT(*) as total_files,
        SUM(CASE WHEN transfer_status = 'completed' THEN 1 ELSE 0 END) as completed_files,
        SUM(CASE WHEN transfer_status = 'failed' THEN 1 ELSE 0 END) as failed_files,
        COALESCE(SUM(file_size_bytes), 0) as total_size_bytes,
        COALESCE(AVG(transfer_duration_ms), 0) as avg_transfer_time_ms,
        SUM(CASE WHEN processing_status = 'not_processed' THEN 1 ELSE 0 END) as not_processed,
        SUM(CASE WHEN processing_status = 'processing' THEN 1 ELSE 0 END) as processing,
        SUM(CASE WHEN processing_status = 'processed' THEN 1 ELSE 0 END) as processed,
        SUM(CASE WHEN processing_status = 'failed_processing' THEN 1 ELSE 0 END) as failed_processing
      FROM sftp_file_transfers 
      WHERE execution_id = ?
    `;

    const rows = await executeQuery<SftpTransferSummaryRow>(query, [
      executionId,
    ]);
    const row = rows[0];

    return {
      totalFiles: row.total_files || 0,
      completedFiles: row.completed_files || 0,
      failedFiles: row.failed_files || 0,
      totalSizeBytes: row.total_size_bytes || 0,
      averageTransferTimeMs: row.avg_transfer_time_ms || 0,
      processingStats: {
        notProcessed: row.not_processed || 0,
        processing: row.processing || 0,
        processed: row.processed || 0,
        failedProcessing: row.failed_processing || 0,
      },
    };
  } catch (error) {
    logger.error(
      `Failed to get SFTP transfer summary for execution ${executionId}:`,
      error
    );
    throw error;
  }
}

/**
 * Mark a file transfer as completed
 */
export async function markTransferCompleted(
  id: number,
  fileSizeBytes: number,
  transferDurationMs: number,
  fileHash?: string
): Promise<void> {
  try {
    await updateSftpFileTransfer(id, {
      transferStatus: "completed",
      transferEndTime: new Date(),
      transferDurationMs,
      fileSizeBytes,
      fileHash,
    });
  } catch (error) {
    logger.error(`Failed to mark transfer ${id} as completed:`, error);
    throw error;
  }
}

/**
 * Mark a file transfer as failed
 */
export async function markTransferFailed(
  id: number,
  errorMessage: string
): Promise<void> {
  try {
    await updateSftpFileTransfer(id, {
      transferStatus: "failed",
      transferEndTime: new Date(),
      errorMessage,
    });
  } catch (error) {
    logger.error(`Failed to mark transfer ${id} as failed:`, error);
    throw error;
  }
}

/**
 * Get files that need processing
 */
export async function getFilesForProcessing(
  jobId?: string,
  limit: number = 50
): Promise<SftpFileTransfer[]> {
  try {
    // Validate limit to prevent SQL injection
    const safeLimit = Math.max(1, Math.min(1000, Math.floor(limit)));
    
    let query = `
      SELECT * FROM sftp_file_transfers 
      WHERE transfer_status = 'completed' 
      AND processing_status = 'not_processed'
    `;

    const params: (string | number)[] = [];

    if (jobId) {
      query += " AND job_id = ?";
      params.push(jobId);
    }

    query += ` ORDER BY created_at ASC LIMIT ${safeLimit}`;

    const rows = await executeQuery<SftpFileTransferRow>(query, params);

    return rows.map((row) => ({
      id: row.id,
      jobId: row.job_id,
      executionId: row.execution_id,
      remoteFilePath: row.remote_file_path,
      remoteFileName: row.remote_file_name,
      localFilePath: row.local_file_path,
      localDirectory: row.local_directory,
      fileSizeBytes: row.file_size_bytes,
      fileHash: row.file_hash,
      transferStatus: row.transfer_status as SftpFileTransfer["transferStatus"],
      transferStartTime: row.transfer_start_time,
      transferEndTime: row.transfer_end_time,
      transferDurationMs: row.transfer_duration_ms,
      errorMessage: row.error_message,
      fileModifiedDate: row.file_modified_date,
      fileCreatedDate: row.file_created_date,
      processingStatus:
        row.processing_status as SftpFileTransfer["processingStatus"],
      processingNotes: row.processing_notes,
      metadata: row.metadata ? JSON.parse(row.metadata) : null,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    }));
  } catch (error) {
    logger.error("Failed to get files for processing:", error);
    throw error;
  }
}
