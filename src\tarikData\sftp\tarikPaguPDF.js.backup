const { koneksiLokal } = require("../db/database.js");
const fs = require("fs");
const path = require("path");
const pdfParse = require("pdf-parse");

const saveErrorLog = async (connection, folder, nmfile, error) => {
  const query = `
    INSERT INTO log_ftp.error_logs (folder, nmfile, error_message, stack_trace)
    VALUES (?, ?, ?, ?)
  `;
  const params = [folder, nmfile, error.message, error.stack || null];
  await connection.query(query, params);
  console.error(`Error logged for file: ${nmfile}`);
};

const parsePdf = async (connection, folder, nmfile) => {
  const pdfPath = path.join("C:\\KUMPULAN_ADK\\ADK_2024_DIPA", `${folder.substring(0, 7)}`, nmfile);
  try {
    const pdfBuffer = fs.readFileSync(pdfPath);
    const data = await pdfParse(pdfBuffer);
    const pages = data.text.split("\f");
    if (pages.length > 0) {
      return pages[0];
    } else {
      console.log(`No pages found in ${nmfile}`);
      return null;
    }
  } catch (error) {
    console.error(`Error reading or parsing PDF ${nmfile}:`, error.message);
    if (connection) {
      await saveErrorLog(connection, folder, nmfile, error);
    }
    return null;
  }
};

const extractData = async (connection, folder, nmfile, contents) => {
  try {
    const patterns = {
      pagu: /Sebesar\s*([0-9.]+)/,
      ds: /DS:\s*(\S+)/,
      kpa: /Kuasa Pengguna Anggaran.*?:\s*(.*?)(?=\n|$)/,
      bendahara: /Bendahara Pengeluaran.*?:\s*(.*?)(?=\n|$)/,
      ppspm: /Pejabat Penanda Tangan SPM.*?:\s*(.*?)(?=\n|$)/,
    };

    const results = {};
    for (const [key, pattern] of Object.entries(patterns)) {
      const match = contents.match(pattern);
      results[key] = match ? match[1].trim() : "";
    }

    if (results.pagu) {
      results.pagu = results.pagu.replace(/\./g, "");
      results.pagu = parseFloat(results.pagu);
    }

    if (isNaN(results.pagu) || results.pagu === "") {
      results.pagu = 0;
    }

    return results;
  } catch (error) {
    console.error(`Error extracting data from contents of file ${nmfile}:`, error.message);
    if (connection) {
      await saveErrorLog(connection, folder, nmfile, error);
    }
    return {
      pagu: 0,
      ds: "",
      kpa: "",
      bendahara: "",
      ppspm: "",
    };
  }
};

const bacaFilePDF = async () => {
  let connection;
  try {
    connection = await koneksiLokal();
    const [rows] = await connection.query(
      "SELECT DISTINCT folder, nmfile FROM monev2024.file_metadata WHERE RIGHT(nmfile,3)='pdf' AND STATUS='NEW' ORDER BY folder"
    );

    for (const { folder, nmfile } of rows) {
      try {
        const contents = await parsePdf(connection, folder, nmfile);
        if (!contents || !contents.includes("Rp.")) {
          console.log(`Skipping ${nmfile}: content invalid or "Rp." not found.`);
          continue;
        }

        const data = await extractData(connection, folder, nmfile, contents);

        // Ekstraksi data dari nama file
        const kdjendok = nmfile.slice(5, 7);
        const kddept = nmfile.slice(8, 11);
        const kdunit = nmfile.slice(12, 14);
        const kdsatker = nmfile.slice(15, 21);
        const kddekon = nmfile.slice(29, 30);
        const norev = nmfile.slice(31, 33);

        const query = `
          INSERT INTO ftp_pagu_pdf_2024 
          (kdjendok, kddept, kdunit, kdsatker, kddekon, norev, pagu, ds, kpa, bendahara, ppspm, status)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'OLD')
          ON DUPLICATE KEY UPDATE 
            pagu = VALUES(pagu), 
            ds = VALUES(ds), 
            kpa = VALUES(kpa), 
            bendahara = VALUES(bendahara), 
            ppspm = VALUES(ppspm);
        `;

        const params = [
          kdjendok,
          kddept,
          kdunit,
          kdsatker,
          kddekon,
          norev,
          data.pagu,
          data.ds,
          data.kpa,
          data.bendahara,
          data.ppspm,
        ];

        await connection.query(query, params);
        console.log(`Sedang Proses Update Pagu DIPA dari file: ${nmfile}`);

        // Update kolom status menjadi OLD
        await connection.query(
          `UPDATE monev2024.file_metadata 
           SET status = 'OLD' 
           WHERE folder = ? AND nmfile = ?`,
          [folder, nmfile]
        );
      } catch (error) {
        await saveErrorLog(connection, folder, nmfile, error);
      }
    }
  } catch (error) {
    console.error("Database connection error:", error.message);
  } finally {
    if (connection) await connection.end();
  }
};

module.exports = { bacaFilePDF };
