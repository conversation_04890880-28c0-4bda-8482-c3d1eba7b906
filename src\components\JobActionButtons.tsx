"use client";

import { <PERSON><PERSON> } from "@heroui/react";
import { Play, X } from "lucide-react";
import { useState } from "react";
import { JobStatus } from "@/types/job";
import { RunJobConfirmModal } from "./RunJobConfirmModal";

interface JobActionButtonsProps {
  job: JobStatus;
  isJobRunning: boolean;
  onRunJob: (jobId: string) => void;
  onCancelJob: (jobId: string) => void;
  onViewDetails: (job: JobStatus) => void;
  allSequenceJobs?: JobStatus[]; // All jobs in the same sequence (for dependency checking)
}

export const JobActionButtons = ({
  job,
  isJobRunning,
  onRunJob,
  onCancelJob,
  onViewDetails,
  allSequenceJobs = [],
}: JobActionButtonsProps) => {
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  const handleRunClick = () => {
    setIsConfirmModalOpen(true);
  };

  const handleConfirmRun = () => {
    onRunJob(job.id);
  };

  // Check if this job can run based on sequence dependencies
  const canRunJob = () => {
    // If job is not in a sequence, use normal rules
    if (!job.sequenceConfig) {
      return job.status !== "running" && job.enabled;
    }

    // If this is the first job in the sequence, it can run
    if (job.sequenceConfig.order === 1) {
      return job.status !== "running" && job.enabled;
    }

    // For subsequent jobs, check if all previous jobs have completed successfully
    const previousJobs = allSequenceJobs.filter(
      (seqJob) =>
        seqJob.sequenceConfig?.sequenceId === job.sequenceConfig?.sequenceId &&
        (seqJob.sequenceConfig?.order ?? 0) < (job.sequenceConfig?.order ?? 0)
    );

    // All previous jobs must have completed successfully
    const allPreviousCompleted = previousJobs.every(
      (prevJob) => prevJob.lastRunStatus === "completed"
    );

    return job.status !== "running" && job.enabled && allPreviousCompleted;
  };

  return (
    <>
      <div className="flex gap-4 flex-wrap justify-center">
        <Button
          className="w-[80px]"
          size="sm"
          color="success"
          variant="flat"
          startContent={<Play className="w-3 h-3" />}
          isLoading={isJobRunning && job.status === "running"}
          onPress={handleRunClick}
          isDisabled={!canRunJob()}
        >
          Run
        </Button>

        <Button
          className="w-[80px]"
          size="sm"
          color="danger"
          variant="flat"
          startContent={<X className="w-3 h-3" />}
          onPress={() => onCancelJob(job.id)}
          isDisabled={job.status !== "running"}
        >
          Cancel
        </Button>

        <Button
          className="w-[80px]"
          size="sm"
          variant="flat"
          onPress={() => onViewDetails(job)}
        >
          Details
        </Button>
      </div>

      <RunJobConfirmModal
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={handleConfirmRun}
        job={job}
        isLoading={isJobRunning && job.status === "running"}
      />
    </>
  );
};
