import SftpClient from "ssh2-sftp-client";
import fs from "fs";
import path from "path";
import { executeUpdate } from "./database";
import { logger, getJobDefinition } from "./jobManager";
import { isJobCancelled } from "./jobRunner";
import {
  ensureFileTrackingTable,
  saveFileMetadata as saveFileMetadataToCustomDb,
  FileTrackingDbConfig,
  FileMetadataRecord,
} from "./fileTrackingDatabase";

// SFTP configuration interface
interface SftpConfig {
  host: string;
  port: number;
  username: string;
  password: string;
}

// File metadata interface
interface FileMetadata {
  folder: string;
  filename: string;
  size: number;
  modifyTime: Date;
  localPath: string;
}

// Result interface
interface SftpDownloadResult {
  type: "bulk_download";
  unitsProcessed: number;
  status: "completed" | "failed";
  downloadPath: string;
  filesDownloaded: number;
  errors: string[];
}

/**
 * Check for job cancellation
 */
async function checkCancellation(jobId?: string): Promise<void> {
  if (jobId && isJobCancelled(jobId)) {
    throw new Error("Job was cancelled by user");
  }
}

/**
 * Log error to database using the existing job_execution_logs table
 */
async function logError(
  folder: string,
  filename: string,
  errorMessage: string,
  stackTrace?: string,
  executionId?: string
): Promise<void> {
  try {
    const currentExecutionId = executionId || `4-${Date.now()}`;

    // Create a detailed error message that includes context
    const detailedMessage = `SFTP Error - Folder: ${folder || "N/A"}, File: ${
      filename || "N/A"
    }, Error: ${errorMessage}`;

    await executeUpdate(
      `INSERT INTO job_execution_logs (execution_id, log_level, message, log_timestamp) 
       VALUES (?, ?, ?, NOW())`,
      [currentExecutionId, "error", detailedMessage]
    );
    logger.info("Error logged to database successfully", {
      folder,
      filename,
      errorMessage,
      executionId: currentExecutionId,
    });
  } catch (dbError) {
    logger.error("Failed to log error to database", {
      dbError: dbError instanceof Error ? dbError.message : "Unknown error",
      originalError: errorMessage,
    });
  }
}

/**
 * Save file metadata using configurable file tracking (if enabled)
 */
async function saveFileMetadata(
  metadata: FileMetadata,
  jobId?: string,
  executionId?: string
): Promise<void> {
  try {
    // Get job definition to check for file tracking configuration
    const jobDefinition = jobId ? await getJobDefinition(jobId) : null;
    const fileTrackingConfig = jobDefinition?.destination?.fileTracking;

    if (fileTrackingConfig?.enabled && fileTrackingConfig.database) {
      // Use configurable file tracking
      await saveWithConfigurableFileTracking(
        metadata,
        fileTrackingConfig.database,
        jobId,
        executionId
      );
    } else {
      // File tracking is not enabled for this job
      logger.debug("File tracking not enabled for this job", {
        jobId,
        executionId,
        filename: metadata.filename,
      });
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown database error";
    logger.error("Failed to save file metadata", {
      error: errorMessage,
      metadata,
      jobId,
      executionId,
    });
    await logError(
      metadata.folder,
      metadata.filename,
      `Metadata save failed: ${errorMessage}`,
      undefined,
      executionId
    );
  }
}

/**
 * Save file metadata using configurable file tracking system
 */
async function saveWithConfigurableFileTracking(
  metadata: FileMetadata,
  dbConfig: FileTrackingDbConfig,
  jobId?: string,
  executionId?: string
): Promise<void> {
  try {
    // Ensure the file tracking table exists
    await ensureFileTrackingTable(dbConfig);

    // Prepare metadata record
    const metadataRecord: FileMetadataRecord = {
      folder: metadata.folder,
      filename: metadata.filename,
      size: metadata.size,
      modifyTime: metadata.modifyTime,
      status: "NEW",
      jobId,
      executionId,
    };

    // Save to the configured database
    await saveFileMetadataToCustomDb(dbConfig, metadataRecord);

    logger.info("File metadata saved using configurable tracking", {
      database: dbConfig.database,
      table: dbConfig.table,
      folder: metadata.folder,
      filename: metadata.filename,
      jobId,
      executionId,
    });
  } catch (error) {
    logger.error(
      "Failed to save file metadata using configurable tracking:",
      error
    );
    throw error;
  }
}

/**
 * Check if file already exists locally
 */
function fileExistsLocally(localPath: string): boolean {
  return fs.existsSync(localPath);
}

/**
 * Ensure directory exists
 */
function ensureDirectoryExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    logger.info("Created local directory", { dirPath });
  }
}

/**
 * Download file from SFTP server
 */
async function downloadFile(
  sftp: SftpClient,
  remotePath: string,
  localPath: string,
  metadata: FileMetadata,
  jobId?: string,
  executionId?: string
): Promise<boolean> {
  try {
    logger.info("Starting file download", {
      remotePath,
      localPath,
      size: metadata.size,
    });

    await sftp.get(remotePath, localPath);

    // Save metadata to database
    await saveFileMetadata(metadata, jobId, executionId);

    logger.info("File downloaded successfully", {
      remotePath,
      localPath,
      size: metadata.size,
    });

    return true;
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown download error";

    logger.error("File download failed", {
      remotePath,
      localPath,
      error: errorMessage,
    });

    await logError(
      metadata.folder,
      metadata.filename,
      `Download failed: ${errorMessage}`,
      error instanceof Error ? error.stack : undefined,
      executionId
    );

    return false;
  }
}

/**
 * Process organizational unit directory
 */
async function processOrganizationalUnit(
  sftp: SftpClient,
  unitCode: string,
  baseLocalPath: string,
  jobId?: string
): Promise<{ downloaded: number; errors: number }> {
  // Get job definition to extract remote path and year dynamically
  const jobDef = await getJobDefinition(jobId || "4");
  const remotePath = jobDef?.dataSource.sftp?.remotePath || "adk_rkakl2024";
  const year = unitCode.slice(5, 9); // Extract year from unit code

  const remoteDir = `${remotePath}/${unitCode.slice(0, 2)}/${unitCode.slice(
    2,
    5
  )}/${year}`;

  let downloaded = 0;
  let errors = 0;
  const executionId = `${jobId || "4"}-${Date.now()}`;

  try {
    logger.info("Processing organizational unit", { unitCode, remoteDir });

    // Check for cancellation before processing unit
    await checkCancellation(jobId);

    // List files in remote directory
    const fileList = await sftp.list(remoteDir);

    if (!fileList || fileList.length === 0) {
      logger.info("No files found in directory", { remoteDir });
      return { downloaded, errors };
    }

    // Filter out directories, only process files
    const files = fileList.filter((file) => file.type !== "d");

    logger.info("Found files to process", {
      unitCode,
      fileCount: files.length,
    });

    // Check for cancellation before processing files
    await checkCancellation(jobId);

    for (const file of files) {
      // Check for cancellation before each file
      await checkCancellation(jobId);

      const filename = file.name;
      const modifyTimeInSeconds = file.modifyTime / 1000;
      const modifyTime = new Date(modifyTimeInSeconds * 1000);
      const folder = `${unitCode.slice(0, 2)}/${unitCode.slice(2, 5)}/${year}`;

      // Setup local paths
      const localDir = path.join(baseLocalPath, folder.slice(0, 6));
      const localFilePath = path.join(localDir, filename);

      // Check if file already exists locally
      if (fileExistsLocally(localFilePath)) {
        logger.info("File already exists locally, saving metadata only", {
          localFilePath,
        });

        // Save metadata even if file exists
        const metadata: FileMetadata = {
          folder,
          filename,
          size: file.size,
          modifyTime,
          localPath: localFilePath,
        };

        await saveFileMetadata(metadata, jobId, executionId);
        continue;
      }

      // Ensure local directory exists
      ensureDirectoryExists(localDir);

      // Prepare metadata
      const metadata: FileMetadata = {
        folder,
        filename,
        size: file.size,
        modifyTime,
        localPath: localFilePath,
      };

      // Download file
      const remotePath = `${remoteDir}/${filename}`;
      const success = await downloadFile(
        sftp,
        remotePath,
        localFilePath,
        metadata,
        jobId,
        executionId
      );

      if (success) {
        downloaded++;
      } else {
        errors++;
      }
    }

    logger.info("Organizational unit processing completed", {
      unitCode,
      downloaded,
      errors,
    });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown processing error";

    logger.error("Error processing organizational unit", {
      unitCode,
      remoteDir,
      error: errorMessage,
    });

    await logError(
      remoteDir,
      "N/A",
      `Unit processing failed: ${errorMessage}`,
      error instanceof Error ? error.stack : undefined,
      executionId
    );

    errors++;
  }

  return { downloaded, errors };
}

/**
 * Main SFTP bulk download function
 * This is the TypeScript equivalent of the tarikFtp function
 */
export async function performBulkSftpDownload(
  organizationalUnits: string[],
  localBasePath?: string,
  jobId?: string
): Promise<SftpDownloadResult> {
  // Get job definition to extract SFTP config and local path dynamically
  const jobDef = await getJobDefinition(jobId || "4");

  const sftpConfig: SftpConfig = {
    host: jobDef?.dataSource.sftp?.host || "aksesdata-anggaran.kemenkeu.go.id",
    port: jobDef?.dataSource.sftp?.port || 54321,
    username: jobDef?.dataSource.sftp?.username || "PA_DJPBN",
    password: jobDef?.dataSource.sftp?.password || "Sinergi100Persen",
  };

  // Use job definition's local path if not provided
  const finalLocalBasePath =
    localBasePath ||
    jobDef?.destination.localPath ||
    "C:/KUMPULAN_ADK/ADK_2024_DIPA";

  const sftp = new SftpClient();
  let totalDownloaded = 0;
  let totalErrors = 0;
  const errors: string[] = [];

  try {
    logger.info("Starting SFTP bulk download", {
      organizationalUnitsCount: organizationalUnits.length,
      localBasePath: finalLocalBasePath,
    });

    // Check for cancellation before starting
    await checkCancellation(jobId);

    // Connect to SFTP server
    await sftp.connect(sftpConfig);
    logger.info("Connected to SFTP server successfully", {
      host: sftpConfig.host,
      port: sftpConfig.port,
    });

    // Check for cancellation after connection
    await checkCancellation(jobId);

    // Process each organizational unit
    for (const unitCode of organizationalUnits) {
      try {
        // Check for cancellation before processing each unit
        await checkCancellation(jobId);

        const result = await processOrganizationalUnit(
          sftp,
          unitCode,
          finalLocalBasePath,
          jobId
        );
        totalDownloaded += result.downloaded;
        totalErrors += result.errors;

        // Check for cancellation after processing each unit
        await checkCancellation(jobId);
      } catch (error) {
        // If this is a cancellation error, re-throw it
        if (
          error instanceof Error &&
          error.message.includes("cancelled by user")
        ) {
          throw error;
        }

        const errorMessage =
          error instanceof Error ? error.message : "Unknown unit error";
        errors.push(`Unit ${unitCode}: ${errorMessage}`);
        totalErrors++;
        logger.error("Failed to process organizational unit", {
          unitCode,
          error: errorMessage,
        });
      }
    }

    logger.info("SFTP bulk download completed", {
      organizationalUnitsProcessed: organizationalUnits.length,
      totalDownloaded,
      totalErrors,
      localBasePath: finalLocalBasePath,
    });

    return {
      type: "bulk_download",
      unitsProcessed: organizationalUnits.length,
      status: totalErrors === 0 ? "completed" : "completed",
      downloadPath: finalLocalBasePath,
      filesDownloaded: totalDownloaded,
      errors,
    };
  } catch (connectionError) {
    const errorMessage =
      connectionError instanceof Error
        ? connectionError.message
        : "Unknown connection error";

    logger.error("SFTP connection failed", {
      error: errorMessage,
      config: {
        host: sftpConfig.host,
        port: sftpConfig.port,
        username: sftpConfig.username,
      },
    });

    await logError(
      "N/A",
      "N/A",
      `SFTP connection failed: ${errorMessage}`,
      connectionError instanceof Error ? connectionError.stack : undefined,
      `${jobId || "4"}-${Date.now()}`
    );

    // Throw error instead of returning failed result to properly fail the job
    throw new Error(`SFTP connection failed: ${errorMessage}`);
  } finally {
    try {
      await sftp.end();
      logger.info("SFTP connection closed");
    } catch (error) {
      logger.warn("Error closing SFTP connection", {
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
}

/**
 * Legacy function name for backward compatibility
 * This matches the original tarikFtp function but uses TypeScript
 * Now reads organizational units from job definition to eliminate duplication
 */
export async function tarikFtp(
  jobId: string = "4"
): Promise<SftpDownloadResult> {
  try {
    // Get organizational units from job definition instead of hardcoding
    const jobDef = await getJobDefinition(jobId);
    const organizationalUnits =
      (jobDef?.dataSource.options?.organisationalUnits as string[]) || [];

    if (organizationalUnits.length === 0) {
      logger.warn(
        "No organizational units found in job definition, using fallback list"
      );
      // Fallback to a minimal set if job definition is not available
      const fallbackUnits = ["010012024", "010022024", "010032024"];
      return await performBulkSftpDownload(fallbackUnits, undefined, jobId);
    }

    logger.info(
      `Retrieved ${organizationalUnits.length} organizational units from job definition`
    );
    return await performBulkSftpDownload(organizationalUnits, undefined, jobId);
  } catch (error) {
    logger.error("Failed to get organizational units from job definition", {
      error: error instanceof Error ? error.message : "Unknown error",
    });

    // Fallback to a minimal set in case of error
    const fallbackUnits = ["010012024", "010022024", "010032024"];

    logger.info("Using fallback organizational units due to error");
    return await performBulkSftpDownload(fallbackUnits, undefined, jobId);
  }
}
