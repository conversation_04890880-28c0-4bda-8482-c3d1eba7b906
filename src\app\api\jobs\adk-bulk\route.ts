import { NextResponse } from "next/server";
import { runDataPullingJob } from "@/lib/jobRunner";
import { getJobDefinition, logger } from "@/lib/jobManager";

export async function POST() {
  try {
    logger.info("Manual trigger for ADK RKAKL bulk download job requested");

    // Check if the ADK bulk download job exists
    const adkBulkJob = await getJobDefinition("4");
    if (!adkBulkJob) {
      return NextResponse.json(
        { error: "ADK RKAKL bulk download job not found" },
        { status: 404 }
      );
    }

    // Start the job execution (manual trigger)
    const success = await runDataPullingJob("4", "manual");

    if (success) {
      logger.info("ADK RKAKL bulk download job triggered successfully");
      return NextResponse.json({
        message: "ADK RKAKL bulk download job started successfully",
        jobId: "4",
        jobName: adkBulkJob.name,
        status: "running",
      });
    } else {
      logger.error("Failed to trigger ADK RKAKL bulk download job");
      return NextResponse.json(
        { error: "Failed to start ADK RKAKL bulk download job" },
        { status: 500 }
      );
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    logger.error("Error triggering ADK RKAKL bulk download job", {
      error: errorMessage,
    });

    return NextResponse.json(
      { error: "Internal server error", details: errorMessage },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Get the status of the ADK bulk download job
    const adkBulkJob = await getJobDefinition("4");

    if (!adkBulkJob) {
      return NextResponse.json(
        { error: "ADK RKAKL bulk download job not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      jobId: "4",
      name: adkBulkJob.name,
      description: adkBulkJob.description,
      enabled: adkBulkJob.enabled,
      schedule: adkBulkJob.schedule,
      dataSource: {
        type: adkBulkJob.dataSource.type,
        host: adkBulkJob.dataSource.sftp?.host,
        organisationalUnits:
          adkBulkJob.dataSource.options?.organisationalUnits || [],
      },
      destination: {
        type: adkBulkJob.destination.type,
        localPath: adkBulkJob.destination.localPath,
      },
      retryConfig: adkBulkJob.retryConfig,
    });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    logger.error("Error getting ADK RKAKL bulk download job status", {
      error: errorMessage,
    });

    return NextResponse.json(
      { error: "Internal server error", details: errorMessage },
      { status: 500 }
    );
  }
}
