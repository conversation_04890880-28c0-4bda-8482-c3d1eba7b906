import { JobDefinition } from "@/lib/jobManager";

// Type for destination options
export interface DestinationOptions {
  createDirectory?: boolean;
  preserveStructure?: boolean;
  trackInDatabase?: boolean;
  metadataTable?: string;
  errorLogTable?: string;
}

export interface JobAdminModalEnhancedProps {
  isOpen: boolean;
  onClose: () => void;
}

export type ViewMode =
  | "list"
  | "create"
  | "edit"
  | "sequences"
  | "create-sequence"
  | "edit-sequence";

export interface JobStats {
  total: number;
  active: number;
  disabled: number;
  oracle: number;
  sftp: number;
}

export interface SequenceInfo {
  position: number;
  total: number;
  sequenceName: string;
}

export interface SequenceForExecution {
  id: string;
  name: string;
  description?: string;
  jobCount: number;
}

export interface JobsByType {
  independentJobs: JobDefinition[];
  sequenceJobs: JobDefinition[];
}
