import { NextRequest, NextResponse } from "next/server";
import {
  saveJobSequence,
  loadJobSequence,
  loadJobSequences,
  deleteJobSequence,
  updateJobSequenceAssignment,
} from "@/lib/sequencePersistence";
import { JobSequence } from "@/lib/jobManager";
import { logger } from "@/lib/jobManager";
import { cronScheduler } from "@/lib/cronScheduler";
import JobSequenceManager from "@/lib/jobSequenceManager";

// Helper function to update job assignments for a sequence
async function updateSequenceJobAssignments(
  sequenceId: string,
  jobIds: string[]
): Promise<void> {
  try {
    // Import database functions
    const { executeUpdate } = await import("@/lib/database");

    // First, remove all existing assignments for this sequence
    await executeUpdate(
      "UPDATE job_definitions SET sequence_id = NULL, sequence_order = NULL WHERE sequence_id = ?",
      [sequenceId]
    );
    logger.info(`Cleared existing job assignments for sequence ${sequenceId}`);

    // Then assign the new jobs in order
    for (let i = 0; i < jobIds.length; i++) {
      const jobId = jobIds[i];
      const order = i + 1;
      await updateJobSequenceAssignment(jobId, sequenceId, order);
      logger.info(
        `Assigned job ${jobId} to sequence ${sequenceId} at order ${order}`
      );
    }
  } catch (error) {
    logger.error(
      `Failed to update job assignments for sequence ${sequenceId}:`,
      error
    );
    throw error;
  }
}

// GET /api/admin/sequences - Get all job sequences
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sequenceId = searchParams.get("id");

    if (sequenceId) {
      // Get specific sequence
      const sequence = await loadJobSequence(sequenceId);
      if (!sequence) {
        return NextResponse.json(
          { error: `Sequence not found: ${sequenceId}` },
          { status: 404 }
        );
      }
      return NextResponse.json({ sequence });
    } else {
      // Get all sequences
      const sequences = await loadJobSequences();
      return NextResponse.json({ sequences });
    }
  } catch (error) {
    logger.error("Failed to get sequences:", error);
    return NextResponse.json(
      { error: "Failed to retrieve sequences" },
      { status: 500 }
    );
  }
}

// POST /api/admin/sequences - Create new sequence
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const sequence: JobSequence = body.sequence;

    if (!sequence) {
      return NextResponse.json(
        { error: "Sequence data is required" },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!sequence.id || !sequence.name) {
      return NextResponse.json(
        { error: "Sequence ID and name are required" },
        { status: 400 }
      );
    }

    // Check if sequence already exists
    const existingSequence = await loadJobSequence(sequence.id);
    if (existingSequence) {
      return NextResponse.json(
        { error: `Sequence with ID ${sequence.id} already exists` },
        { status: 409 }
      );
    }

    // Save sequence
    await saveJobSequence(sequence);

    // Update job assignments if any jobs are specified
    if (sequence.jobs && sequence.jobs.length > 0) {
      await updateSequenceJobAssignments(sequence.id, sequence.jobs);
    }

    // Schedule sequence if enabled and has schedule
    if (sequence.enabled && sequence.schedule) {
      cronScheduler.scheduleSequence(
        sequence.id,
        sequence.schedule,
        sequence.name
      );
    }

    logger.info(`Sequence created: ${sequence.id} - ${sequence.name}`);

    // Broadcast both job and sequence updates to all connected clients
    try {
      const { broadcastAllUpdates } = await import("@/lib/sseManager");
      await broadcastAllUpdates();
    } catch (error) {
      logger.debug("Could not broadcast updates after sequence creation", {
        error,
      });
    }

    return NextResponse.json({
      message: "Sequence created successfully",
      sequence,
    });
  } catch (error) {
    logger.error("Failed to create sequence:", error);
    return NextResponse.json(
      { error: "Failed to create sequence" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/sequences - Update existing sequence
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const sequence: JobSequence = body.sequence;

    if (!sequence || !sequence.id) {
      return NextResponse.json(
        { error: "Sequence data with ID is required" },
        { status: 400 }
      );
    }

    // Check if sequence exists
    const existingSequence = await loadJobSequence(sequence.id);
    if (!existingSequence) {
      return NextResponse.json(
        { error: `Sequence not found: ${sequence.id}` },
        { status: 404 }
      );
    }

    // Save updated sequence
    await saveJobSequence(sequence);

    // Update job assignments
    await updateSequenceJobAssignments(sequence.id, sequence.jobs || []);

    // Update scheduling
    if (sequence.enabled && sequence.schedule) {
      cronScheduler.rescheduleSequence(
        sequence.id,
        sequence.schedule,
        sequence.name
      );
    } else {
      cronScheduler.unscheduleSequence(sequence.id);
    }

    logger.info(`Sequence updated: ${sequence.id} - ${sequence.name}`);

    // Broadcast both job and sequence updates to all connected clients
    try {
      const { broadcastAllUpdates } = await import("@/lib/sseManager");
      await broadcastAllUpdates();
    } catch (error) {
      logger.debug("Could not broadcast updates after sequence change", {
        error,
      });
    }

    return NextResponse.json({
      message: "Sequence updated successfully",
      sequence,
    });
  } catch (error) {
    logger.error("Failed to update sequence:", error);
    return NextResponse.json(
      { error: "Failed to update sequence" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/sequences - Delete sequence
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sequenceId = searchParams.get("id");

    if (!sequenceId) {
      return NextResponse.json(
        { error: "Sequence ID is required" },
        { status: 400 }
      );
    }

    // Check if sequence exists
    const existingSequence = await loadJobSequence(sequenceId);
    if (!existingSequence) {
      return NextResponse.json(
        { error: `Sequence not found: ${sequenceId}` },
        { status: 404 }
      );
    }

    // Check if sequence is currently running
    const sequenceManager = JobSequenceManager.getInstance();
    if (sequenceManager.isSequenceRunning(sequenceId)) {
      return NextResponse.json(
        { error: "Cannot delete a running sequence. Stop it first." },
        { status: 409 }
      );
    }

    // Unschedule sequence
    cronScheduler.unscheduleSequence(sequenceId);

    // Delete sequence
    await deleteJobSequence(sequenceId);

    logger.info(`Sequence deleted: ${sequenceId}`);

    // Broadcast both job and sequence updates to all connected clients
    try {
      const { broadcastAllUpdates } = await import("@/lib/sseManager");
      await broadcastAllUpdates();
    } catch (error) {
      logger.debug("Could not broadcast updates after sequence deletion", {
        error,
      });
    }

    return NextResponse.json({
      message: "Sequence deleted successfully",
    });
  } catch (error) {
    logger.error("Failed to delete sequence:", error);
    return NextResponse.json(
      { error: "Failed to delete sequence" },
      { status: 500 }
    );
  }
}
