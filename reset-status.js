const mysql = require("mysql2/promise");

async function resetFileStatuses() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    port: parseInt(process.env.DB_PORT || "3399"),
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "monev2025",
  });

  console.log("Resetting PDF file statuses from OLD to NEW...");
  const [result] = await connection.query(
    "UPDATE file_metadata SET status = 'NEW' WHERE RIGHT(nmfile,3)='pdf' AND status='OLD'"
  );
  console.log(`Updated ${result.affectedRows} files from OLD to NEW status`);

  // Verify the change
  const [rows] = await connection.query(
    "SELECT status, COUNT(*) as count FROM file_metadata WHERE RIGHT(nmfile,3)='pdf' GROUP BY status ORDER BY status"
  );
  console.log("New status distribution:");
  rows.forEach((row) => {
    console.log(`  ${row.status}: ${row.count} files`);
  });

  await connection.end();
}

resetFileStatuses().catch(console.error);
