{"templates": [{"id": "drop_create_summary_table", "name": "Drop & Create Summary Table", "description": "Template for dropping and recreating summary tables with complex JOINs", "category": "Table Management", "jobDefinition": {"name": "Daily Summary Table Recreation", "description": "Drop and recreate TEST_DROP_CREATE table with aggregated data from pagu_real_detail_harian_2025 and t_dept_2025", "schedule": "0 3 * * *", "enabled": true, "dataSource": {"type": "database_admin", "database_admin": {"host": "localhost", "port": 3306, "database": "monev2025", "username": "", "password": "", "connectionLimit": 5, "acquireTimeout": 60000, "timeout": 120000, "operationMode": "table_management", "operations": [], "rawSql": "-- Drop existing table if it exists\nDROP TABLE IF EXISTS TEST_DROP_CREATE;\n\n-- Create new table with aggregated data\nCREATE TABLE TEST_DROP_CREATE AS\nSELECT \n    a.kddept,\n    b.nmdept,\n    ROUND(SUM(a.pagu)/1, 0) AS PAGU_DIPA,\n    ROUND(SUM(real1 + real2 + real3 + real4 + real5 + real6 + real7 + real8 + real9 + real10 + real11 + real12) / 1, 0) AS REALISASI,\n    ROUND(SUM(a.blokir) / 1, 0) AS BLOKIR\nFROM \n    pagu_real_detail_harian_2025 a\nLEFT JOIN \n    t_dept_2025 b ON a.kddept = b.kddept\nGROUP BY \n    a.kddept;"}}, "destination": {"type": "database", "database": {"type": "mysql", "host": "localhost", "port": 3306, "database": "monev2025", "username": "", "password": "", "table": "job_results"}}, "retryConfig": {"maxRetries": 2, "retryDelay": 60}}}, {"id": "multi_step_table_recreation", "name": "Multi-Step Table Recreation", "description": "Template for complex table recreation with backup and validation steps", "category": "Advanced Operations", "jobDefinition": {"name": "Safe Table Recreation with Backup", "description": "Safely recreate tables with backup, validation, and rollback capabilities", "schedule": "0 2 * * 0", "enabled": true, "dataSource": {"type": "database_admin", "database_admin": {"host": "localhost", "port": 3306, "database": "monev2025", "username": "", "password": "", "connectionLimit": 5, "acquireTimeout": 60000, "timeout": 180000, "operationMode": "table_management", "operations": [{"id": "validate_source", "type": "DATA_EXTRACTION", "name": "Validate Source Data", "description": "Check if source tables have data", "sql": "SELECT COUNT(*) as source_count FROM pagu_real_detail_harian_2025", "continueOnError": false}, {"id": "backup_existing", "type": "CREATE_TABLE_AS_SELECT", "name": "Backup Existing Table", "description": "Create backup of existing table", "sql": "CREATE TABLE TEST_DROP_CREATE_BACKUP AS SELECT * FROM TEST_DROP_CREATE", "dependsOn": ["validate_source"], "continueOnError": true}, {"id": "drop_existing", "type": "DROP_TABLE", "name": "Drop Existing Table", "description": "Remove the existing table", "sql": "DROP TABLE IF EXISTS TEST_DROP_CREATE", "dependsOn": ["backup_existing"], "continueOnError": false}, {"id": "create_new", "type": "CREATE_TABLE_AS_SELECT", "name": "Create New Table", "description": "Create new table with fresh data", "sql": "CREATE TABLE TEST_DROP_CREATE AS SELECT a.kddept, b.nmdept, ROUND(SUM(a.pagu)/1, 0) AS PAGU_DIPA, ROUND(SUM(real1 + real2 + real3 + real4 + real5 + real6 + real7 + real8 + real9 + real10 + real11 + real12) / 1, 0) AS REALISASI, ROUND(SUM(a.blokir) / 1, 0) AS BLOKIR FROM pagu_real_detail_harian_2025 a LEFT JOIN t_dept_2025 b ON a.kddept = b.kddept GROUP BY a.kddept", "dependsOn": ["drop_existing"], "continueOnError": false}, {"id": "validate_result", "type": "DATA_EXTRACTION", "name": "Validate New Table", "description": "Check if new table was created successfully", "sql": "SELECT COUNT(*) as new_count FROM TEST_DROP_CREATE", "dependsOn": ["create_new"], "continueOnError": false}, {"id": "cleanup_backup", "type": "DROP_TABLE", "name": "Cleanup Backup", "description": "Remove backup table after successful creation", "sql": "DROP TABLE IF EXISTS TEST_DROP_CREATE_BACKUP", "dependsOn": ["validate_result"], "continueOnError": true}]}}, "destination": {"type": "database", "database": {"type": "mysql", "host": "localhost", "port": 3306, "database": "monev2025", "username": "", "password": "", "table": "job_results"}}, "retryConfig": {"maxRetries": 1, "retryDelay": 120}}}, {"id": "visual_query_template", "name": "Visual Query Builder Template", "description": "Template using visual query builder for complex JOINs", "category": "Visual Builder", "jobDefinition": {"name": "Visual Query - Department Summary", "description": "Create department summary using visual query builder", "schedule": "0 4 * * *", "enabled": true, "dataSource": {"type": "database_admin", "database_admin": {"host": "localhost", "port": 3306, "database": "monev2025", "username": "", "password": "", "connectionLimit": 5, "acquireTimeout": 60000, "timeout": 120000, "operationMode": "table_management", "operations": [], "visualQuery": {"sourceTables": ["pagu_real_detail_harian_2025", "t_dept_2025"], "relationships": [{"id": "rel_1", "sourceTable": "pagu_real_detail_harian_2025", "sourceColumn": "kddept", "targetTable": "t_dept_2025", "targetColumn": "kddept", "joinType": "LEFT"}], "selectedColumns": [{"id": "col_1", "table": "pagu_real_detail_harian_2025", "column": "kddept", "alias": "kddept"}, {"id": "col_2", "table": "t_dept_2025", "column": "nmdept", "alias": "nmdept"}, {"id": "col_3", "table": "pagu_real_detail_harian_2025", "column": "pagu", "aggregation": "SUM", "expression": "ROUND(${column}/1, 0)", "alias": "PAGU_DIPA"}, {"id": "col_4", "table": "pagu_real_detail_harian_2025", "column": "real1", "aggregation": "SUM", "expression": "ROUND(${column} + real2 + real3 + real4 + real5 + real6 + real7 + real8 + real9 + real10 + real11 + real12, 0)", "alias": "REALISASI"}, {"id": "col_5", "table": "pagu_real_detail_harian_2025", "column": "blokir", "aggregation": "SUM", "expression": "ROUND(${column}/1, 0)", "alias": "BLOKIR"}], "whereConditions": [], "groupByColumns": ["pagu_real_detail_harian_2025.kddept"], "orderByColumns": [{"column": "pagu_real_detail_harian_2025.kddept", "direction": "ASC"}], "targetTable": "TEST_DROP_CREATE", "operationType": "CREATE_TABLE_AS_SELECT"}}}, "destination": {"type": "database", "database": {"type": "mysql", "host": "localhost", "port": 3306, "database": "monev2025", "username": "", "password": "", "table": "job_results"}}, "retryConfig": {"maxRetries": 2, "retryDelay": 60}}}, {"id": "database_maintenance", "name": "Database Maintenance Workflow", "description": "Comprehensive database maintenance including cleanup and optimization", "category": "Maintenance", "jobDefinition": {"name": "Weekly Database Maintenance", "description": "Perform weekly database maintenance tasks including cleanup and optimization", "schedule": "0 1 * * 0", "enabled": true, "dataSource": {"type": "database_admin", "database_admin": {"host": "localhost", "port": 3306, "database": "monev2025", "username": "", "password": "", "connectionLimit": 3, "acquireTimeout": 60000, "timeout": 300000, "operationMode": "table_management", "operations": [{"id": "cleanup_temp_tables", "type": "MULTI_STEP_DDL", "name": "Cleanup Temporary Tables", "description": "Remove temporary tables older than 7 days", "sql": "DROP TABLE IF EXISTS temp_table_1; DROP TABLE IF EXISTS temp_table_2;", "continueOnError": true}, {"id": "archive_old_data", "type": "CREATE_TABLE_AS_SELECT", "name": "Archive Old Data", "description": "Archive data older than 1 year", "sql": "CREATE TABLE archived_data_2024 AS SELECT * FROM pagu_real_detail_harian_2025 WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR)", "dependsOn": ["cleanup_temp_tables"], "continueOnError": true}, {"id": "optimize_tables", "type": "MULTI_STEP_DDL", "name": "Optimize Tables", "description": "Optimize main tables for better performance", "sql": "OPTIMIZE TABLE pagu_real_detail_harian_2025; OPTIMIZE TABLE t_dept_2025;", "dependsOn": ["archive_old_data"], "continueOnError": true}, {"id": "update_statistics", "type": "MULTI_STEP_DDL", "name": "Update Table Statistics", "description": "Update table statistics for query optimization", "sql": "ANALYZE TABLE pagu_real_detail_harian_2025; ANALYZE TABLE t_dept_2025;", "dependsOn": ["optimize_tables"], "continueOnError": true}]}}, "destination": {"type": "database", "database": {"type": "mysql", "host": "localhost", "port": 3306, "database": "monev2025", "username": "", "password": "", "table": "job_results"}}, "retryConfig": {"maxRetries": 1, "retryDelay": 300}}}]}