import { JobStatus, DisplayStatus, SystemStatus } from "@/types/job";

export const getDisplayStatus = (
  job: JobStatus,
  systemStatus?: SystemStatus
): DisplayStatus => {
  if (job.status === "running") {
    return "running";
  }

  // Check if job is individually disabled
  if (!job.enabled) {
    return "disabled";
  }

  // Check if job is part of a disabled sequence
  if (job.parentSequence && !job.parentSequence.enabled) {
    return "disabled";
  }

  // If job is enabled but system is stopped, show service-stopped
  if (systemStatus?.status === "stopped") {
    return "service-stopped";
  }

  return "scheduled";
};

export const getDisplayStatusColor = (
  job: JobStatus,
  systemStatus?: SystemStatus
) => {
  const displayStatus = getDisplayStatus(job, systemStatus);
  switch (displayStatus) {
    case "running":
      return "primary";
    case "scheduled":
      return "warning";
    case "disabled":
      return "default";
    case "service-stopped":
      return "danger";
    default:
      return "default";
  }
};

export const getLastRunStatusColor = (status?: string) => {
  switch (status) {
    case "completed":
      return "success";
    case "failed":
      return "danger";
    case "cancelled":
      return "warning";
    default:
      return "default";
  }
};

export const getDisabledReason = (
  job: JobStatus
): "job" | "sequence" | null => {
  if (!job.enabled) {
    return "job";
  }

  if (job.parentSequence && !job.parentSequence.enabled) {
    return "sequence";
  }

  return null;
};
