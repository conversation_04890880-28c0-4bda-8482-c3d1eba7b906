"use client";

// ============================================================================
// IMPORTS
// ============================================================================

// React imports
import { useState } from "react";

// UI library imports
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
} from "@heroui/react";

// Animation imports
import { motion, AnimatePresence } from "framer-motion";

// Icon imports
import {
  Activity,
  CheckCircle,
  XCircle,
  Clock,
  Database,
  X,
  Maximize,
  Minimize,
} from "lucide-react";

// Type imports
import { JobStatus } from "@/types/job";

// Utility imports
import { getDisplayStatus, getDisabledReason } from "@/utils/jobHelpers";

// Component imports
import { LogViewer } from "./LogViewer";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface JobDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  job: JobStatus | null;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const JobDetailsModal = ({
  isOpen,
  onClose,
  job,
}: JobDetailsModalProps) => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [isFullscreen, setIsFullscreen] = useState(false);

  // ============================================================================
  // HELPER FUNCTIONS
  // ============================================================================

  const getDisplayStatusIcon = (job: JobStatus) => {
    const displayStatus = getDisplayStatus(job);
    switch (displayStatus) {
      case "running":
        return <Activity className="w-4 h-4" />;
      case "scheduled":
        return <Clock className="w-4 h-4" />;
      case "disabled":
        return <XCircle className="w-4 h-4" />;
      default:
        return <Database className="w-4 h-4" />;
    }
  };

  const getLastRunStatusIcon = (status?: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4" />;
      case "failed":
        return <XCircle className="w-4 h-4" />;
      case "cancelled":
        return <X className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <AnimatePresence>
      {isOpen && job && (
        <Modal
          isOpen={isOpen}
          onClose={onClose}
          size={isFullscreen ? "full" : "5xl"}
          backdrop="blur"
          scrollBehavior="inside"
          hideCloseButton
          classNames={{
            base: isFullscreen
              ? "h-screen w-screen animate-scale-in"
              : "h-[95vh] sm:h-[85vh] w-full sm:w-[64rem] max-w-[95vw] sm:max-w-[90vw] animate-scale-in",
            wrapper: "items-center justify-center flex p-2 sm:p-0",
            body: isFullscreen
              ? "h-[calc(100vh-160px)] overflow-hidden px-4 sm:px-6 py-2 sm:py-4"
              : "h-[calc(95vh-160px)] sm:h-[calc(85vh-160px)] overflow-hidden px-4 sm:px-6 py-2 sm:py-4",
            header:
              "px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200/50 flex-shrink-0",
            footer:
              "px-4 sm:px-6 py-3 sm:py-4 border-t border-gray-200/50 bg-gray-50/30 flex-shrink-0",
          }}
        >
          <ModalContent className={isFullscreen ? "" : "h-full flex flex-col"}>
            <ModalHeader className="flex flex-col gap-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-blue-100 text-blue-600">
                    {getDisplayStatusIcon(job)}
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {job.name}
                    </h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Job Execution Details & Live Monitoring
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {/* Fullscreen Toggle */}
                  <Button
                    variant="flat"
                    size="sm"
                    isIconOnly
                    startContent={
                      isFullscreen ? (
                        <Minimize className="w-3 h-3 sm:w-4 sm:h-4" />
                      ) : (
                        <Maximize className="w-3 h-3 sm:w-4 sm:h-4" />
                      )
                    }
                    onPress={() => setIsFullscreen(!isFullscreen)}
                    title={
                      isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"
                    }
                    className="border border-gray-300 hover:bg-gray-50 text-gray-600 min-w-8 sm:min-w-10"
                  />

                  {/* Close Button */}
                  <Button
                    variant="flat"
                    size="sm"
                    isIconOnly
                    onPress={onClose}
                    title="Close"
                    className="border border-gray-300 hover:bg-gray-50 text-gray-600 hover:text-gray-800 min-w-8 sm:min-w-10 text-sm sm:text-base"
                  >
                    ×
                  </Button>
                </div>
              </div>
            </ModalHeader>
            <ModalBody className="modal-content-container">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="space-y-2 modal-scrollable-content"
              >
                {/* Status Overview Cards */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {/* Status Card */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-2">
                    <div className="flex items-center gap-2">
                      {getDisplayStatusIcon(job)}
                      <div>
                        <p className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                          Status
                        </p>
                        <p className="text-sm font-semibold text-blue-900 dark:text-blue-100">
                          {(() => {
                            const displayStatus = getDisplayStatus(job);
                            if (displayStatus === "disabled") {
                              const disabledReason = getDisabledReason(job);
                              if (disabledReason === "sequence") {
                                return "Disabled (Seq)";
                              } else if (disabledReason === "job") {
                                return "Disabled";
                              }
                            }
                            return (
                              displayStatus.charAt(0).toUpperCase() +
                              displayStatus.slice(1)
                            );
                          })()}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Last Result Card */}
                  <div
                    className={`${
                      job.lastRunStatus === "completed"
                        ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
                        : job.lastRunStatus === "failed"
                        ? "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
                        : "bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700"
                    } border rounded-lg p-2`}
                  >
                    <div className="flex items-center gap-2">
                      {getLastRunStatusIcon(job.lastRunStatus)}
                      <div>
                        <p
                          className={`text-xs font-medium ${
                            job.lastRunStatus === "completed"
                              ? "text-green-600 dark:text-green-400"
                              : job.lastRunStatus === "failed"
                              ? "text-red-600 dark:text-red-400"
                              : "text-gray-600 dark:text-gray-400"
                          }`}
                        >
                          Last Result
                        </p>
                        <p
                          className={`text-sm font-semibold ${
                            job.lastRunStatus === "completed"
                              ? "text-green-900 dark:text-green-100"
                              : job.lastRunStatus === "failed"
                              ? "text-red-900 dark:text-red-100"
                              : "text-gray-900 dark:text-gray-100"
                          }`}
                        >
                          {job.lastRunStatus
                            ? job.lastRunStatus.charAt(0).toUpperCase() +
                              job.lastRunStatus.slice(1)
                            : "No runs"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Duration Card */}
                  <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-2">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                      <div>
                        <p className="text-xs text-purple-600 dark:text-purple-400 font-medium">
                          Duration
                        </p>
                        <p className="text-sm font-mono font-semibold text-purple-900 dark:text-purple-100">
                          {job.duration > 0 ? `${job.duration}s` : "N/A"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Scheduler Card */}
                  <div
                    className={`${
                      job.enabled
                        ? "bg-emerald-50 dark:bg-emerald-900/20 border-emerald-200 dark:border-emerald-800"
                        : "bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700"
                    } border rounded-lg p-2`}
                  >
                    <div className="flex items-center gap-2">
                      <Database className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                      <div>
                        <p
                          className={`text-xs font-medium ${
                            job.enabled
                              ? "text-emerald-600 dark:text-emerald-400"
                              : "text-gray-600 dark:text-gray-400"
                          }`}
                        >
                          Scheduler
                        </p>
                        <p
                          className={`text-sm font-semibold ${
                            job.enabled
                              ? "text-emerald-900 dark:text-emerald-100"
                              : "text-gray-900 dark:text-gray-100"
                          }`}
                        >
                          {job.enabled ? "Enabled" : "Disabled"}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Live Execution Logs */}
                <div className="space-y-3 mt-5">
                  <div className="flex items-center justify-between">
                    <h3 className="text-md font-semibold text-gray-900 dark:text-gray-100">
                      Live Execution Logs
                    </h3>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-400">
                        Real-time monitoring
                      </span>
                    </div>
                  </div>

                  <div className="rounded-lg overflow-hidden">
                    <LogViewer
                      logs={job.logs}
                      jobId={job.id}
                      isFullscreen={isFullscreen}
                    />
                  </div>
                </div>
              </motion.div>
            </ModalBody>
            <ModalFooter>
              <div className="flex items-center justify-end w-full">
                <Button
                  color="primary"
                  onPress={onClose}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-sm"
                >
                  Close
                </Button>
              </div>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}
    </AnimatePresence>
  );
};
