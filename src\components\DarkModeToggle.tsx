"use client";

// ============================================================================
// IMPORTS
// ============================================================================

// React imports
import React, { useEffect, useState } from "react";

// UI library imports
import { Button } from "@heroui/react";

// Icon imports
import { Sun, Moon } from "lucide-react";

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export default function DarkModeToggle() {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [isDark, setIsDark] = useState(false);
  const [mounted, setMounted] = useState(false);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  // Ensure component is mounted before accessing localStorage
  useEffect(() => {
    setMounted(true);

    // Check if dark mode is enabled
    const darkMode =
      localStorage.getItem("darkMode") === "true" ||
      (!localStorage.getItem("darkMode") &&
        window.matchMedia("(prefers-color-scheme: dark)").matches);

    setIsDark(darkMode);

    // Apply dark mode class to document
    if (darkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, []);

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const toggleDarkMode = () => {
    const newDarkMode = !isDark;
    setIsDark(newDarkMode);

    // Save to localStorage
    localStorage.setItem("darkMode", newDarkMode.toString());

    // Apply to document
    if (newDarkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <Button
        variant="flat"
        size="md"
        isIconOnly
        className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600"
        disabled
      >
        <Sun className="w-4 h-4" />
      </Button>
    );
  }

  return (
    <Button
      variant="flat"
      size="sm"
      isIconOnly
      onPress={toggleDarkMode}
      title={isDark ? "Switch to Light Mode" : "Switch to Dark Mode"}
      className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
    >
      {isDark ? (
        <Sun className="w-4 h-4 text-yellow-500" />
      ) : (
        <Moon className="w-4 h-4 text-blue-600" />
      )}
    </Button>
  );
}
