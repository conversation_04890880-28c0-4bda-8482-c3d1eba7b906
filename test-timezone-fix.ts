/**
 * Test script to verify timezone functionality for Indonesia (UTC+7)
 */

import {
  formatTableDate,
  formatDetailedDate,
  formatInServerTimezone,
  getTimezoneDisplayName,
  getUTCOffsetString,
  getUserTimezone,
  SERVER_TIMEZONE,
} from "./src/utils/timeUtils";

console.log("Testing New Timezone Functionality for Indonesia");
console.log("===============================================");

// Test current time
const now = new Date();
console.log(`Current time (browser local): ${now.toLocaleString()}`);
console.log(`Current time (user's timezone): ${formatDetailedDate(now)}`);
console.log(`Current time (server timezone): ${formatInServerTimezone(now)}`);
console.log(`User timezone: ${getUserTimezone()}`);
console.log(`Timezone display: ${getTimezoneDisplayName()}`);
console.log(`UTC offset: ${getUTCOffsetString()}`);
console.log(`Server timezone: ${SERVER_TIMEZONE}`);

console.log("\nTesting Table Format:");
console.log(`Table format (local): ${formatTableDate(now)}`);

console.log("\nTesting Cron Job Scenario:");
console.log("When you schedule a job for 'Daily at 2 AM':");

// Create a date representing 2 AM in Indonesia timezone (server time)
const twoAMJakarta = new Date();
twoAMJakarta.setHours(2, 0, 0, 0); // 2 AM local time

console.log(`- Server schedules job for: 2 AM Asia/Jakarta`);
console.log(`- Server time: ${formatInServerTimezone(twoAMJakarta)}`);
console.log(
  `- Your local time (Indonesia): ${formatDetailedDate(twoAMJakarta)}`
);
console.log(`- This means the job runs at 2 AM Indonesia time (as expected)`);

console.log("\nBenefits of the new system:");
console.log("✅ Server uses Asia/Jakarta timezone for local scheduling");
console.log("✅ You see times in your local Indonesia timezone");
console.log("✅ Clear indicators show which timezone is being displayed");
console.log("✅ No more confusion - 6 AM means 6 AM local time");
console.log("✅ All times use 24-hour format for clarity");

console.log("\nTesting 24-hour format:");
const testTimes = [
  new Date(2024, 0, 1, 2, 30, 0), // 2:30 AM
  new Date(2024, 0, 1, 14, 45, 0), // 2:45 PM
  new Date(2024, 0, 1, 23, 59, 59), // 11:59:59 PM
];

testTimes.forEach((time, index) => {
  console.log(`Test ${index + 1}: ${formatDetailedDate(time)}`);
});

console.log("\nTest completed successfully!");
