import type { JobDefinition } from "../../jobManager";
import type {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ult,
  JobExecutionContext,
  DatabaseOperationResult,
} from "../types";

/**
 * Abstract base class for all job handlers providing common functionality
 */
export abstract class BaseJobHandler implements JobHandler {
  public abstract readonly jobType: string;

  /**
   * Execute the job - must be implemented by subclasses
   */
  public abstract execute(context: JobExecutionContext): Promise<JobResult>;

  /**
   * Validate job configuration - can be overridden by subclasses
   */
  public validateConfig(jobDef: JobDefinition): boolean {
    // Basic validation - check that the job type matches
    return jobDef.dataSource.type === this.jobType;
  }

  /**
   * Get required permissions - can be overridden by subclasses
   */
  public getRequiredPermissions?(): string[];

  /**
   * Helper method to simulate network delay (used in some job types)
   */
  protected async simulateNetworkDelay(ms: number = 1000): Promise<void> {
    await new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Helper method to calculate records processed from data
   */
  protected calculateRecordsProcessed(data: unknown): number {
    if (Array.isArray(data)) {
      return data.length;
    } else if (data && typeof data === "object") {
      return 1;
    }
    return 0;
  }

  /**
   * Helper method to validate required configuration fields
   */
  protected validateRequiredFields(
    config: Record<string, unknown>,
    requiredFields: string[],
    configName: string = "configuration"
  ): void {
    const missingFields = requiredFields.filter(
      (field) => config[field] === undefined || config[field] === null
    );

    if (missingFields.length > 0) {
      throw new Error(
        `Missing required ${configName} fields: ${missingFields.join(", ")}`
      );
    }
  }

  /**
   * Helper method to create a standardized job result
   */
  protected createJobResult(
    recordsProcessed: number,
    data?: unknown,
    metadata?: Record<string, unknown>
  ): JobResult {
    return {
      recordsProcessed,
      data,
      metadata,
    };
  }

  /**
   * Helper method to handle database operation results
   */
  protected processDatabaseResults(
    results: DatabaseOperationResult[]
  ): JobResult {
    const successfulOperations = results.filter((r) => r.success).length;
    const totalExecutionTime = results.reduce(
      (sum, result) => sum + (result.executionTimeMs || 0),
      0
    );

    return this.createJobResult(successfulOperations, {
      type: "database_operations",
      operationCount: results.length,
      successfulOperations,
      failedOperations: results.length - successfulOperations,
      totalExecutionTimeMs: totalExecutionTime,
      results: results,
      message: "Database operations completed",
    });
  }

  /**
   * Helper method to handle file processing results
   */
  protected processFileResults(
    files: Array<{ name: string; size: number; status: string }>
  ): JobResult {
    const processedFiles = files.filter((f) => f.status === "processed");
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);

    return this.createJobResult(processedFiles.length, files, {
      totalFiles: files.length,
      processedFiles: processedFiles.length,
      totalSizeBytes: totalSize,
      message: `Processed ${processedFiles.length}/${files.length} files`,
    });
  }

  /**
   * Helper method to execute operations with progress tracking
   */
  protected async executeWithProgress<T>(
    context: JobExecutionContext,
    items: T[],
    processor: (item: T, index: number) => Promise<void>,
    operationName: string,
    progressInterval: number = 10
  ): Promise<void> {
    const total = items.length;

    for (let i = 0; i < total; i++) {
      await context.checkCancellation();

      await processor(items[i], i);

      // Log progress at intervals
      if ((i + 1) % progressInterval === 0 || i === total - 1) {
        await context.addProgressLog(i + 1, total, operationName);
      }
    }
  }

  /**
   * Helper method to handle errors consistently
   */
  protected async handleError(
    context: JobExecutionContext,
    error: unknown,
    operation: string
  ): Promise<never> {
    const errorMessage = error instanceof Error ? error.message : String(error);
    await context.addLog(`❌ ${operation} failed: ${errorMessage}`);

    if (error instanceof Error) {
      throw error;
    } else {
      throw new Error(`${operation} failed: ${errorMessage}`);
    }
  }

  /**
   * Helper method to log operation start
   */
  protected async logOperationStart(
    context: JobExecutionContext,
    operation: string,
    details?: string
  ): Promise<number> {
    const message = details
      ? `🚀 Starting ${operation}: ${details}`
      : `🚀 Starting ${operation}`;
    await context.addLog(message);
    return Date.now();
  }

  /**
   * Helper method to log operation completion
   */
  protected async logOperationComplete(
    context: JobExecutionContext,
    operation: string,
    startTime: number,
    details?: string
  ): Promise<void> {
    const duration = Date.now() - startTime;
    const message = details
      ? `✅ ${operation} completed in ${duration}ms: ${details}`
      : `✅ ${operation} completed in ${duration}ms`;
    await context.addLog(message);
  }
}
