import React from "react";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { ChevronDown, ChevronRight, Users, Play } from "lucide-react";
import { JobDefinition, JobSequence } from "@/lib/jobManager";
import { JobCard } from "./JobCard";
import { groupJobsBySequence, getSequenceInfo } from "../utils/jobUtils";

interface SequencesListProps {
  jobs: JobDefinition[];
  sequences: JobSequence[];
  isLoading: boolean;
  sequenceJobsExpanded: boolean;
  setSequenceJobsExpanded: (expanded: boolean) => void;
  onJobSelect: (job: JobDefinition) => void;
  onSequenceExecution: (sequenceId: string) => void;
}

export const SequencesList: React.FC<SequencesListProps> = ({
  jobs,
  sequences,
  isLoading,
  sequenceJobsExpanded,
  setSequenceJobsExpanded,
  onJobSelect,
  onSequenceExecution,
}) => {
  const groupedJobs = groupJobsBySequence(jobs);

  if (isLoading) {
    return <div className="text-center py-8">Loading sequences...</div>;
  }

  if (groupedJobs.size === 0) {
    return null;
  }

  const renderSequenceGroup = (
    sequenceId: string,
    jobs: JobDefinition[],
    sequenceInfo?: JobSequence
  ) => (
    <div
      key={sequenceId}
      className="border border-blue-200 rounded-2xl bg-white w-full"
    >
      <div className="flex items-center justify-between p-2 rounded-t-2xl bg-blue-50 border-b border-blue-200">
        <div className="flex flex-col items-start gap-0.5">
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4 text-blue-600" />
            <h4 className="font-semibold text-blue-800">
              {sequenceInfo?.name || `Sequence ${sequenceId}`}
            </h4>
            <Chip
              size="sm"
              color="primary"
              variant="flat"
              className="text-xs px-1 py-0 h-5"
            >
              {jobs.length} jobs
            </Chip>
            {sequenceInfo && (
              <Chip
                size="sm"
                color={sequenceInfo.enabled ? "success" : "default"}
                variant="flat"
                className="text-xs px-1 py-0 h-5"
              >
                {sequenceInfo.enabled ? "ON" : "OFF"}
              </Chip>
            )}
          </div>
          {sequenceInfo?.description && (
            <p className="text-xs text-gray-600 ml-6">
              {sequenceInfo.description}
            </p>
          )}
        </div>
        <div className="flex items-center gap-2">
          {sequenceInfo && (
            <Button
              size="sm"
              color="primary"
              variant="flat"
              startContent={<Play className="w-3 h-3" />}
              className="text-xs px-2 py-1 h-6"
              isDisabled={!sequenceInfo.enabled}
              onPress={() => onSequenceExecution(sequenceId)}
            >
              Execute
            </Button>
          )}
          <span className="text-xs text-blue-600">
            {sequenceInfo?.schedule || "Manual"}
          </span>
        </div>
      </div>
      <div className="p-4 space-y-2 w-full">
        {jobs.map((job, index) => {
          const sequenceJobInfo = {
            position: job.sequenceConfig?.order || index + 1,
            total: jobs.length,
            sequenceName: sequenceInfo?.name || `Sequence ${sequenceId}`,
          };
          return (
            <JobCard
              key={job.id}
              job={job}
              index={index}
              isSequenceJob={true}
              sequenceInfo={sequenceJobInfo}
              onJobSelect={onJobSelect}
            />
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="space-y-1 w-full">
      <div
        className="flex items-center justify-between p-2 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors w-full"
        onClick={() => setSequenceJobsExpanded(!sequenceJobsExpanded)}
      >
        <div className="flex items-center gap-2">
          {sequenceJobsExpanded ? (
            <ChevronDown className="w-4 h-4 text-blue-600" />
          ) : (
            <ChevronRight className="w-4 h-4 text-blue-600" />
          )}
          <h3 className="font-semibold text-blue-800">Sequence Jobs</h3>
          <Chip
            size="sm"
            color="primary"
            variant="flat"
            className="text-xs px-1 py-0 h-5"
          >
            {Array.from(groupedJobs.values()).reduce(
              (total, jobs) => total + jobs.length,
              0
            )}
          </Chip>
          <Chip
            size="sm"
            color="secondary"
            variant="flat"
            className="text-xs px-1 py-0 h-5"
          >
            {groupedJobs.size} sequences
          </Chip>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-blue-600">
            Jobs organized in sequences
          </span>
        </div>
      </div>

      {sequenceJobsExpanded && (
        <div className="space-y-2 p-2 sm:p-4 w-full">
          {Array.from(groupedJobs.entries()).map(([sequenceId, jobs]) => {
            const sequenceInfo = getSequenceInfo(sequenceId, sequences);
            return renderSequenceGroup(sequenceId, jobs, sequenceInfo);
          })}
        </div>
      )}
    </div>
  );
};
