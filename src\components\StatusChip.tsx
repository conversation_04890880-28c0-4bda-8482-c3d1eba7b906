"use client";

// ============================================================================
// IMPORTS
// ============================================================================

// UI library imports
import { Chip } from "@heroui/react";

// Animation imports
import { motion } from "framer-motion";

// Icon imports
import {
  Activity,
  CheckCircle,
  XCircle,
  Clock,
  Database,
  X,
  User,
  Timer,
} from "lucide-react";

// Type imports
import { JobStatus, SystemStatus } from "@/types/job";

// Utility imports
import {
  getDisplayStatus,
  getDisplayStatusColor,
  getLastRunStatusColor,
  getDisabledReason,
} from "@/utils/jobHelpers";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface StatusChipProps {
  job: JobStatus;
  systemStatus?: SystemStatus;
}

interface LastRunStatusChipProps {
  status?: string;
  triggerType?: "manual" | "automatic";
}

// ============================================================================
// STATUS CHIP COMPONENT
// ============================================================================

export const StatusChip = ({ job, systemStatus }: StatusChipProps) => {
  // ============================================================================
  // HELPER FUNCTIONS
  // ============================================================================

  const getDisplayStatusIcon = (
    job: JobStatus,
    systemStatus?: SystemStatus
  ) => {
    const displayStatus = getDisplayStatus(job, systemStatus);
    switch (displayStatus) {
      case "running":
        return <Activity className="w-4 h-4" />;
      case "scheduled":
        return <Clock className="w-4 h-4" />;
      case "disabled":
        return <XCircle className="w-4 h-4" />;
      case "service-stopped":
        return <X className="w-4 h-4" />;
      default:
        return <Database className="w-4 h-4" />;
    }
  };

  const getStatusText = (job: JobStatus, systemStatus?: SystemStatus) => {
    const displayStatus = getDisplayStatus(job, systemStatus);

    if (displayStatus === "service-stopped") {
      return "Service Stopped";
    }

    if (displayStatus === "disabled") {
      const disabledReason = getDisabledReason(job);
      if (disabledReason === "sequence") {
        return `Disabled (Sequence)`;
      } else if (disabledReason === "job") {
        return "Disabled";
      }
    }

    return displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1);
  };

  return (
    <motion.div
      key={`${job.id}-${job.status}-${job.enabled}-${job.parentSequence?.enabled}-${systemStatus?.status}`}
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <Chip
        color={getDisplayStatusColor(job, systemStatus)}
        variant="flat"
        startContent={
          <motion.div
            key={`${job.id}-icon-${getDisplayStatus(job, systemStatus)}`}
            initial={{ rotate: -180, opacity: 0 }}
            animate={{ rotate: 0, opacity: 1 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          >
            {getDisplayStatusIcon(job, systemStatus)}
          </motion.div>
        }
        size="sm"
      >
        {getStatusText(job, systemStatus)}
      </Chip>
    </motion.div>
  );
};

// ============================================================================
// LAST RUN STATUS CHIP COMPONENT
// ============================================================================

export const LastRunStatusChip = ({
  status,
  triggerType,
}: LastRunStatusChipProps) => {
  // ============================================================================
  // EARLY RETURN
  // ============================================================================

  if (!status) return <span className="text-gray-400">-</span>;

  // ============================================================================
  // HELPER FUNCTIONS
  // ============================================================================

  const getLastRunStatusIcon = (status?: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4" />;
      case "failed":
        return <XCircle className="w-4 h-4" />;
      case "cancelled":
        return <X className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <motion.div
      key={`lastrun-${status}`}
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <Chip
        color={getLastRunStatusColor(status)}
        variant="flat"
        startContent={
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            {getLastRunStatusIcon(status)}
          </motion.div>
        }
        size="sm"
      >
        <div className="flex items-center gap-1">
          <span>{status.charAt(0).toUpperCase() + status.slice(1)}</span>
          {triggerType && (
            <span className="text-xs opacity-75 flex items-center gap-0.5">
              {triggerType === "manual" ? (
                <>
                  <User className="w-3 h-3" />
                  <span>Manual</span>
                </>
              ) : (
                <>
                  <Timer className="w-3 h-3" />
                  <span>Auto</span>
                </>
              )}
            </span>
          )}
        </div>
      </Chip>
    </motion.div>
  );
};
