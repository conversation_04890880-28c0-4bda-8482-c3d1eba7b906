# Legacy Code Removal Checklist

## Pre-Removal Verification Steps

### 1. **Production Testing** ✅ **REQUIRED**
- [ ] Test each job type (oracle, mysql, database_admin, sftp, pdf_dipa, adk_processing) in production
- [ ] Verify all job configurations work with the modular system
- [ ] Monitor job execution logs for any fallback usage
- [ ] Run jobs with different configurations (simple queries, complex operations, bulk operations)

### 2. **Error Handling Verification** ✅ **REQUIRED**
- [ ] Test job cancellation works correctly
- [ ] Test error scenarios (invalid configs, connection failures, etc.)
- [ ] Verify error messages are consistent and helpful
- [ ] Test timeout scenarios

### 3. **Performance Comparison** ⚠️ **RECOMMENDED**
- [ ] Compare execution times between modular and legacy systems
- [ ] Monitor memory usage during job execution
- [ ] Test with large datasets to ensure no performance regression

### 4. **Configuration Compatibility** ✅ **REQUIRED**
- [ ] Test all existing job configurations without modification
- [ ] Verify complex configurations (database_admin with multiple operations)
- [ ] Test edge cases and optional parameters

## Safe Removal Strategy

### Phase 1: Monitor Fallback Usage (1-2 weeks)
```typescript
// Add monitoring to detect fallback usage
await addJobLog(
  jobDef.id,
  `⚠️ FALLBACK USED: Modular system failed, using legacy code for ${jobDef.dataSource.type}`
);

// Add metrics/alerts for fallback usage
logger.warn(`Legacy fallback used for job ${jobDef.id}`, {
  jobType: jobDef.dataSource.type,
  error: errorMessage
});
```

### Phase 2: Disable Fallback (Test Period)
```typescript
// Replace fallback with error throwing
throw new Error(
  `Modular job handler failed and legacy fallback is disabled: ${errorMessage}`
);
```

### Phase 3: Remove Legacy Code
Only after confirming no fallback usage for 1-2 weeks.

## Functions Safe to Remove

### ✅ **Confirmed Safe** (only used in fallback):
- `pullFromOracle()`
- `pullFromMySQL()`
- `executeDatabaseAdmin()`
- `pullFromSFTP()`
- `pullFromPdfDipa()`
- `pullFromAdkProcessing()`

### ⚠️ **Check Dependencies** (may have other references):
- Helper functions within the above functions
- Shared utility functions
- Import statements that might be used elsewhere

## Risk Assessment

### **Low Risk** ✅
- Functions are only called from the fallback switch statement
- Modular system has been tested and works correctly
- All job types have equivalent modular handlers

### **Medium Risk** ⚠️
- Some edge cases might not be covered by modular handlers
- Complex configurations might behave differently
- Performance characteristics might differ

### **High Risk** ❌
- Production systems haven't been tested with modular system
- No monitoring of fallback usage
- No rollback plan if issues arise

## Recommended Approach

1. **Week 1-2**: Add fallback monitoring and run in production
2. **Week 3**: Analyze fallback usage, fix any issues found
3. **Week 4**: Disable fallback, monitor for errors
4. **Week 5+**: Remove legacy code if no issues detected

## Rollback Plan

Keep legacy functions in a separate file for quick restoration:
```typescript
// src/lib/jobs/legacy-backup.ts
// Move all legacy functions here before deletion
// Can be quickly restored if needed
```
