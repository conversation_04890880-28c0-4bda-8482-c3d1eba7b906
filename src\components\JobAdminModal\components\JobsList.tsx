import React from "react";
import { Chip } from "@heroui/react";
import { ChevronDown, ChevronRight } from "lucide-react";
import { JobDefinition } from "@/lib/jobManager";
import { JobCard } from "./JobCard";
import { separateJobsByType } from "../utils/jobUtils";

interface JobsListProps {
  jobs: JobDefinition[];
  isLoading: boolean;
  independentJobsExpanded: boolean;
  setIndependentJobsExpanded: (expanded: boolean) => void;
  onJobSelect: (job: JobDefinition) => void;
}

export const JobsList: React.FC<JobsListProps> = ({
  jobs,
  isLoading,
  independentJobsExpanded,
  setIndependentJobsExpanded,
  onJobSelect,
}) => {
  const { independentJobs } = separateJobsByType(jobs);

  if (isLoading) {
    return <div className="text-center py-8">Loading jobs...</div>;
  }

  if (jobs.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        No jobs found. Create your first job to get started.
      </div>
    );
  }

  if (independentJobs.length === 0) {
    return null;
  }

  return (
    <div className="space-y-1 w-full">
      <div
        className="flex items-center justify-between p-3 bg-gray-100 rounded-2xl cursor-pointer hover:bg-gray-200 transition-colors w-full"
        onClick={() => setIndependentJobsExpanded(!independentJobsExpanded)}
      >
        <div className="flex items-center gap-2">
          {independentJobsExpanded ? (
            <ChevronDown className="w-4 h-4 text-gray-500" />
          ) : (
            <ChevronRight className="w-4 h-4 text-gray-500" />
          )}
          <h3 className="font-semibold text-gray-800">Independent Jobs</h3>
          <Chip
            size="sm"
            color="default"
            variant="flat"
            className="text-xs px-1 py-0 h-5"
          >
            {independentJobs.length}
          </Chip>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">
            Jobs that run independently
          </span>
        </div>
      </div>

      {independentJobsExpanded && (
        <div className="grid gap-1 p-2 sm:p-4 w-full">
          {independentJobs.map((job, index) => (
            <JobCard
              key={job.id}
              job={job}
              index={index}
              isSequenceJob={false}
              onJobSelect={onJobSelect}
            />
          ))}
        </div>
      )}
    </div>
  );
};
