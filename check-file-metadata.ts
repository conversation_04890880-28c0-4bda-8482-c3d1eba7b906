// Check file_metadata table for files with status "NEW"
import { initializeDatabase } from './src/lib/database';
import { queryFilesByStatus } from './src/lib/fileTrackingDatabase';

async function checkFileMetadata() {
  try {
    console.log('🔍 Checking file_metadata table for files to process...\n');
    
    console.log('Initializing database...');
    await initializeDatabase();
    
    // File tracking configuration (same as what we just set for the job)
    const fileTrackingConfig = {
      host: "localhost",
      port: 3306,
      username: "root", 
      password: "",
      database: "monev2025",
      table: "file_metadata"
    };
    
    console.log('Querying files with status "NEW"...');
    const newFiles = await queryFilesByStatus(fileTrackingConfig, "NEW");
    
    console.log(`📊 Found ${newFiles.length} files with status "NEW"`);
    
    if (newFiles.length > 0) {
      console.log('\n📋 Files ready for processing:');
      newFiles.slice(0, 10).forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.nmfile} (ID: ${file.id}, Folder: ${file.folder})`);
      });
      
      if (newFiles.length > 10) {
        console.log(`  ... and ${newFiles.length - 10} more files`);
      }
      
      console.log('\n✅ ADK processing job should be able to process these files now!');
      console.log('💡 You can now run the ADK processing job to test it.');
    } else {
      console.log('\n⚠️  No files with status "NEW" found.');
      console.log('💡 You may need to:');
      console.log('   1. Run an SFTP job to download new files');
      console.log('   2. Or manually insert test records into file_metadata table');
      console.log('   3. Or check if files have different status values');
      
      // Check what statuses exist
      console.log('\n🔍 Checking what file statuses exist...');
      try {
        const allFiles = await queryFilesByStatus(fileTrackingConfig, "PROCESSED");
        console.log(`   - PROCESSED: ${allFiles.length} files`);
      } catch (e) {
        console.log('   - Could not query PROCESSED files');
      }
      
      try {
        const errorFiles = await queryFilesByStatus(fileTrackingConfig, "ERROR");
        console.log(`   - ERROR: ${errorFiles.length} files`);
      } catch (e) {
        console.log('   - Could not query ERROR files');
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error checking file metadata:', error);
    process.exit(1);
  }
}

checkFileMetadata();
