import React, { useState } from "react";
import { Input, Select, SelectItem, Divider, Tabs, Tab } from "@heroui/react";
import { Eye, EyeOff, Upload, Database } from "lucide-react";
import { JobDefinition } from "@/lib/jobManager";
import { DestinationOptions } from "../../utils/types";
import { AdkTableMappingDisplay } from "./AdkTableMappingDisplay";
import { AdkWorkflowVisualization } from "./AdkWorkflowVisualization";

interface DestinationFormProps {
  editedJob: JobDefinition;
  updateNestedField: (path: string, value: unknown) => void;
}

export const DestinationForm: React.FC<DestinationFormProps> = ({
  editedJob,
  updateNestedField,
}) => {
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>(
    {}
  );

  const togglePasswordVisibility = (field: string) => {
    setShowPasswords((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const renderTableManagementInfo = () => (
    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
      <h5 className="font-medium text-green-800 mb-2">
        🔧 Table Management Mode
      </h5>
      <p className="text-sm text-green-700 mb-3">
        DDL operations (CREATE/DROP/ALTER) don&apos;t export data to
        destinations. Results are logged for monitoring and audit purposes.
      </p>
      <div className="bg-white p-3 rounded border">
        <p className="text-sm font-medium text-gray-700 mb-1">
          Execution Results Saved To:
        </p>
        <p className="text-sm text-gray-600">
          📄 Local file:{" "}
          <code className="bg-gray-100 px-1 rounded">
            ./output/ddl-execution-logs.json
          </code>
        </p>
        <p className="text-sm text-gray-600">
          📊 Job logs: Available in job execution history
        </p>
      </div>
      <p className="text-xs text-green-600 mt-2">
        Perfect for your DROP/CREATE table operations!
      </p>
    </div>
  );

  const renderDataExtractionInfo = () => (
    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
      <h5 className="font-medium text-blue-800 mb-2">
        📊 Data Extraction Mode
      </h5>
      <p className="text-sm text-blue-700 mb-3">
        SELECT queries will extract data and export it to your chosen
        destination.
      </p>
    </div>
  );

  const renderWorkflowInfo = () => (
    <div className="bg-purple-50 p-4 rounded-lg border border-purple-200 mb-4">
      <h5 className="font-medium text-purple-800 mb-2">
        ⚙️ Complex Workflow Mode
      </h5>
      <p className="text-sm text-purple-700 mb-3">
        Multi-step operations may include both DDL and data extraction.
        Configure destination for data extraction steps.
      </p>
    </div>
  );

  const renderDatabaseDestination = () => (
    <div className="form-group">
      <Select
        label="Database Type"
        selectedKeys={[editedJob.destination.database?.type || "mysql"]}
        onSelectionChange={(keys) => {
          const dbType = Array.from(keys)[0] as "mysql" | "oracle" | "postgres";
          updateNestedField("destination.database.type", dbType);
        }}
        classNames={{
          label: "text-sm font-medium text-gray-700",
          trigger: "border-gray-300 hover:border-gray-400",
        }}
      >
        <SelectItem key="mysql">MySQL</SelectItem>
        <SelectItem key="oracle">Oracle</SelectItem>
        <SelectItem key="postgres">PostgreSQL</SelectItem>
      </Select>
      <div className="form-row">
        <Input
          label="Host"
          value={editedJob.destination.database?.host || ""}
          onChange={(e) =>
            updateNestedField("destination.database.host", e.target.value)
          }
          placeholder="localhost"
          classNames={{
            input: "text-sm",
            label: "text-sm font-medium text-gray-700",
          }}
        />
        <Input
          label="Port"
          type="number"
          value={editedJob.destination.database?.port?.toString() || ""}
          onChange={(e) =>
            updateNestedField(
              "destination.database.port",
              parseInt(e.target.value) || 3306
            )
          }
          placeholder="3306"
          classNames={{
            input: "text-sm",
            label: "text-sm font-medium text-gray-700",
          }}
        />
      </div>
      <Input
        label="Database Name"
        value={editedJob.destination.database?.database || ""}
        onChange={(e) =>
          updateNestedField("destination.database.database", e.target.value)
        }
        placeholder="your_database"
        classNames={{
          input: "text-sm",
          label: "text-sm font-medium text-gray-700",
        }}
      />
      <div className="grid grid-cols-2 gap-3">
        <Input
          label="Username"
          value={editedJob.destination.database?.username || ""}
          onChange={(e) =>
            updateNestedField("destination.database.username", e.target.value)
          }
        />
        <Input
          label="Password"
          type={showPasswords.destination ? "text" : "password"}
          value={editedJob.destination.database?.password || ""}
          onChange={(e) =>
            updateNestedField("destination.database.password", e.target.value)
          }
          endContent={
            <button
              type="button"
              onClick={() => togglePasswordVisibility("destination")}
              className="focus:outline-none"
            >
              {showPasswords.destination ? (
                <EyeOff className="w-4 h-4 text-gray-400" />
              ) : (
                <Eye className="w-4 h-4 text-gray-400" />
              )}
            </button>
          }
        />
      </div>
      {/* Hide Table Name field for ADK Processing since tables are automatically determined */}
      {editedJob.dataSource.type !== "adk_processing" && (
        <Input
          label="Table Name"
          value={editedJob.destination.database?.table || ""}
          onChange={(e) =>
            updateNestedField("destination.database.table", e.target.value)
          }
          placeholder="destination_table"
        />
      )}

      {/* Show ADK Processing specific information */}
      {editedJob.dataSource.type === "adk_processing" && (
        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
          <div className="flex items-start gap-2">
            <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-0.5">
              <Database className="w-3 h-3 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-blue-800 mb-1">
                Automatic Table Distribution
              </p>
              <p className="text-xs text-blue-700">
                ADK processing automatically routes XML files to appropriate
                tables (d_akun, d_item, d_kmpnen, etc.) based on filename
                patterns. Only the database name is required.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const shouldShowDestinationOptions = () => {
    // Show destination options for all job types except database_admin table_management mode
    // This includes: oracle, mysql, sftp, pdf_dipa, adk_processing, and database_admin data_extraction/workflow modes
    return (
      editedJob.dataSource.type !== "database_admin" ||
      editedJob.dataSource.database_admin?.operationMode ===
        "data_extraction" ||
      editedJob.dataSource.database_admin?.operationMode === "workflow"
    );
  };

  // ADK Processing specific UI components
  const renderAdkProcessingInfo = () => (
    <div className="space-y-6">
      {/* Header Information */}
      <div className="text-sm text-green-600 bg-green-50 p-4 rounded-lg border border-green-200">
        <div className="flex items-start gap-3">
          <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-0.5">
            <Database className="w-3 h-3 text-green-600" />
          </div>
          <div>
            <strong className="text-green-700">
              ADK Multi-Table Destination Configuration
            </strong>
            <p className="mt-1 text-green-600">
              ADK processing uses <strong>dual database connections</strong> and
              distributes data across <strong>14 target tables</strong>{" "}
              automatically.
            </p>
            <div className="mt-3 grid grid-cols-2 gap-4 text-xs">
              <div className="bg-white p-2 rounded border border-green-200">
                <strong>Primary Database:</strong>
                <br />
                Contains the 14 ADK target tables (d_akun, d_item, etc.)
              </div>
              <div className="bg-white p-2 rounded border border-green-200">
                <strong>File Tracking Database:</strong>
                <br />
                Manages file_metadata table for processing status
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Tabbed Interface */}
      <Tabs
        aria-label="ADK Processing Configuration"
        color="primary"
        variant="underlined"
        classNames={{
          tabList:
            "gap-6 w-full relative rounded-none p-0 border-b border-divider",
          cursor: "w-full bg-primary",
          tab: "max-w-fit px-0 h-12",
          tabContent: "group-data-[selected=true]:text-primary",
        }}
      >
        <Tab
          key="workflow"
          title={
            <div className="flex items-center space-x-2">
              <Database className="w-4 h-4" />
              <span>Processing Workflow</span>
            </div>
          }
        >
          <div className="py-4">
            <AdkWorkflowVisualization />
          </div>
        </Tab>

        <Tab
          key="tables"
          title={
            <div className="flex items-center space-x-2">
              <Upload className="w-4 h-4" />
              <span>Table Mappings</span>
            </div>
          }
        >
          <div className="py-4">
            <AdkTableMappingDisplay />
          </div>
        </Tab>
      </Tabs>
    </div>
  );

  return (
    <div className="form-section animate-fade-in">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center">
          <Upload className="w-4 h-4 text-green-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Destination</h3>
          <p className="text-sm text-gray-600">
            Configure where to send extracted data
          </p>
        </div>
      </div>

      <div className="form-group">
        {/* Show different content based on operation mode */}
        {editedJob.dataSource.type === "database_admin" &&
          editedJob.dataSource.database_admin?.operationMode ===
            "table_management" &&
          renderTableManagementInfo()}

        {editedJob.dataSource.type === "database_admin" &&
          editedJob.dataSource.database_admin?.operationMode ===
            "data_extraction" &&
          renderDataExtractionInfo()}

        {editedJob.dataSource.type === "database_admin" &&
          editedJob.dataSource.database_admin?.operationMode === "workflow" &&
          renderWorkflowInfo()}

        {/* Show ADK processing information */}
        {editedJob.dataSource.type === "adk_processing" &&
          renderAdkProcessingInfo()}

        {/* Show destination options only for data extraction and workflow modes */}
        {shouldShowDestinationOptions() && (
          <div className="form-group">
            <Select
              label="Destination Type"
              selectedKeys={[editedJob.destination.type]}
              onSelectionChange={(keys) => {
                const type = Array.from(keys)[0] as
                  | "database"
                  | "file"
                  | "local";
                updateNestedField("destination.type", type);
              }}
              classNames={{
                label: "text-sm font-medium text-gray-700",
                trigger: "border-gray-300 hover:border-gray-400",
              }}
            >
              <SelectItem key="database">Database</SelectItem>
              <SelectItem key="local">Local File System</SelectItem>
            </Select>

            {editedJob.destination.type === "database" &&
              renderDatabaseDestination()}

            {editedJob.destination.type === "local" && (
              <div className="space-y-4">
                <Input
                  label="Local Path"
                  value={editedJob.destination.localPath || ""}
                  onChange={(e) =>
                    updateNestedField("destination.localPath", e.target.value)
                  }
                  placeholder="C:/data/downloads"
                  isRequired
                />

                {/* Database Tracking Configuration */}
                <div className="space-y-3">
                  <Divider />
                  <h5 className="text-sm font-semibold text-gray-700">
                    Database Tracking Configuration
                  </h5>

                  {/* Compact two-column layout for table references */}
                  <div className="grid grid-cols-2 gap-3">
                    <Input
                      label="Metadata Table"
                      value={
                        (editedJob.destination.options as DestinationOptions)
                          ?.metadataTable || ""
                      }
                      onChange={(e) =>
                        updateNestedField(
                          "destination.options.metadataTable",
                          e.target.value
                        )
                      }
                      placeholder="monev2025.file_metadata"
                      description="database.table_name"
                      isRequired
                    />
                    <Input
                      label="Error Log Table"
                      value={
                        (editedJob.destination.options as DestinationOptions)
                          ?.errorLogTable || ""
                      }
                      onChange={(e) =>
                        updateNestedField(
                          "destination.options.errorLogTable",
                          e.target.value
                        )
                      }
                      placeholder="log_ftp.error_logs"
                      description="database.table_name"
                      isRequired
                    />
                  </div>
                </div>

                {/* File Tracking Database Configuration for SFTP Jobs */}
                {editedJob.dataSource.type === "sftp" && (
                  <div className="space-y-3">
                    <Divider />
                    <h5 className="text-sm font-semibold text-gray-700">
                      File Tracking Database Configuration
                    </h5>
                    <p className="text-xs text-gray-600">
                      Configure the database connection for tracking downloaded
                      files
                    </p>

                    {/* Database Connection Settings */}
                    <div className="grid grid-cols-2 gap-3">
                      <Input
                        label="Database Host"
                        value={
                          editedJob.destination.fileTracking?.database?.host ||
                          ""
                        }
                        onChange={(e) =>
                          updateNestedField(
                            "destination.fileTracking.database.host",
                            e.target.value
                          )
                        }
                        placeholder="localhost"
                        isRequired
                      />
                      <Input
                        label="Database Port"
                        type="number"
                        value={
                          editedJob.destination.fileTracking?.database?.port?.toString() ||
                          ""
                        }
                        onChange={(e) =>
                          updateNestedField(
                            "destination.fileTracking.database.port",
                            parseInt(e.target.value) || 3306
                          )
                        }
                        placeholder="3306"
                        isRequired
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <Input
                        label="Database Name"
                        value={
                          editedJob.destination.fileTracking?.database
                            ?.database || ""
                        }
                        onChange={(e) =>
                          updateNestedField(
                            "destination.fileTracking.database.database",
                            e.target.value
                          )
                        }
                        placeholder="monev2025"
                        isRequired
                      />
                      <Input
                        label="Table Name"
                        value={
                          editedJob.destination.fileTracking?.database?.table ||
                          ""
                        }
                        onChange={(e) =>
                          updateNestedField(
                            "destination.fileTracking.database.table",
                            e.target.value
                          )
                        }
                        placeholder="file_metadata"
                        isRequired
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <Input
                        label="Username"
                        value={
                          editedJob.destination.fileTracking?.database
                            ?.username || ""
                        }
                        onChange={(e) =>
                          updateNestedField(
                            "destination.fileTracking.database.username",
                            e.target.value
                          )
                        }
                        placeholder="root"
                        isRequired
                      />
                      <Input
                        label="Password"
                        type={showPasswords.fileTracking ? "text" : "password"}
                        value={
                          editedJob.destination.fileTracking?.database
                            ?.password || ""
                        }
                        onChange={(e) =>
                          updateNestedField(
                            "destination.fileTracking.database.password",
                            e.target.value
                          )
                        }
                        placeholder="database password"
                        endContent={
                          <button
                            type="button"
                            onClick={() =>
                              togglePasswordVisibility("fileTracking")
                            }
                            className="focus:outline-none"
                          >
                            {showPasswords.fileTracking ? (
                              <EyeOff className="w-4 h-4 text-gray-400" />
                            ) : (
                              <Eye className="w-4 h-4 text-gray-400" />
                            )}
                          </button>
                        }
                      />
                    </div>

                    <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                      <p className="text-xs text-blue-700">
                        💡 The metadata table above will be automatically synced
                        with this database configuration (database.table format)
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
