/**
 * Test script to verify sequence retry works with the job cleanup fix
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function testSequenceRetry() {
  console.log('🧪 Testing sequence retry with job cleanup fix...');
  
  try {
    // First, let's check if sequence seq-001 exists
    console.log('🔍 Checking for sequence seq-001...');
    
    try {
      const seqResponse = await axios.get(`${API_BASE}/admin/sequences`);
      const sequences = seqResponse.data;
      const seq001 = sequences.find(seq => seq.id === 'seq-001');
      
      if (!seq001) {
        console.log('❌ Sequence seq-001 not found, cannot test sequence retry');
        return;
      }
      
      console.log('✅ Found sequence seq-001:', seq001.name);
      console.log('📋 Jobs in sequence:', seq001.jobs);
      console.log('🔄 Max retries:', seq001.maxRetries);
      
      // Execute the sequence
      console.log('🚀 Executing sequence seq-001...');
      const execResponse = await axios.post(`${API_BASE}/admin/sequences/seq-001/execute`);
      console.log('📊 Sequence execution response:', execResponse.status, execResponse.data);
      
      // Wait for the sequence to complete (including retries)
      console.log('⏳ Waiting 10 seconds for sequence to complete with retries...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      // Check sequence execution status
      console.log('📊 Checking sequence execution status...');
      const statusResponse = await axios.get(`${API_BASE}/admin/sequences`);
      console.log('📋 Current sequences status:', statusResponse.data.map(s => ({
        id: s.id,
        name: s.name,
        enabled: s.enabled
      })));
      
    } catch (error) {
      console.log('📊 Sequence execution error:', error.response?.status, error.response?.data);
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testSequenceRetry().then(() => {
  console.log('🏁 Sequence retry test completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test crashed:', error);
  process.exit(1);
});
