import mysql from "mysql2/promise";
import { logger } from "./jobManager";

// Interface for MySQL column information
interface ColumnInfo {
  COLUMN_NAME: string;
}

// Interface for MySQL error information
interface MySQLError {
  message?: string;
  sqlState?: string;
  errno?: number;
}

// File tracking database configuration interface
export interface FileTrackingDbConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  table: string;
}

// File metadata interface for tracking
export interface FileMetadataRecord {
  folder: string;
  filename: string;
  size: number;
  modifyTime: Date;
  status?: string;
  jobId?: string;
  executionId?: string;
}

// Create a connection pool for file tracking database
const connectionPools = new Map<string, mysql.Pool>();

/**
 * Get or create a connection pool for the specified database configuration
 */
function getConnectionPool(config: FileTrackingDbConfig): mysql.Pool {
  const poolKey = `${config.host}:${config.port}/${config.database}`;

  if (!connectionPools.has(poolKey)) {
    const pool = mysql.createPool({
      host: config.host,
      port: config.port,
      user: config.username,
      password: config.password,
      database: config.database,
      waitForConnections: true,
      connectionLimit: 5,
      queueLimit: 0,
    });

    connectionPools.set(poolKey, pool);
    logger.info(
      `Created connection pool for file tracking database: ${poolKey}`
    );
  }

  return connectionPools.get(poolKey)!;
}

/**
 * Create the file metadata table if it doesn't exist and ensure required columns exist
 */
export async function ensureFileTrackingTable(
  config: FileTrackingDbConfig
): Promise<void> {
  logger.info("Ensuring file tracking table exists", {
    database: config.database,
    table: config.table,
    host: config.host,
    port: config.port,
  });

  const pool = getConnectionPool(config);
  const connection = await pool.getConnection();

  try {
    // First, create the table if it doesn't exist
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS ${config.table} (
        id INT PRIMARY KEY AUTO_INCREMENT,
        folder VARCHAR(255) NOT NULL,
        nmfile VARCHAR(255) NOT NULL,
        tg DATE NOT NULL COMMENT 'Date from file modification time',
        jam TIME NOT NULL COMMENT 'Time from file modification time',
        size BIGINT NOT NULL COMMENT 'File size in bytes',
        status VARCHAR(50) DEFAULT 'NEW' COMMENT 'Processing status',
        job_id VARCHAR(36) NULL COMMENT 'Job ID that processed this file',
        execution_id VARCHAR(100) NULL COMMENT 'Execution ID that processed this file',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_folder (folder),
        INDEX idx_nmfile (nmfile),
        INDEX idx_tg (tg),
        INDEX idx_status (status),
        INDEX idx_job_id (job_id),
        UNIQUE KEY unique_file_entry (folder, nmfile, tg, jam, size)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Check if required columns exist and add them if missing (for existing tables)
    const requiredColumns = [
      {
        name: "job_id",
        definition:
          "VARCHAR(36) NULL COMMENT 'Job ID that processed this file'",
      },
      {
        name: "execution_id",
        definition:
          "VARCHAR(100) NULL COMMENT 'Execution ID that processed this file'",
      },
    ];

    for (const column of requiredColumns) {
      try {
        // Check if column exists
        const [rows] = await connection.execute(
          `
          SELECT COLUMN_NAME
          FROM INFORMATION_SCHEMA.COLUMNS
          WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND COLUMN_NAME = ?
        `,
          [config.database, config.table, column.name]
        );

        if (Array.isArray(rows) && rows.length === 0) {
          // Column doesn't exist, add it
          await connection.execute(`
            ALTER TABLE ${config.table}
            ADD COLUMN ${column.name} ${column.definition}
          `);

          logger.info(
            `Added missing column '${column.name}' to table '${config.table}' in database '${config.database}'`
          );
        }
      } catch (columnError) {
        logger.warn(
          `Failed to check/add column '${column.name}' to table '${config.table}':`,
          columnError
        );
        // Continue with other columns even if one fails
      }
    }

    // Add index for job_id if it doesn't exist
    try {
      await connection.execute(`
        CREATE INDEX IF NOT EXISTS idx_job_id ON ${config.table} (job_id)
      `);
    } catch (indexError) {
      // Index might already exist, ignore error
      logger.debug(
        `Index creation for job_id skipped (likely already exists):`,
        indexError
      );
    }

    logger.info(
      `File tracking table '${config.table}' ensured in database '${config.database}'`
    );
  } catch (error) {
    logger.error(
      `Failed to create/update file tracking table '${config.table}':`,
      error
    );
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * Check if a file already exists in the tracking database
 */
export async function checkFileExists(
  config: FileTrackingDbConfig,
  folder: string,
  filename: string,
  tg: string,
  jam: string,
  size: number
): Promise<boolean> {
  const pool = getConnectionPool(config);
  const connection = await pool.getConnection();

  try {
    const [rows] = await connection.execute(
      `SELECT id FROM ${config.table} WHERE folder = ? AND nmfile = ? AND tg = ? AND jam = ? AND size = ?`,
      [folder, filename, tg, jam, size]
    );

    return Array.isArray(rows) && rows.length > 0;
  } catch (error) {
    logger.error("Failed to check file existence in tracking database:", error);
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * Save file metadata to the tracking database
 */
export async function saveFileMetadata(
  config: FileTrackingDbConfig,
  metadata: FileMetadataRecord
): Promise<void> {
  const pool = getConnectionPool(config);
  const connection = await pool.getConnection();

  try {
    // Extract date and time from modifyTime
    const tg = metadata.modifyTime.toISOString().split("T")[0]; // YYYY-MM-DD
    const jam = metadata.modifyTime.toTimeString().split(" ")[0]; // HH:MM:SS

    // Check if file already exists
    const exists = await checkFileExists(
      config,
      metadata.folder,
      metadata.filename,
      tg,
      jam,
      metadata.size
    );

    if (!exists) {
      try {
        await connection.execute(
          `INSERT INTO ${config.table} (folder, nmfile, tg, jam, size, status, job_id, execution_id)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            metadata.folder,
            metadata.filename,
            tg,
            jam,
            metadata.size,
            metadata.status || "NEW",
            metadata.jobId || null,
            metadata.executionId || null,
          ]
        );
      } catch (insertError: unknown) {
        const error = insertError as MySQLError;
        logger.error(
          "Failed to insert file metadata - checking table structure",
          {
            database: config.database,
            table: config.table,
            error: error?.message || String(insertError),
            sqlState: error?.sqlState,
            errno: error?.errno,
          }
        );

        // Check current table structure for debugging
        try {
          const [columns] = await connection.execute(
            `
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
            ORDER BY ORDINAL_POSITION
          `,
            [config.database, config.table]
          );

          logger.error("Current table columns:", {
            database: config.database,
            table: config.table,
            columns: Array.isArray(columns)
              ? (columns as ColumnInfo[]).map((col) => col.COLUMN_NAME)
              : [],
          });
        } catch (columnCheckError) {
          logger.error("Failed to check table columns:", columnCheckError);
        }

        throw insertError;
      }

      logger.info("File metadata saved to tracking database", {
        table: config.table,
        database: config.database,
        folder: metadata.folder,
        filename: metadata.filename,
        size: metadata.size,
        jobId: metadata.jobId,
        executionId: metadata.executionId,
      });
    } else {
      logger.debug("File metadata already exists in tracking database", {
        table: config.table,
        database: config.database,
        folder: metadata.folder,
        filename: metadata.filename,
      });
    }
  } catch (error) {
    logger.error("Failed to save file metadata to tracking database:", error);
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * Close all connection pools
 */
export async function closeFileTrackingPools(): Promise<void> {
  for (const [poolKey, pool] of connectionPools.entries()) {
    try {
      await pool.end();
      logger.info(`Closed file tracking connection pool: ${poolKey}`);
    } catch (error) {
      logger.error(
        `Error closing file tracking connection pool ${poolKey}:`,
        error
      );
    }
  }
  connectionPools.clear();
}

/**
 * Query files with specific status from file_metadata table
 */
export async function queryFilesByStatus(
  config: FileTrackingDbConfig,
  status: string = "NEW"
): Promise<
  Array<{
    id: number;
    folder: string;
    nmfile: string;
    tg: string;
    jam: string;
    size: number;
    status: string;
  }>
> {
  const pool = getConnectionPool(config);
  const connection = await pool.getConnection();

  try {
    const [rows] = await connection.execute(
      `SELECT id, folder, nmfile, tg, jam, size, status
       FROM ${config.table}
       WHERE status = ?
       ORDER BY created_at ASC`,
      [status]
    );

    return rows as Array<{
      id: number;
      folder: string;
      nmfile: string;
      tg: string;
      jam: string;
      size: number;
      status: string;
    }>;
  } catch (error) {
    logger.error("Failed to query files by status:", error);
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * Update file status in file_metadata table
 */
export async function updateFileStatus(
  config: FileTrackingDbConfig,
  fileId: number,
  newStatus: string,
  jobId?: string,
  executionId?: string
): Promise<void> {
  const pool = getConnectionPool(config);
  const connection = await pool.getConnection();

  try {
    await connection.execute(
      `UPDATE ${config.table}
       SET status = ?, job_id = ?, execution_id = ?
       WHERE id = ?`,
      [newStatus, jobId || null, executionId || null, fileId]
    );

    logger.info("File status updated in tracking database", {
      table: config.table,
      database: config.database,
      fileId,
      newStatus,
      jobId,
      executionId,
    });
  } catch (error) {
    logger.error("Failed to update file status:", error);
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * Health check for file tracking database connection
 */
export async function checkFileTrackingHealth(
  config: FileTrackingDbConfig
): Promise<boolean> {
  try {
    const pool = getConnectionPool(config);
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    return true;
  } catch (error) {
    logger.error("File tracking database health check failed:", error);
    return false;
  }
}
