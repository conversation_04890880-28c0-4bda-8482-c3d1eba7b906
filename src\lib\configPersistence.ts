import { executeQuery, executeUpdate } from "./database";
import { logger } from "./jobManager";

// Types for configuration
export interface AppConfig {
  key: string;
  value: string;
  description?: string;
}

export interface SystemSetting {
  category: string;
  key: string;
  value: string;
  type: "string" | "number" | "boolean" | "json";
  description?: string;
}

// Database types
interface DbAppConfig {
  id: number;
  config_key: string;
  config_value: string | null;
  description: string | null;
  created_at: Date;
  updated_at: Date;
}

interface DbSystemSetting {
  id: number;
  setting_category: string;
  setting_key: string;
  setting_value: string | null;
  setting_type: "string" | "number" | "boolean" | "json";
  description: string | null;
  created_at: Date;
  updated_at: Date;
}

// App Configuration CRUD operations
export async function saveAppConfig(
  key: string,
  value: string,
  description?: string
): Promise<void> {
  try {
    const query = `
      INSERT INTO app_config (config_key, config_value, description)
      VALUES (?, ?, ?)
      ON DUPLICATE KEY UPDATE
        config_value = VALUES(config_value),
        description = VALUES(description),
        updated_at = CURRENT_TIMESTAMP
    `;

    await executeUpdate(query, [key, value, description || null]);
    logger.info(`App config saved: ${key}`);
  } catch (error) {
    logger.error(`Failed to save app config ${key}:`, error);
    throw error;
  }
}

export async function loadAppConfig(key: string): Promise<string | null> {
  try {
    const query = "SELECT config_value FROM app_config WHERE config_key = ?";
    const rows = await executeQuery<DbAppConfig>(query, [key]);

    return rows.length > 0 ? rows[0].config_value : null;
  } catch (error) {
    logger.error(`Failed to load app config ${key}:`, error);
    throw error;
  }
}

export async function loadAllAppConfigs(): Promise<AppConfig[]> {
  try {
    const query = "SELECT * FROM app_config ORDER BY config_key";
    const rows = await executeQuery<DbAppConfig>(query);

    return rows.map((row) => ({
      key: row.config_key,
      value: row.config_value || "",
      description: row.description || undefined,
    }));
  } catch (error) {
    logger.error("Failed to load all app configs:", error);
    throw error;
  }
}

export async function deleteAppConfig(key: string): Promise<void> {
  try {
    const query = "DELETE FROM app_config WHERE config_key = ?";
    const result = await executeUpdate(query, [key]);

    if (result.affectedRows === 0) {
      throw new Error(`App config not found: ${key}`);
    }

    logger.info(`App config deleted: ${key}`);
  } catch (error) {
    logger.error(`Failed to delete app config ${key}:`, error);
    throw error;
  }
}

// System Settings CRUD operations
export async function saveSystemSetting(
  category: string,
  key: string,
  value: string,
  type: "string" | "number" | "boolean" | "json" = "string",
  description?: string
): Promise<void> {
  try {
    const query = `
      INSERT INTO system_settings (setting_category, setting_key, setting_value, setting_type, description)
      VALUES (?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        setting_value = VALUES(setting_value),
        setting_type = VALUES(setting_type),
        description = VALUES(description),
        updated_at = CURRENT_TIMESTAMP
    `;

    await executeUpdate(query, [
      category,
      key,
      value,
      type,
      description || null,
    ]);
    logger.info(`System setting saved: ${category}.${key}`);
  } catch (error) {
    logger.error(`Failed to save system setting ${category}.${key}:`, error);
    throw error;
  }
}

export async function loadSystemSetting(
  category: string,
  key: string
): Promise<string | null> {
  try {
    const query =
      "SELECT setting_value FROM system_settings WHERE setting_category = ? AND setting_key = ?";
    const rows = await executeQuery<DbSystemSetting>(query, [category, key]);

    return rows.length > 0 ? rows[0].setting_value : null;
  } catch (error) {
    logger.error(`Failed to load system setting ${category}.${key}:`, error);
    throw error;
  }
}

export async function loadSystemSettingsByCategory(
  category: string
): Promise<SystemSetting[]> {
  try {
    const query =
      "SELECT * FROM system_settings WHERE setting_category = ? ORDER BY setting_key";
    const rows = await executeQuery<DbSystemSetting>(query, [category]);

    return rows.map((row) => ({
      category: row.setting_category,
      key: row.setting_key,
      value: row.setting_value || "",
      type: row.setting_type,
      description: row.description || undefined,
    }));
  } catch (error) {
    logger.error(
      `Failed to load system settings for category ${category}:`,
      error
    );
    throw error;
  }
}

export async function loadAllSystemSettings(): Promise<SystemSetting[]> {
  try {
    const query =
      "SELECT * FROM system_settings ORDER BY setting_category, setting_key";
    const rows = await executeQuery<DbSystemSetting>(query);

    return rows.map((row) => ({
      category: row.setting_category,
      key: row.setting_key,
      value: row.setting_value || "",
      type: row.setting_type,
      description: row.description || undefined,
    }));
  } catch (error) {
    logger.error("Failed to load all system settings:", error);
    throw error;
  }
}

export async function deleteSystemSetting(
  category: string,
  key: string
): Promise<void> {
  try {
    const query =
      "DELETE FROM system_settings WHERE setting_category = ? AND setting_key = ?";
    const result = await executeUpdate(query, [category, key]);

    if (result.affectedRows === 0) {
      throw new Error(`System setting not found: ${category}.${key}`);
    }

    logger.info(`System setting deleted: ${category}.${key}`);
  } catch (error) {
    logger.error(`Failed to delete system setting ${category}.${key}:`, error);
    throw error;
  }
}

// Helper functions for typed settings
export async function getSystemSettingAsNumber(
  category: string,
  key: string,
  defaultValue: number
): Promise<number> {
  const value = await loadSystemSetting(category, key);
  if (value === null) return defaultValue;

  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
}

export async function getSystemSettingAsBoolean(
  category: string,
  key: string,
  defaultValue: boolean
): Promise<boolean> {
  const value = await loadSystemSetting(category, key);
  if (value === null) return defaultValue;

  return value.toLowerCase() === "true" || value === "1";
}

export async function getSystemSettingAsJSON<T>(
  category: string,
  key: string,
  defaultValue: T
): Promise<T> {
  const value = await loadSystemSetting(category, key);
  if (value === null) return defaultValue;

  try {
    return JSON.parse(value) as T;
  } catch (error) {
    logger.warn(`Failed to parse JSON setting ${category}.${key}:`, error);
    return defaultValue;
  }
}

// Initialize default settings
export async function initializeDefaultSettings(): Promise<void> {
  try {
    logger.info("Initializing default system settings...");

    // Application settings
    await saveSystemSetting(
      "app",
      "max_concurrent_jobs",
      "3",
      "number",
      "Maximum number of jobs that can run concurrently"
    );
    await saveSystemSetting(
      "app",
      "job_timeout_minutes",
      "60",
      "number",
      "Default timeout for job execution in minutes"
    );
    await saveSystemSetting(
      "app",
      "enable_auto_cleanup",
      "true",
      "boolean",
      "Enable automatic cleanup of old job data"
    );
    await saveSystemSetting(
      "app",
      "cleanup_days_to_keep",
      "30",
      "number",
      "Number of days to keep job execution history"
    );

    // Logging settings
    await saveSystemSetting(
      "logging",
      "log_level",
      "info",
      "string",
      "Default logging level (debug, info, warn, error)"
    );
    await saveSystemSetting(
      "logging",
      "max_log_files",
      "5",
      "number",
      "Maximum number of log files to keep"
    );
    await saveSystemSetting(
      "logging",
      "max_log_size_mb",
      "5",
      "number",
      "Maximum size of each log file in MB"
    );

    // Notification settings
    await saveSystemSetting(
      "notifications",
      "enable_email_alerts",
      "false",
      "boolean",
      "Enable email notifications for job failures"
    );
    await saveSystemSetting(
      "notifications",
      "email_recipients",
      "[]",
      "json",
      "List of email addresses for notifications"
    );
    await saveSystemSetting(
      "notifications",
      "slack_webhook_url",
      "",
      "string",
      "Slack webhook URL for notifications"
    );

    // Database settings
    await saveSystemSetting(
      "database",
      "connection_timeout",
      "60000",
      "number",
      "Database connection timeout in milliseconds"
    );
    await saveSystemSetting(
      "database",
      "max_connections",
      "10",
      "number",
      "Maximum number of database connections in pool"
    );
    await saveSystemSetting(
      "database",
      "retry_attempts",
      "3",
      "number",
      "Number of retry attempts for failed database operations"
    );

    // Scheduler settings
    await saveSystemSetting(
      "scheduler",
      "enable_scheduler",
      "true",
      "boolean",
      "Enable the cron job scheduler"
    );
    await saveSystemSetting(
      "scheduler",
      "timezone",
      "Asia/Jakarta",
      "string",
      "Timezone for cron job scheduling"
    );
    await saveSystemSetting(
      "scheduler",
      "max_missed_runs",
      "3",
      "number",
      "Maximum number of missed runs before disabling a job"
    );

    logger.info("Default system settings initialized successfully");
  } catch (error) {
    logger.error("Failed to initialize default settings:", error);
    throw error;
  }
}

// Export configuration keys for easy reference
export const CONFIG_KEYS = {
  APP: {
    MAX_CONCURRENT_JOBS: "max_concurrent_jobs",
    JOB_TIMEOUT_MINUTES: "job_timeout_minutes",
    ENABLE_AUTO_CLEANUP: "enable_auto_cleanup",
    CLEANUP_DAYS_TO_KEEP: "cleanup_days_to_keep",
  },
  LOGGING: {
    LOG_LEVEL: "log_level",
    MAX_LOG_FILES: "max_log_files",
    MAX_LOG_SIZE_MB: "max_log_size_mb",
  },
  NOTIFICATIONS: {
    ENABLE_EMAIL_ALERTS: "enable_email_alerts",
    EMAIL_RECIPIENTS: "email_recipients",
    SLACK_WEBHOOK_URL: "slack_webhook_url",
  },
  DATABASE: {
    CONNECTION_TIMEOUT: "connection_timeout",
    MAX_CONNECTIONS: "max_connections",
    RETRY_ATTEMPTS: "retry_attempts",
  },
  SCHEDULER: {
    ENABLE_SCHEDULER: "enable_scheduler",
    TIMEZONE: "timezone",
    MAX_MISSED_RUNS: "max_missed_runs",
  },
} as const;

export const SETTING_CATEGORIES = {
  APP: "app",
  LOGGING: "logging",
  NOTIFICATIONS: "notifications",
  DATABASE: "database",
  SCHEDULER: "scheduler",
  JOBS: "jobs",
} as const;
