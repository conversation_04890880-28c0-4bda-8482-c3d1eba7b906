import { NextRequest, NextResponse } from "next/server";
import {
  loadJobExecution,
  loadJobExecutions,
  cleanupOldJobData,
} from "@/lib/jobPersistence";
import { logger } from "@/lib/jobManager";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get("jobId");
    const executionId = searchParams.get("executionId");
    const limit = parseInt(searchParams.get("limit") || "50");

    if (executionId) {
      // Get specific execution
      const execution = await loadJobExecution(executionId);
      if (!execution) {
        return NextResponse.json(
          { error: "Job execution not found" },
          { status: 404 }
        );
      }
      return NextResponse.json({ execution });
    } else if (jobId) {
      // Get executions for specific job
      const executions = await loadJobExecutions(jobId, limit);
      return NextResponse.json({ executions, jobId, count: executions.length });
    } else {
      // Get all recent executions
      const executions = await loadJobExecutions(undefined, limit);
      return NextResponse.json({ executions, count: executions.length });
    }
  } catch (error) {
    logger.error("Failed to get job history:", error);
    return NextResponse.json(
      { error: "Failed to retrieve job history" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const daysToKeep = parseInt(searchParams.get("daysToKeep") || "30");

    if (daysToKeep < 1) {
      return NextResponse.json(
        { error: "daysToKeep must be at least 1" },
        { status: 400 }
      );
    }

    await cleanupOldJobData(daysToKeep);

    logger.info(`Cleaned up job data older than ${daysToKeep} days`);

    return NextResponse.json({
      success: true,
      message: `Successfully cleaned up job data older than ${daysToKeep} days`,
    });
  } catch (error) {
    logger.error("Failed to cleanup job history:", error);
    return NextResponse.json(
      { error: "Failed to cleanup job history" },
      { status: 500 }
    );
  }
}
