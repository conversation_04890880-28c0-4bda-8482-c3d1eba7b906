# ADK Processing Configuration Guide

## Overview

The ADK Processing job extracts XML files from compressed ADK archives (.s25, .s2504, .s2507) and processes the data into multiple database tables. This guide explains the proper configuration setup.

## Database Architecture

The ADK processing system uses **two separate databases**:

### 1. **ADK Data Storage** (`dbdipa25`)
- **Purpose**: Stores the actual processed XML data
- **Tables**: `d_akun`, `d_cttakun`, `d_item`, `d_kmpnen`, `d_kpa`, `d_kpjm`, `d_output`, `d_pdpt`, `d_pgj`, `d_polri`, `d_skmpnen`, `d_soutput`, `d_trktrm`, `d_valas`
- **Configuration**: `destination.database`

### 2. **File Tracking** (`monev2025`)
- **Purpose**: Tracks which files have been processed to avoid duplicates
- **Table**: `file_metadata`
- **Configuration**: `destination.fileTracking.database`

## Required Configuration

### Complete Job Definition Structure

```json
{
  "id": "6",
  "name": "ADK Processing",
  "description": "Extract and process XML files from compressed ADK archives",
  "schedule": "0 4 * * *",
  "enabled": false,
  "dataSource": {
    "type": "adk_processing",
    "adk_processing": {
      "sourceDirectory": "C:\\KUMPULAN_ADK\\ADK_2025_DIPA",
      "extractionPath": "C:\\KUMPULAN_ADK\\XML",
      "rarToolPath": "C:\\KUMPULAN_ADK\\TOOLS\\Rar.exe",
      "fileListDatabase": {
        "host": "localhost",
        "port": 3306,
        "database": "monev2025",
        "username": "root",
        "password": "",
        "table": "file_metadata"
      },
      "fileFilter": {
        "startsWith": ["d", "D"],
        "excludeExtensions": ["pdf"]
      },
      "processingOptions": {
        "deleteOldXmlFiles": true,
        "continueOnError": true,
        "batchSize": 10
      }
    }
  },
  "destination": {
    "type": "database",
    "database": {
      "type": "mysql",
      "host": "localhost",
      "port": 3306,
      "database": "dbdipa25",
      "username": "root",
      "password": "",
      "table": "d_item"
    },
    "fileTracking": {
      "enabled": true,
      "database": {
        "host": "localhost",
        "port": 3306,
        "username": "root",
        "password": "",
        "database": "monev2025",
        "table": "file_metadata"
      }
    }
  },
  "retryConfig": {
    "maxRetries": 3,
    "retryDelay": 600
  }
}
```

## Critical Configuration Points

### ✅ **Correct Setup**
- `destination.database.database`: `"dbdipa25"` (ADK data storage)
- `destination.fileTracking.database.database`: `"monev2025"` (file tracking)
- `destination.fileTracking.enabled`: `true`

### ❌ **Common Mistakes**
- Missing `destination.fileTracking` configuration
- Using same database for both data and file tracking
- Setting `destination.database.database` to `"monev2025"`

## Error Resolution

### "File tracking configuration is required for ADK processing"

This error occurs when `destination.fileTracking.database` is missing or incomplete.

**Solution**: Ensure the complete `fileTracking` configuration is present in the job definition.

### Database Connection Errors

- Verify both `dbdipa25` and `monev2025` databases exist
- Check database credentials and permissions
- Ensure required tables exist in both databases

## Migration File

The default configuration is defined in `migrate.mjs`. The migration automatically creates a properly configured ADK processing job with:

- ✅ Correct database separation
- ✅ File tracking enabled
- ✅ All required configurations
- ✅ Validation-ready setup

## UI Configuration

The ADK processing job can be edited through the Job Admin UI:

1. **Access**: Job Administration → Select ADK Processing job
2. **Edit**: Modify configuration through form interface
3. **Validation**: Real-time validation ensures correct setup
4. **Save**: Changes are persisted to database

## Workflow

1. **File Discovery**: Query `monev2025.file_metadata` for files with status 'NEW'
2. **Archive Extraction**: Extract XML files from compressed archives
3. **Data Processing**: Parse XML and distribute data to appropriate tables in `dbdipa25`
4. **Status Update**: Mark files as 'PROCESSED' in `monev2025.file_metadata`

## Troubleshooting

### Check File Metadata
```sql
SELECT COUNT(*) FROM monev2025.file_metadata WHERE status = 'NEW';
```

### Verify ADK Tables
```sql
SHOW TABLES FROM dbdipa25 LIKE 'd_%';
```

### Test Configuration
```bash
npx tsx test-migration-adk-config.ts
```

## Support

For configuration issues:
1. Check this guide for proper setup
2. Verify database connections
3. Use the UI validation features
4. Test with the provided scripts
