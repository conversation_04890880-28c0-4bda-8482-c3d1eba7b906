{"name": "sintesa-datapuller", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "migrate": "node migrate.mjs", "test-sftp": "npx tsx test-sftp-download.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@heroui/react": "^2.8.0", "@heroui/theme": "^2.4.18", "@types/ssh2": "^1.15.5", "axios": "^1.10.0", "cron-parser": "^5.3.0", "date-fns": "^4.1.0", "dotenv": "^16.6.1", "framer-motion": "^12.23.5", "lucide-react": "^0.525.0", "mysql2": "^3.14.2", "next": "15.4.1", "node-cron": "^4.2.1", "pdf-parse": "^1.1.1", "react": "19.1.0", "react-dom": "19.1.0", "ssh2": "^1.16.0", "ssh2-sftp-client": "^12.0.1", "winston": "^3.17.0", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/node-cron": "^3.0.11", "@types/pdf-parse": "^1.1.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/ssh2-sftp-client": "^9.0.5", "@types/winston": "^2.4.4", "@types/xml2js": "^0.4.14", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}, "optionalDependencies": {"@types/fs-extra": "^11.0.4", "fs-extra": "^11.2.0", "oracledb": "^6.7.0"}}