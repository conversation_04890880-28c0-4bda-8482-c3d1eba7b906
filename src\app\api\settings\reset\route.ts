import { NextResponse } from "next/server";
import { initializeDefaultSettings } from "@/lib/configPersistence";
import { initializeJobManager } from "@/lib/jobManager";
import { logger } from "@/lib/jobManager";
import { executeUpdate } from "@/lib/database";

export async function POST() {
  try {
    logger.info("Starting system reset to defaults...");

    // Clear existing settings and jobs
    await clearExistingData();

    // Reinitialize with defaults
    await initializeDefaultSettings();
    await initializeJobManager();

    logger.info("System reset to defaults completed successfully");

    return NextResponse.json({
      success: true,
      message: "System reset to defaults successfully",
    });
  } catch (error) {
    logger.error("Failed to reset system to defaults:", error);
    return NextResponse.json(
      { error: "Failed to reset system to defaults" },
      { status: 500 }
    );
  }
}

async function clearExistingData(): Promise<void> {
  try {
    logger.info("Clearing existing system data...");

    // Clear job-related data
    await executeUpdate("DELETE FROM job_execution_logs WHERE execution_id IN (SELECT id FROM job_executions)");
    await executeUpdate("DELETE FROM job_executions");
    await executeUpdate("DELETE FROM job_schedule_history");
    await executeUpdate("DELETE FROM job_definitions");

    // Clear system settings
    await executeUpdate("DELETE FROM system_settings");

    // Clear app configs
    await executeUpdate("DELETE FROM app_config");

    logger.info("Existing system data cleared successfully");
  } catch (error) {
    logger.error("Failed to clear existing data:", error);
    throw error;
  }
}
