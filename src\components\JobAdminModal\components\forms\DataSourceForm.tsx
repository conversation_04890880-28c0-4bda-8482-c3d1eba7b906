import React, { useState } from "react";
import {
  Input,
  Textarea,
  Select,
  SelectItem,
  Divider,
  Tabs,
  Tab,
  Button,
} from "@heroui/react";
import { Eye, EyeOff, Database } from "lucide-react";
import { JobDefinition } from "@/lib/jobManager";
import { AdkValidationDisplay } from "./AdkValidationDisplay";
import { AdkQuickHelp } from "./AdkHelpSystem";

interface DataSourceFormProps {
  editedJob: JobDefinition;
  updateNestedField: (path: string, value: unknown) => void;
  setShowVisualBuilder?: (show: boolean) => void;
  setShowSchemaBrowser?: (show: boolean) => void;
}

export const DataSourceForm: React.FC<DataSourceFormProps> = ({
  editedJob,
  updateNestedField,
  setShowVisualBuilder,
  setShowSchemaBrowser,
}) => {
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>(
    {}
  );

  const togglePasswordVisibility = (field: string) => {
    setShowPasswords((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const renderOracleForm = () => (
    <div className="form-section animate-fade-in">
      <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
        Oracle Database Connection
      </h3>
      <div className="form-group">
        <div className="form-row">
          <Input
            label="Host"
            value={editedJob.dataSource.oracle?.host || ""}
            onChange={(e) =>
              updateNestedField("dataSource.oracle.host", e.target.value)
            }
            placeholder="localhost"
            classNames={{
              input: "text-sm",
              label: "text-sm font-medium text-gray-700",
            }}
          />
          <Input
            label="Port"
            type="number"
            value={editedJob.dataSource.oracle?.port?.toString() || ""}
            onChange={(e) =>
              updateNestedField(
                "dataSource.oracle.port",
                parseInt(e.target.value) || 1521
              )
            }
            placeholder="1521"
            classNames={{
              input: "text-sm",
              label: "text-sm font-medium text-gray-700",
            }}
          />
        </div>
        <Input
          label="Service Name"
          value={editedJob.dataSource.oracle?.serviceName || ""}
          onChange={(e) =>
            updateNestedField("dataSource.oracle.serviceName", e.target.value)
          }
          placeholder="XE"
          classNames={{
            input: "text-sm",
            label: "text-sm font-medium text-gray-700",
          }}
        />
        <div className="form-row">
          <Input
            label="Username"
            value={editedJob.dataSource.oracle?.username || ""}
            onChange={(e) =>
              updateNestedField("dataSource.oracle.username", e.target.value)
            }
            classNames={{
              input: "text-sm",
              label: "text-sm font-medium text-gray-700",
            }}
          />
          <Input
            label="Password"
            type={showPasswords.oracle ? "text" : "password"}
            value={editedJob.dataSource.oracle?.password || ""}
            onChange={(e) =>
              updateNestedField("dataSource.oracle.password", e.target.value)
            }
            classNames={{
              input: "text-sm",
              label: "text-sm font-medium text-gray-700",
            }}
            endContent={
              <button
                type="button"
                onClick={() => togglePasswordVisibility("oracle")}
                className="focus:outline-none hover:text-gray-600 transition-colors"
              >
                {showPasswords.oracle ? (
                  <EyeOff className="w-4 h-4 text-gray-400" />
                ) : (
                  <Eye className="w-4 h-4 text-gray-400" />
                )}
              </button>
            }
          />
        </div>
      </div>
      <Input
        label="Schema (Optional)"
        value={editedJob.dataSource.oracle?.schema || ""}
        onChange={(e) =>
          updateNestedField("dataSource.oracle.schema", e.target.value)
        }
      />
      <Textarea
        label="SQL Query"
        value={editedJob.dataSource.oracle?.query || ""}
        onChange={(e) =>
          updateNestedField("dataSource.oracle.query", e.target.value)
        }
        placeholder="SELECT * FROM your_table WHERE condition"
        minRows={3}
      />
    </div>
  );

  const renderMySQLForm = () => (
    <div className="space-y-3">
      <div className="grid grid-cols-2 gap-3">
        <Input
          label="Host"
          value={editedJob.dataSource.mysql?.host || ""}
          onChange={(e) =>
            updateNestedField("dataSource.mysql.host", e.target.value)
          }
          placeholder="localhost"
        />
        <Input
          label="Port"
          type="number"
          value={editedJob.dataSource.mysql?.port?.toString() || ""}
          onChange={(e) =>
            updateNestedField(
              "dataSource.mysql.port",
              parseInt(e.target.value) || 3306
            )
          }
          placeholder="3306"
        />
      </div>
      <Input
        label="Database Name"
        value={editedJob.dataSource.mysql?.database || ""}
        onChange={(e) =>
          updateNestedField("dataSource.mysql.database", e.target.value)
        }
        placeholder="your_database"
      />
      <div className="grid grid-cols-2 gap-3">
        <Input
          label="Username"
          value={editedJob.dataSource.mysql?.username || ""}
          onChange={(e) =>
            updateNestedField("dataSource.mysql.username", e.target.value)
          }
        />
        <Input
          label="Password"
          type={showPasswords.mysql ? "text" : "password"}
          value={editedJob.dataSource.mysql?.password || ""}
          onChange={(e) =>
            updateNestedField("dataSource.mysql.password", e.target.value)
          }
          endContent={
            <button
              type="button"
              onClick={() => togglePasswordVisibility("mysql")}
              className="focus:outline-none"
            >
              {showPasswords.mysql ? (
                <EyeOff className="w-4 h-4 text-gray-400" />
              ) : (
                <Eye className="w-4 h-4 text-gray-400" />
              )}
            </button>
          }
        />
      </div>
      <div className="grid grid-cols-3 gap-3">
        <Input
          label="Connection Limit (Optional)"
          type="number"
          value={editedJob.dataSource.mysql?.connectionLimit?.toString() || ""}
          onChange={(e) =>
            updateNestedField(
              "dataSource.mysql.connectionLimit",
              parseInt(e.target.value) || 10
            )
          }
          placeholder="10"
        />
        <Input
          label="Acquire Timeout (ms)"
          type="number"
          value={editedJob.dataSource.mysql?.acquireTimeout?.toString() || ""}
          onChange={(e) =>
            updateNestedField(
              "dataSource.mysql.acquireTimeout",
              parseInt(e.target.value) || 60000
            )
          }
          placeholder="60000"
        />
        <Input
          label="Query Timeout (ms)"
          type="number"
          value={editedJob.dataSource.mysql?.timeout?.toString() || ""}
          onChange={(e) =>
            updateNestedField(
              "dataSource.mysql.timeout",
              parseInt(e.target.value) || 60000
            )
          }
          placeholder="60000"
        />
      </div>
      <Textarea
        label="SQL Query"
        value={editedJob.dataSource.mysql?.query || ""}
        onChange={(e) =>
          updateNestedField("dataSource.mysql.query", e.target.value)
        }
        placeholder="SELECT * FROM your_table WHERE condition"
        minRows={3}
      />
    </div>
  );

  const renderSftpForm = () => (
    <div className="space-y-3">
      <div className="text-sm text-green-600 bg-green-50 p-3 rounded">
        <strong>SFTP Server Configuration</strong>
        <br />
        Configure connection to SFTP server for file downloads and transfers.
      </div>
      <div className="grid grid-cols-2 gap-3">
        <Input
          label="Host"
          value={editedJob.dataSource.sftp?.host || ""}
          onChange={(e) =>
            updateNestedField("dataSource.sftp.host", e.target.value)
          }
          placeholder="sftp.example.com"
          isRequired
        />
        <Input
          label="Port"
          type="number"
          value={editedJob.dataSource.sftp?.port?.toString() || ""}
          onChange={(e) =>
            updateNestedField(
              "dataSource.sftp.port",
              parseInt(e.target.value) || 22
            )
          }
          placeholder="22"
          isRequired
        />
      </div>
      <div className="grid grid-cols-2 gap-3">
        <Input
          label="Username"
          value={editedJob.dataSource.sftp?.username || ""}
          onChange={(e) =>
            updateNestedField("dataSource.sftp.username", e.target.value)
          }
          placeholder="sftp_user"
          isRequired
        />
        <Input
          label="Password"
          type={showPasswords.sftp ? "text" : "password"}
          value={editedJob.dataSource.sftp?.password || ""}
          onChange={(e) =>
            updateNestedField("dataSource.sftp.password", e.target.value)
          }
          placeholder="Enter password or leave empty if using private key"
          endContent={
            <button
              type="button"
              onClick={() => togglePasswordVisibility("sftp")}
              className="focus:outline-none"
            >
              {showPasswords.sftp ? (
                <EyeOff className="w-4 h-4 text-gray-400" />
              ) : (
                <Eye className="w-4 h-4 text-gray-400" />
              )}
            </button>
          }
        />
      </div>
      <Input
        label="Private Key Path (Optional)"
        value={editedJob.dataSource.sftp?.privateKey || ""}
        onChange={(e) =>
          updateNestedField("dataSource.sftp.privateKey", e.target.value)
        }
        placeholder="/path/to/private/key or key content"
        description="Alternative to password authentication"
      />
      <Input
        label="Remote Path"
        value={editedJob.dataSource.sftp?.remotePath || ""}
        onChange={(e) =>
          updateNestedField("dataSource.sftp.remotePath", e.target.value)
        }
        placeholder="/exports/data/"
        description="Path to files on SFTP server"
        isRequired
      />
      <Input
        label="File Pattern (Optional)"
        value={editedJob.dataSource.sftp?.filePattern || ""}
        onChange={(e) =>
          updateNestedField("dataSource.sftp.filePattern", e.target.value)
        }
        placeholder="*.csv, *.pdf, data_*.txt"
        description="Pattern to match specific files (e.g., *.csv)"
      />
    </div>
  );

  const renderPdfDipaForm = () => (
    <div className="space-y-4">
      <div className="text-sm text-purple-600 bg-purple-50 p-3 rounded">
        <strong>PDF DIPA Extraction</strong>
        <br />
        Extract budget information from Indonesian government DIPA PDF files.
      </div>
      <Input
        label="Source Directory"
        value={editedJob.dataSource.pdf_dipa?.sourceDirectory || ""}
        onChange={(e) =>
          updateNestedField(
            "dataSource.pdf_dipa.sourceDirectory",
            e.target.value
          )
        }
        placeholder="Default: ADK_BASE_PATH env var or C:\\KUMPULAN_ADK\\ADK_2024_DIPA"
        description="Directory containing PDF files to process"
        isRequired
      />
      <Input
        label="File Status Filter"
        value={editedJob.dataSource.pdf_dipa?.fileStatusFilter || ""}
        onChange={(e) =>
          updateNestedField(
            "dataSource.pdf_dipa.fileStatusFilter",
            e.target.value
          )
        }
        placeholder="NEW"
        description="Status to filter files by (defaults to 'NEW' for incremental processing)"
      />
      <Input
        label="Error Log Table (Optional)"
        value={editedJob.dataSource.pdf_dipa?.errorLogTable || ""}
        onChange={(e) =>
          updateNestedField("dataSource.pdf_dipa.errorLogTable", e.target.value)
        }
        placeholder="log_ftp.error_logs"
        description="Table for error logging (optional)"
      />

      <Divider />

      <div className="space-y-3">
        <h5 className="text-sm font-semibold">File Metadata Database</h5>
        <div className="grid grid-cols-2 gap-3">
          <Input
            label="Host"
            value={
              editedJob.dataSource.pdf_dipa?.fileMetadataDatabase?.host || ""
            }
            onChange={(e) =>
              updateNestedField(
                "dataSource.pdf_dipa.fileMetadataDatabase.host",
                e.target.value
              )
            }
            placeholder="localhost"
            isRequired
          />
          <Input
            label="Port"
            type="number"
            value={
              editedJob.dataSource.pdf_dipa?.fileMetadataDatabase?.port?.toString() ||
              ""
            }
            onChange={(e) =>
              updateNestedField(
                "dataSource.pdf_dipa.fileMetadataDatabase.port",
                parseInt(e.target.value) || 3306
              )
            }
            placeholder="3306"
            isRequired
          />
        </div>
        <Input
          label="Database Name"
          value={
            editedJob.dataSource.pdf_dipa?.fileMetadataDatabase?.database || ""
          }
          onChange={(e) =>
            updateNestedField(
              "dataSource.pdf_dipa.fileMetadataDatabase.database",
              e.target.value
            )
          }
          placeholder="monev2024"
          isRequired
        />
        <div className="grid grid-cols-2 gap-3">
          <Input
            label="Username"
            value={
              editedJob.dataSource.pdf_dipa?.fileMetadataDatabase?.username ||
              ""
            }
            onChange={(e) =>
              updateNestedField(
                "dataSource.pdf_dipa.fileMetadataDatabase.username",
                e.target.value
              )
            }
            isRequired
          />
          <Input
            label="Password"
            type={showPasswords.pdf_dipa ? "text" : "password"}
            value={
              editedJob.dataSource.pdf_dipa?.fileMetadataDatabase?.password ||
              ""
            }
            onChange={(e) =>
              updateNestedField(
                "dataSource.pdf_dipa.fileMetadataDatabase.password",
                e.target.value
              )
            }
            endContent={
              <button
                type="button"
                onClick={() => togglePasswordVisibility("pdf_dipa")}
                className="focus:outline-none"
              >
                {showPasswords.pdf_dipa ? (
                  <EyeOff className="w-4 h-4 text-gray-400" />
                ) : (
                  <Eye className="w-4 h-4 text-gray-400" />
                )}
              </button>
            }
            isRequired
          />
        </div>
        <Input
          label="Metadata Table"
          value={
            editedJob.dataSource.pdf_dipa?.fileMetadataDatabase?.table || ""
          }
          onChange={(e) =>
            updateNestedField(
              "dataSource.pdf_dipa.fileMetadataDatabase.table",
              e.target.value
            )
          }
          placeholder="file_metadata"
          description="Table that tracks file metadata"
          isRequired
        />
      </div>
    </div>
  );

  const renderAdkProcessingForm = () => (
    <div className="space-y-6">
      {/* Enhanced Header with Multi-Table Information */}
      <div className="text-sm text-green-600 bg-green-50 p-4 rounded-lg border border-green-200">
        <div className="flex items-start gap-3">
          <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-0.5">
            <Database className="w-3 h-3 text-green-600" />
          </div>
          <div>
            <strong className="text-green-700">
              ADK Multi-Table Processing
            </strong>
            <p className="mt-1 text-green-600">
              Extract and process XML files from compressed ADK archives (.s25,
              .s2504, .s2507) and distribute data across{" "}
              <strong>14 target tables</strong>: d_akun, d_cttakun, d_item,
              d_kmpnen, d_kpa, d_kpjm, d_output, d_pdpt, d_pgj, d_polri,
              d_skmpnen, d_soutput, d_trktrm, d_valas.
            </p>
            <p className="mt-2 text-xs text-green-500">
              💡 Each archive contains ~27 XML files. Data is automatically
              routed to appropriate tables.
            </p>
          </div>
        </div>
      </div>

      {/* File Paths Configuration */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <h5 className="text-sm font-semibold text-gray-700">
            File Paths Configuration
          </h5>
          <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
            Required
          </div>
          <AdkQuickHelp topic="filePaths" />
        </div>

        <div className="grid gap-4">
          <Input
            label="Source Directory"
            value={editedJob.dataSource.adk_processing?.sourceDirectory || ""}
            onChange={(e) =>
              updateNestedField(
                "dataSource.adk_processing.sourceDirectory",
                e.target.value
              )
            }
            placeholder="C:\\KUMPULAN_ADK\\ADK_2025_DIPA"
            description="Base directory containing compressed ADK files (.s25, .s2504, .s2507)"
            isRequired
            startContent={<div className="text-gray-400">📁</div>}
          />

          <Input
            label="Extraction Path"
            value={editedJob.dataSource.adk_processing?.extractionPath || ""}
            onChange={(e) =>
              updateNestedField(
                "dataSource.adk_processing.extractionPath",
                e.target.value
              )
            }
            placeholder="C:\\KUMPULAN_ADK\\XML"
            description="Temporary directory for XML extraction (will be cleaned automatically)"
            isRequired
            startContent={<div className="text-gray-400">📂</div>}
          />

          <Input
            label="RAR Tool Path"
            value={editedJob.dataSource.adk_processing?.rarToolPath || ""}
            onChange={(e) =>
              updateNestedField(
                "dataSource.adk_processing.rarToolPath",
                e.target.value
              )
            }
            placeholder="C:\\KUMPULAN_ADK\\TOOLS\\Rar.exe"
            description="Path to RAR extraction executable (Rar.exe)"
            isRequired
            startContent={<div className="text-gray-400">⚙️</div>}
          />
        </div>
      </div>

      <Divider />

      {/* File List Database */}
      <div className="space-y-3">
        <h5 className="text-sm font-semibold">File List Database</h5>
        <div className="grid grid-cols-2 gap-3">
          <Input
            label="Host"
            value={
              editedJob.dataSource.adk_processing?.fileListDatabase?.host || ""
            }
            onChange={(e) =>
              updateNestedField(
                "dataSource.adk_processing.fileListDatabase.host",
                e.target.value
              )
            }
            placeholder="localhost"
            isRequired
          />
          <Input
            label="Port"
            type="number"
            value={
              editedJob.dataSource.adk_processing?.fileListDatabase?.port?.toString() ||
              ""
            }
            onChange={(e) =>
              updateNestedField(
                "dataSource.adk_processing.fileListDatabase.port",
                parseInt(e.target.value) || 3306
              )
            }
            placeholder="3306"
            isRequired
          />
        </div>
        <Input
          label="Database Name"
          value={
            editedJob.dataSource.adk_processing?.fileListDatabase?.database ||
            ""
          }
          onChange={(e) =>
            updateNestedField(
              "dataSource.adk_processing.fileListDatabase.database",
              e.target.value
            )
          }
          placeholder="monev2024"
          isRequired
        />
        <div className="grid grid-cols-2 gap-3">
          <Input
            label="Username"
            value={
              editedJob.dataSource.adk_processing?.fileListDatabase?.username ||
              ""
            }
            onChange={(e) =>
              updateNestedField(
                "dataSource.adk_processing.fileListDatabase.username",
                e.target.value
              )
            }
            isRequired
          />
          <Input
            label="Password"
            type={showPasswords.adk_processing_file_list ? "text" : "password"}
            value={
              editedJob.dataSource.adk_processing?.fileListDatabase?.password ||
              ""
            }
            onChange={(e) =>
              updateNestedField(
                "dataSource.adk_processing.fileListDatabase.password",
                e.target.value
              )
            }
            endContent={
              <button
                type="button"
                onClick={() =>
                  togglePasswordVisibility("adk_processing_file_list")
                }
                className="focus:outline-none"
              >
                {showPasswords.adk_processing_file_list ? (
                  <EyeOff className="w-4 h-4 text-gray-400" />
                ) : (
                  <Eye className="w-4 h-4 text-gray-400" />
                )}
              </button>
            }
            isRequired
          />
        </div>
        <Input
          label="File List Table"
          value={
            editedJob.dataSource.adk_processing?.fileListDatabase?.table || ""
          }
          onChange={(e) =>
            updateNestedField(
              "dataSource.adk_processing.fileListDatabase.table",
              e.target.value
            )
          }
          placeholder="ftp_baru_2024"
          description="Table containing file list to process"
          isRequired
        />
      </div>

      <Divider />

      {/* Enhanced File Filtering */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <h5 className="text-sm font-semibold text-gray-700">
            File Filtering Options
          </h5>
          <div className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
            Smart Filtering
          </div>
          <AdkQuickHelp topic="filtering" />
        </div>

        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
          <p className="text-xs text-blue-600">
            🎯 <strong>Smart Filtering:</strong> Only process files that match
            your criteria. Supports .s25, .s2504, .s2507 extensions
            automatically.
          </p>
        </div>

        <div className="grid gap-4">
          <Input
            label="File Name Prefixes"
            value={
              editedJob.dataSource.adk_processing?.fileFilter?.startsWith?.join(
                ", "
              ) || ""
            }
            onChange={(e) =>
              updateNestedField(
                "dataSource.adk_processing.fileFilter.startsWith",
                e.target.value
                  .split(",")
                  .map((s) => s.trim())
                  .filter((s) => s)
              )
            }
            placeholder="d, D"
            description="Comma-separated prefixes (e.g., 'd, D' for files starting with 'd' or 'D')"
            isRequired
            startContent={<div className="text-gray-400">🔍</div>}
          />

          <Input
            label="Exclude Extensions"
            value={
              editedJob.dataSource.adk_processing?.fileFilter?.excludeExtensions?.join(
                ", "
              ) || ""
            }
            onChange={(e) =>
              updateNestedField(
                "dataSource.adk_processing.fileFilter.excludeExtensions",
                e.target.value
                  .split(",")
                  .map((s) => s.trim())
                  .filter((s) => s)
              )
            }
            placeholder="pdf, txt"
            description="Comma-separated extensions to skip (e.g., 'pdf, txt')"
            startContent={<div className="text-gray-400">🚫</div>}
          />
        </div>
      </div>

      <Divider />

      {/* Enhanced Processing Options */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <h5 className="text-sm font-semibold text-gray-700">
            Processing Options
          </h5>
          <div className="text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded">
            Advanced
          </div>
          <AdkQuickHelp topic="processing" />
        </div>

        <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
          <p className="text-xs text-purple-600">
            ⚙️ <strong>Multi-Table Processing:</strong> Each archive is
            extracted, processed across 14 tables, then cleaned up
            automatically. Configure behavior below.
          </p>
        </div>

        {/* Processing Behavior Checkboxes */}
        <div className="grid grid-cols-1 gap-4">
          <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
            <input
              type="checkbox"
              id="deleteOldXmlFiles"
              checked={
                editedJob.dataSource.adk_processing?.processingOptions
                  ?.deleteOldXmlFiles !== false
              }
              onChange={(e) =>
                updateNestedField(
                  "dataSource.adk_processing.processingOptions.deleteOldXmlFiles",
                  e.target.checked
                )
              }
              className="rounded mt-0.5"
            />
            <div>
              <label
                htmlFor="deleteOldXmlFiles"
                className="text-sm font-medium text-gray-700"
              >
                🗑️ Clean extraction directory before processing
              </label>
              <p className="text-xs text-gray-500 mt-1">
                Recommended: Removes old XML files to prevent conflicts
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
            <input
              type="checkbox"
              id="continueOnError"
              checked={
                editedJob.dataSource.adk_processing?.processingOptions
                  ?.continueOnError || false
              }
              onChange={(e) =>
                updateNestedField(
                  "dataSource.adk_processing.processingOptions.continueOnError",
                  e.target.checked
                )
              }
              className="rounded mt-0.5"
            />
            <div>
              <label
                htmlFor="continueOnError"
                className="text-sm font-medium text-gray-700"
              >
                🔄 Continue processing on individual file errors
              </label>
              <p className="text-xs text-gray-500 mt-1">
                Recommended: Process remaining files even if some fail
              </p>
            </div>
          </div>
        </div>

        {/* Batch Size Configuration */}
        <div className="space-y-2">
          <Input
            label="Batch Size"
            type="number"
            value={
              editedJob.dataSource.adk_processing?.processingOptions?.batchSize?.toString() ||
              ""
            }
            onChange={(e) =>
              updateNestedField(
                "dataSource.adk_processing.processingOptions.batchSize",
                e.target.value ? parseInt(e.target.value) : undefined
              )
            }
            placeholder="10"
            description="Number of files to process in each batch (leave empty for no batching)"
            startContent={<div className="text-gray-400">📦</div>}
          />
        </div>
      </div>

      <Divider />

      {/* ADK Configuration Validation */}
      <AdkValidationDisplay job={editedJob} />
    </div>
  );

  return (
    <div className="form-section animate-fade-in">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center">
          <Database className="w-4 h-4 text-blue-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Data Source</h3>
          <p className="text-sm text-gray-600">
            Configure where to extract data from
          </p>
        </div>
      </div>

      <div className="form-group">
        <Select
          label="Data Source Type"
          selectedKeys={[editedJob.dataSource.type]}
          onSelectionChange={(keys) => {
            const type = Array.from(keys)[0] as
              | "oracle"
              | "sftp"
              | "mysql"
              | "database_admin"
              | "pdf_dipa"
              | "adk_processing";
            updateNestedField("dataSource.type", type);
          }}
          classNames={{
            label: "text-sm font-medium text-gray-700",
            trigger: "border-gray-300 hover:border-gray-400",
          }}
        >
          <SelectItem key="oracle">Oracle Database</SelectItem>
          <SelectItem key="mysql">MySQL Database</SelectItem>
          <SelectItem key="database_admin">Database Administration</SelectItem>
          <SelectItem key="sftp">SFTP Server</SelectItem>
          <SelectItem key="pdf_dipa">PDF DIPA Extraction</SelectItem>
          <SelectItem key="adk_processing">ADK Processing</SelectItem>
        </Select>

        {editedJob.dataSource.type === "oracle" && renderOracleForm()}
        {editedJob.dataSource.type === "mysql" && renderMySQLForm()}
        {editedJob.dataSource.type === "sftp" && renderSftpForm()}
        {editedJob.dataSource.type === "pdf_dipa" && renderPdfDipaForm()}
        {editedJob.dataSource.type === "adk_processing" &&
          renderAdkProcessingForm()}

        {editedJob.dataSource.type === "database_admin" && (
          <div className="space-y-4">
            <div className="text-sm text-blue-600 bg-blue-50 p-3 rounded">
              <strong>Database Administration Mode</strong>
              <br />
              This mode allows you to perform DDL operations, multi-step
              database tasks, and use the visual query builder for complex
              operations.
            </div>

            <div className="grid grid-cols-2 gap-3">
              <Input
                label="Host"
                value={editedJob.dataSource.database_admin?.host || ""}
                onChange={(e) =>
                  updateNestedField(
                    "dataSource.database_admin.host",
                    e.target.value
                  )
                }
                placeholder="localhost"
              />
              <Input
                label="Port"
                type="number"
                value={
                  editedJob.dataSource.database_admin?.port?.toString() || ""
                }
                onChange={(e) =>
                  updateNestedField(
                    "dataSource.database_admin.port",
                    parseInt(e.target.value) || 3306
                  )
                }
                placeholder="3306"
              />
            </div>

            <Input
              label="Database Name"
              value={editedJob.dataSource.database_admin?.database || ""}
              onChange={(e) =>
                updateNestedField(
                  "dataSource.database_admin.database",
                  e.target.value
                )
              }
              placeholder="your_database"
            />

            <div className="grid grid-cols-2 gap-3">
              <Input
                label="Username"
                value={editedJob.dataSource.database_admin?.username || ""}
                onChange={(e) =>
                  updateNestedField(
                    "dataSource.database_admin.username",
                    e.target.value
                  )
                }
              />
              <Input
                label="Password"
                type={showPasswords.database_admin ? "text" : "password"}
                value={editedJob.dataSource.database_admin?.password || ""}
                onChange={(e) =>
                  updateNestedField(
                    "dataSource.database_admin.password",
                    e.target.value
                  )
                }
                endContent={
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility("database_admin")}
                    className="focus:outline-none"
                  >
                    {showPasswords.database_admin ? (
                      <EyeOff className="w-4 h-4 text-gray-400" />
                    ) : (
                      <Eye className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                }
              />
            </div>

            <div className="grid grid-cols-3 gap-3">
              <Input
                label="Connection Limit"
                type="number"
                value={
                  editedJob.dataSource.database_admin?.connectionLimit?.toString() ||
                  ""
                }
                onChange={(e) =>
                  updateNestedField(
                    "dataSource.database_admin.connectionLimit",
                    parseInt(e.target.value) || 10
                  )
                }
                placeholder="10"
              />
              <Input
                label="Acquire Timeout (ms)"
                type="number"
                value={
                  editedJob.dataSource.database_admin?.acquireTimeout?.toString() ||
                  ""
                }
                onChange={(e) =>
                  updateNestedField(
                    "dataSource.database_admin.acquireTimeout",
                    parseInt(e.target.value) || 60000
                  )
                }
                placeholder="60000"
              />
              <Input
                label="Query Timeout (ms)"
                type="number"
                value={
                  editedJob.dataSource.database_admin?.timeout?.toString() || ""
                }
                onChange={(e) =>
                  updateNestedField(
                    "dataSource.database_admin.timeout",
                    parseInt(e.target.value) || 120000
                  )
                }
                placeholder="120000"
              />
            </div>

            <Divider />

            <div className="space-y-4">
              <h4 className="text-md font-semibold">Database Operations</h4>

              <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                <label className="text-sm font-medium text-amber-800 mb-2 block">
                  Operation Mode
                </label>
                <Select
                  selectedKeys={[
                    editedJob.dataSource.database_admin?.operationMode ||
                      "table_management",
                  ]}
                  onSelectionChange={(keys) => {
                    const mode = Array.from(keys)[0] as string;
                    updateNestedField(
                      "dataSource.database_admin.operationMode",
                      mode
                    );
                  }}
                  className="w-full"
                >
                  <SelectItem key="table_management">
                    🔧 Table Management - DDL operations (CREATE/DROP/ALTER)
                  </SelectItem>
                  <SelectItem key="data_extraction">
                    📊 Data Extraction - SELECT queries with export
                  </SelectItem>
                  <SelectItem key="workflow">
                    ⚙️ Complex Workflow - Multi-step operations
                  </SelectItem>
                </Select>
                <p className="text-xs text-amber-700 mt-2">
                  {editedJob.dataSource.database_admin?.operationMode ===
                    "table_management" &&
                    "Perfect for your DROP/CREATE table operations. Results logged only."}
                  {editedJob.dataSource.database_admin?.operationMode ===
                    "data_extraction" &&
                    "Extract data with SELECT queries. Results exported to destinations."}
                  {editedJob.dataSource.database_admin?.operationMode ===
                    "workflow" &&
                    "Chain multiple operations with dependencies and error handling."}
                </p>
              </div>

              <p className="text-sm text-gray-600">
                Choose how you want to define your operations:
              </p>

              <Tabs defaultSelectedKey="raw_sql" className="w-full">
                <Tab key="raw_sql" title="Raw SQL">
                  <div className="mt-4">
                    <Textarea
                      label="SQL Commands"
                      value={editedJob.dataSource.database_admin?.rawSql || ""}
                      onChange={(e) =>
                        updateNestedField(
                          "dataSource.database_admin.rawSql",
                          e.target.value
                        )
                      }
                      placeholder="-- Enter your SQL commands here
DROP TABLE IF EXISTS your_table;
CREATE TABLE your_table AS SELECT * FROM source_table;"
                      minRows={8}
                      className="font-mono"
                    />
                    <p className="text-xs text-gray-500 mt-2">
                      💡 Tip: You can use multiple SQL statements separated by
                      semicolons
                    </p>
                  </div>
                </Tab>
                <Tab key="visual_builder" title="Visual Builder">
                  <div className="mt-4 space-y-4">
                    <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                      <p className="text-gray-500 mb-4">
                        Use the visual query builder for complex operations
                      </p>
                      <div className="flex gap-2 justify-center">
                        <Button
                          color="primary"
                          variant="flat"
                          onPress={() => setShowVisualBuilder?.(true)}
                        >
                          Open Visual Builder
                        </Button>
                        <Button
                          color="secondary"
                          variant="flat"
                          onPress={() => setShowSchemaBrowser?.(true)}
                        >
                          Browse Schema
                        </Button>
                      </div>
                    </div>
                  </div>
                </Tab>
              </Tabs>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
