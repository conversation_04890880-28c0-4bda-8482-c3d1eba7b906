import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Modal<PERSON>ooter,
  Progress,
  useDisclosure,
} from "@heroui/react";
import { Play, Info } from "lucide-react";

interface AdkBulkDownloadProps {
  className?: string;
}

interface AdkJobInfo {
  jobId: string;
  name: string;
  description: string;
  enabled: boolean;
  schedule: string;
  dataSource: {
    type: string;
    host?: string;
    organisationalUnits: string[];
  };
  destination: {
    type: string;
    localPath?: string;
  };
  retryConfig: {
    maxRetries: number;
    retryDelay: number;
  };
}

export function AdkBulkDownload({ className }: AdkBulkDownloadProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [jobInfo, setJobInfo] = useState<AdkJobInfo | null>(null);
  const [progress, setProgress] = useState(0);
  const [lastResult, setLastResult] = useState<string | null>(null);
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  // Fetch job information
  const fetchJobInfo = async () => {
    try {
      const response = await fetch("/api/jobs/adk-bulk");
      if (response.ok) {
        const data = await response.json();
        setJobInfo(data);
      }
    } catch (error) {
      console.error("Failed to fetch ADK job info:", error);
    }
  };

  // Load job info when component mounts
  React.useEffect(() => {
    fetchJobInfo();
  }, []);

  const triggerBulkDownload = async () => {
    setIsRunning(true);
    setProgress(0);
    setLastResult(null);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + Math.random() * 10;
        });
      }, 2000);

      const response = await fetch("/api/jobs/adk-bulk", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      clearInterval(progressInterval);

      if (response.ok) {
        const result = await response.json();
        setProgress(100);
        setLastResult(`Successfully started: ${result.jobName}`);

        // Auto-hide success message after 5 seconds
        setTimeout(() => {
          setLastResult(null);
          setProgress(0);
        }, 5000);
      } else {
        const error = await response.json();
        setProgress(0);
        setLastResult(
          `Error: ${error.error || "Failed to start bulk download"}`
        );
      }
    } catch (error) {
      setProgress(0);
      setLastResult(
        `Network error: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setIsRunning(false);
    }
  };

  const formatUnitCount = (units: string[]) => {
    return units ? units.length : 0;
  };

  return (
    <>
      <Card className={className}>
        <CardHeader className="flex gap-3">
          <div className="flex flex-col">
            <p className="text-md font-semibold">
              ADK RKAKL 2024 Bulk Download
            </p>
            <p className="text-small text-default-500">
              Download all organizational unit files from government SFTP
            </p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody>
          <div className="flex flex-col gap-4">
            {/* Job Status */}
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Status:</span>
              <Chip
                color={jobInfo?.enabled ? "success" : "warning"}
                variant="flat"
                size="sm"
              >
                {jobInfo?.enabled ? "Enabled" : "Disabled"}
              </Chip>
            </div>

            {/* Schedule */}
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Schedule:</span>
              <span className="text-sm text-default-600">
                {jobInfo?.schedule || "Not set"}
              </span>
            </div>

            {/* Organizational Units Count */}
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Org Units:</span>
              <span className="text-sm text-default-600">
                {formatUnitCount(jobInfo?.dataSource.organisationalUnits || [])}{" "}
                units
              </span>
            </div>

            {/* Destination Path */}
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Destination:</span>
              <span className="text-sm text-default-600 truncate max-w-48">
                {jobInfo?.destination.localPath || "Not set"}
              </span>
            </div>

            {/* Progress Bar */}
            {(isRunning || progress > 0) && (
              <div className="space-y-2">
                <Progress
                  aria-label="Download progress"
                  size="sm"
                  value={progress}
                  color={progress === 100 ? "success" : "primary"}
                  className="w-full"
                />
                <p className="text-xs text-default-500 text-center">
                  {isRunning
                    ? "Processing..."
                    : progress === 100
                    ? "Completed"
                    : "Ready"}
                </p>
              </div>
            )}

            {/* Result Message */}
            {lastResult && (
              <div
                className={`p-3 rounded-lg text-sm ${
                  lastResult.startsWith("Error") ||
                  lastResult.startsWith("Network")
                    ? "bg-danger-50 text-danger-700 border border-danger-200"
                    : "bg-success-50 text-success-700 border border-success-200"
                }`}
              >
                {lastResult}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2 pt-2">
              <Button
                color="primary"
                startContent={<Play className="h-4 w-4" />}
                onPress={triggerBulkDownload}
                isLoading={isRunning}
                isDisabled={isRunning}
                className="flex-1"
              >
                {isRunning ? "Running..." : "Start Download"}
              </Button>

              <Button
                variant="flat"
                startContent={<Info className="h-4 w-4" />}
                onPress={onOpen}
              >
                Details
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Details Modal */}
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} size="2xl">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                ADK RKAKL 2024 Bulk Download Details
              </ModalHeader>
              <ModalBody>
                {jobInfo ? (
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">Job Configuration</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Job ID:</span>
                          <p className="text-default-600">{jobInfo.jobId}</p>
                        </div>
                        <div>
                          <span className="font-medium">Enabled:</span>
                          <p className="text-default-600">
                            {jobInfo.enabled ? "Yes" : "No"}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium">Schedule:</span>
                          <p className="text-default-600">{jobInfo.schedule}</p>
                        </div>
                        <div>
                          <span className="font-medium">Max Retries:</span>
                          <p className="text-default-600">
                            {jobInfo.retryConfig.maxRetries}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2">Data Source</h4>
                      <div className="text-sm space-y-2">
                        <div>
                          <span className="font-medium">Type:</span>
                          <p className="text-default-600">
                            {jobInfo.dataSource.type}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium">Host:</span>
                          <p className="text-default-600">
                            {jobInfo.dataSource.host}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium">
                            Organizational Units:
                          </span>
                          <p className="text-default-600">
                            {formatUnitCount(
                              jobInfo.dataSource.organisationalUnits
                            )}{" "}
                            units configured
                          </p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2">Destination</h4>
                      <div className="text-sm space-y-2">
                        <div>
                          <span className="font-medium">Type:</span>
                          <p className="text-default-600">
                            {jobInfo.destination.type}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium">Local Path:</span>
                          <p className="text-default-600 break-all">
                            {jobInfo.destination.localPath}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2">Description</h4>
                      <p className="text-sm text-default-600">
                        {jobInfo.description}
                      </p>
                    </div>
                  </div>
                ) : (
                  <p>Loading job details...</p>
                )}
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Close
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
