import { useEffect, useState, useRef, useCallback } from "react";
import { JobStatus } from "@/types/job";

interface JobSequence {
  id: string;
  name: string;
  description: string;
  schedule?: string;
  enabled: boolean;
  onFailure: "stop" | "continue" | "retry";
  maxRetries: number;
  jobs: string[];
}

interface SSEMessage {
  type: "jobUpdate" | "sequenceUpdate" | "heartbeat";
  jobs?: JobStatus[];
  sequences?: JobSequence[];
  timestamp?: number;
}

export function useJobsSSE() {
  const [jobs, setJobs] = useState<JobStatus[]>([]);
  const [sequences, setSequences] = useState<JobSequence[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef(0);

  const connect = useCallback(() => {
    try {
      // Close existing connection if any
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }

      const eventSource = new EventSource("/api/jobs/events");
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log("SSE connection opened");
        setIsConnected(true);
        setError(null);
        retryCountRef.current = 0;
      };

      eventSource.onmessage = (event) => {
        try {
          const data: SSEMessage = JSON.parse(event.data);

          if (data.type === "jobUpdate" && data.jobs) {
            // Convert date strings back to Date objects
            const jobsData = data.jobs.map((job) => ({
              ...job,
              lastRun: new Date(job.lastRun),
              nextRun: new Date(job.nextRun),
            }));
            console.log("Setting jobs from SSE:", jobsData);
            setJobs(jobsData);
          } else if (data.type === "sequenceUpdate" && data.sequences) {
            console.log("Setting sequences from SSE:", data.sequences);
            setSequences(data.sequences);
          }
          // heartbeat messages don't need special handling
        } catch (err) {
          console.error("Error parsing SSE message:", err);
        }
      };

      eventSource.onerror = (event) => {
        console.error("SSE connection error:", event);
        setIsConnected(false);

        // Implement exponential backoff for reconnection
        if (retryCountRef.current < 5) {
          const delay = Math.min(
            1000 * Math.pow(2, retryCountRef.current),
            30000
          );
          retryCountRef.current++;

          setError(
            `Connection lost. Retrying in ${delay / 1000}s... (attempt ${
              retryCountRef.current
            })`
          );

          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, delay);
        } else {
          setError(
            "Connection failed after multiple attempts. Please refresh the page."
          );
        }
      };
    } catch (err) {
      console.error("Error creating SSE connection:", err);
      setError("Failed to establish connection");
    }
  }, []);

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    setIsConnected(false);
  }, []);

  useEffect(() => {
    connect();

    // Cleanup on unmount
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Fallback polling if SSE fails
  useEffect(() => {
    let pollInterval: NodeJS.Timeout;

    if (!isConnected && retryCountRef.current >= 5) {
      // If SSE completely failed, fall back to polling every 10 seconds
      pollInterval = setInterval(async () => {
        try {
          const response = await fetch("/api/jobs");
          const data = await response.json();
          const jobsData = data.jobs.map((job: JobStatus) => ({
            ...job,
            lastRun: new Date(job.lastRun),
            nextRun: new Date(job.nextRun),
          }));
          setJobs(jobsData);
        } catch (err) {
          console.error("Polling fallback error:", err);
        }
      }, 10000);
    }

    return () => {
      if (pollInterval) {
        clearInterval(pollInterval);
      }
    };
  }, [isConnected]);

  return {
    jobs,
    sequences,
    isConnected,
    error,
    reconnect: connect,
  };
}
