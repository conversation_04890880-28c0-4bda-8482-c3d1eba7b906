// Script to check job 4 organizational units details
import { loadJobDefinition } from "./src/lib/jobPersistence";
import { initializeDatabase } from "./src/lib/database";

async function checkJob4Details() {
  try {
    console.log("Initializing database...");
    await initializeDatabase();

    console.log("Loading job 4 definition...");
    const job4 = await loadJobDefinition("4");

    if (!job4) {
      console.error("Job 4 not found in database");
      process.exit(1);
    }

    console.log("Job 4 Details:");
    console.log("- Name:", job4.name);
    console.log("- Enabled:", job4.enabled);
    console.log(
      "- Organizational Units:",
      job4.dataSource.options?.organisationalUnits || []
    );
    console.log(
      "- Total Units:",
      (job4.dataSource.options?.organisationalUnits as string[])?.length || 0
    );

    process.exit(0);
  } catch (error) {
    console.error("Error:", error);
    process.exit(1);
  }
}

checkJob4Details();
