"use client";

// ============================================================================
// IMPORTS
// ============================================================================

// React imports
import React from "react";

// UI library imports
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
} from "@heroui/react";

// Animation imports
import { motion } from "framer-motion";

// Icon imports
import { Clock, ArrowRight, Play } from "lucide-react";

// Type imports
import { JobStatus, SystemStatus } from "@/types/job";

// Component imports
import { StatusChip, LastRunStatusChip } from "./StatusChip";
import { JobActionButtons } from "./JobActionButtons";

// Utility imports
import { formatTableDate } from "@/utils/timeUtils";
import { calculateNextRuns } from "@/utils/cronUtils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface JobsTableProps {
  jobs: JobStatus[];
  isJobRunning: boolean;
  systemStatus: SystemStatus;
  onRunJob: (jobId: string) => void;
  onCancelJob: (jobId: string) => void;
  onViewJobDetails: (job: JobStatus) => void;
  sequenceSchedule?: string; // Optional: for sequence tables to show sequence schedule
  isSequenceTable?: boolean; // Flag to indicate this is a sequence table
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

// Helper function to get next run display for sequenced jobs
const getSequenceJobNextRunDisplay = (
  job: JobStatus,
  sequenceSchedule?: string
) => {
  if (!job.sequenceConfig) {
    // Individual job - show normal schedule
    return {
      display: formatTableDate(job.nextRun),
      isSequenceDependent: false,
    };
  }

  const { order } = job.sequenceConfig;

  if (order === 1) {
    // First job in sequence - show next scheduled time if sequence has schedule
    if (sequenceSchedule) {
      try {
        // Calculate next run time from sequence schedule
        const nextRuns = calculateNextRuns(sequenceSchedule, 1);
        if (nextRuns.length > 0) {
          return {
            display: formatTableDate(nextRuns[0]),
            isSequenceDependent: false,
          };
        }
      } catch (error) {
        // Fallback to "Sequence scheduled" if calculation fails
        console.warn("Failed to calculate next run time for sequence:", error);
      }

      // Fallback display when calculation fails
      return {
        display: (
          <div className="flex items-center gap-1 text-xs">
            <Clock className="w-3 h-3 text-blue-500" />
            <span className="text-blue-600 font-medium">
              Sequence scheduled
            </span>
          </div>
        ),
        isSequenceDependent: true,
      };
    } else {
      return {
        display: (
          <div className="flex items-center gap-1 text-xs">
            <Play className="w-3 h-3 text-purple-500" />
            <span className="text-purple-600 font-medium">Awaits sequence</span>
          </div>
        ),
        isSequenceDependent: true,
      };
    }
  } else {
    // Subsequent jobs - depend on previous job completion
    return {
      display: (
        <div className="flex items-center gap-1 text-xs">
          <ArrowRight className="w-3 h-3 text-orange-500" />
          <span className="text-orange-600 font-medium">
            Awaits job {order - 1}
          </span>
        </div>
      ),
      isSequenceDependent: true,
    };
  }
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const JobsTable = ({
  jobs,
  isJobRunning,
  systemStatus,
  onRunJob,
  onCancelJob,
  onViewJobDetails,
  sequenceSchedule,
  isSequenceTable = false,
}: JobsTableProps) => {
  return (
    <Table
      aria-label="Jobs table"
      classNames={{
        wrapper: "rounded-none shadow-none",
        table: "rounded-none",
        thead: "rounded-none",
        th: "first:rounded-tl-2xl last:rounded-tr-2xl bg-default-100 border-none",
        tbody: "rounded-none",
        tr: "border-none",
        td: "border-none",
      }}
    >
      <TableHeader>
        <TableColumn width="60" className="text-center text-xs">
          {isSequenceTable ? "ORDER" : "NO"}
        </TableColumn>
        <TableColumn width="200" className="text-center text-xs">
          JOB NAME
        </TableColumn>
        <TableColumn width="80" className="text-center text-xs">
          LAST RUN
        </TableColumn>
        <TableColumn width="100" className="text-center text-xs">
          LAST RUN STATUS
        </TableColumn>
        <TableColumn width="120" className="text-center text-xs">
          {isSequenceTable ? "SEQUENCE DEPNDNCY" : "NEXT RUN"}
        </TableColumn>
        <TableColumn width="100" className="text-center text-xs">
          STATUS
        </TableColumn>
        <TableColumn width="80" className="text-center text-xs">
          DURATION
        </TableColumn>
        <TableColumn width="280" className="text-center text-xs">
          ACTIONS
        </TableColumn>
      </TableHeader>
      <TableBody>
        {jobs.map((job, index) => {
          const nextRunInfo = getSequenceJobNextRunDisplay(
            job,
            sequenceSchedule
          );

          return (
            <TableRow key={job.id}>
              <TableCell className="text-center">
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {isSequenceTable ? (
                    <Chip
                      color="secondary"
                      variant="flat"
                      size="sm"
                      className="text-xs font-bold"
                    >
                      {job.sequenceConfig?.order || "?"}
                    </Chip>
                  ) : (
                    <span className="text-xs font-medium text-gray-600">
                      {index + 1}
                    </span>
                  )}
                </motion.div>
              </TableCell>
              <TableCell className="font-semibold text-center">
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3 }}
                  className="text-sm"
                >
                  {job.name}
                </motion.div>
              </TableCell>
              <TableCell className="text-center">
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                  className="text-xs"
                >
                  {formatTableDate(job.lastRun)}
                </motion.div>
              </TableCell>
              <TableCell className="text-center">
                <LastRunStatusChip
                  status={job.lastRunStatus}
                  triggerType={job.lastRunTriggerType}
                />
              </TableCell>
              <TableCell className="text-center">
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                  className="text-xs"
                >
                  {nextRunInfo.display}
                </motion.div>
              </TableCell>
              <TableCell className="text-center">
                <StatusChip job={job} systemStatus={systemStatus} />
              </TableCell>
              <TableCell className="text-center">
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.3 }}
                  className="text-sm"
                >
                  {job.duration > 0 ? `${job.duration}s` : "-"}
                </motion.div>
              </TableCell>
              <TableCell className="text-center">
                <JobActionButtons
                  job={job}
                  isJobRunning={isJobRunning}
                  onRunJob={onRunJob}
                  onCancelJob={onCancelJob}
                  onViewDetails={onViewJobDetails}
                  allSequenceJobs={isSequenceTable ? jobs : undefined}
                />
              </TableCell>
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
};
