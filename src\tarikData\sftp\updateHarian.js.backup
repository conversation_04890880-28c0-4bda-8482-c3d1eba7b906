const mysql = require('mysql2');
const fs = require('fs');
const { exec } = require('child_process');
const xml2js = require('xml2js');
const { koneksiLokal } = require("../db/database.js");

const execPromise = (command) =>
  new Promise((resolve, reject) => {
    exec(command, (err, stdout, stderr) => {
      if (err) reject(stderr || err);
      else resolve(stdout);
    });
  });

(async function() {
  let connection;
  try {
    console.log('Establishing connection...');
    connection = await koneksiLokal();
    console.log('Connection established successfully.');

    const sql = `
SELECT DISTINCT LEFT(folder, 2) AS folder1,
                MID(folder, 4, 3) AS folder2,
                nmfile AS fileku,
                CASE 
                    WHEN RIGHT(nmfile, 2) = '24' THEN '00'
                    ELSE RIGHT(nmfile, 2)
                END AS norev
FROM monev2024.ftp_baru_2024
WHERE RIGHT(nmfile, 3) <> 'pdf'
  AND (LEFT(nmfile, 1) = 'd' OR LEFT(nmfile, 1) = 'D') 
ORDER BY 3

    `;
    console.log('Executing SQL query...');
    const [result] = await connection.query(sql);
    console.log(`${result.length} records found.`);

    for (const obj of result) {
      const nmfile = obj.fileku;
      const kdjendok = nmfile.substr(0, 2);
      const kddept = nmfile.substr(4, 3);
      const kdunit = nmfile.substr(7, 2);
      const kdsatker = nmfile.substr(13, 6);
      const kddekon = nmfile.substr(20, 1);
      const tmp = nmfile.split('.');
      const rev_ku = tmp[1].length === 5 ? tmp[1].substr(3, 2) : '00';

      const extractionPath = `C:\\KUMPULAN_ADK\\XML\\`;
      const nmtabel = `${extractionPath}d_item${kddept}${kdunit}${rev_ku}${kdsatker}${kddekon}.xml`;
      console.log(`Deleting old XML files...${nmtabel}`);

      await execPromise('del C:\\KUMPULAN_ADK\\XML\\*.xml /f /q');
      console.log('Old XML files deleted successfully.');

      if (!fs.existsSync(extractionPath)) {
        await fs.promises.mkdir(extractionPath, { recursive: true });
        console.log(`Folder created: ${extractionPath}`);
      }

      console.log(`Extracting XML file: ${nmfile}`);
      await execPromise(`C:\\KUMPULAN_ADK\\TOOLS\\Rar.exe e C:\\KUMPULAN_ADK\\ADK_2024_DIPA\\${obj.folder1}\\${obj.folder2}\\${nmfile} ${extractionPath}`);

     
      console.log(`Processing file: ${nmtabel} revisi ke ${rev_ku}`);

      if (!fs.existsSync(nmtabel)) {
        console.error(`File ${nmtabel} does not exist, skipping...`);
        return;
      }

      const data = await fs.promises.readFile(nmtabel, 'utf8');
      const parser = new xml2js.Parser({ explicitArray: false });
      const parsedResult = await parser.parseStringPromise(data);
      const items = parsedResult.VFPData.c_item || [];
     
      const deleteSQL = `DELETE FROM dbdipa24.d_item WHERE kddept='${kddept}' AND kdunit='${kdunit}' AND kdsatker='${kdsatker}' AND kdjendok='${kdjendok}';`;
      await connection.query(deleteSQL);
      console.log('Data lama deleted successfully.');

      console.log(`Inserting new data from XML into d_item table...`);
      for (const item of items) {
        const insertSQL = `INSERT IGNORE INTO dbdipa24.d_item (thang, kdjendok, kdsatker, kddept, kdunit, kdprogram, kdgiat, kdoutput, kdlokasi, kdkabkota, kddekon, kdsoutput, kdkmpnen, kdskmpnen, kdakun, kdkppn, kdbeban, kdjnsban, kdctarik, register, carahitung, header1, header2, kdheader, noitem, nmitem, vol1, sat1, vol2, sat2, vol3, sat3, vol4, sat4, volkeg, satkeg, hargasat, jumlah, paguphln, pagurmp, pagurkp, kdblokir, blokirphln, blokirrmp, blokirrkp, rphblokir, kdcopy, kdabt, kdsbu, volsbk, volrkakl, blnkontrak, nokontrak, tgkontrak, nilkontrak, januari, pebruari, maret, april, mei, juni, juli, agustus, september, oktober, nopember, desember, jmltunda, kdluncuran, jmlabt, norev, kdubah, kurs, indexkpjm, kdib, jumlah2, kdstatus, levelrev, revrkaklke, revdipake) 
        VALUES ('${item.thang}', '${item.kdjendok}', '${item.kdsatker}', '${item.kddept}', '${item.kdunit}', '${item.kdprogram}', '${item.kdgiat}', '${item.kdoutput}', '${item.kdlokasi}', '${item.kdkabkota}', '${item.kddekon}', '${item.kdsoutput}', '${item.kdkmpnen}', '${item.kdskmpnen}', '${item.kdakun}', '${item.kdkppn}', '${item.kdbeban}', '${item.kdjnsban}', '${item.kdctarik}', '${item.register}', '${item.carahitung}', '${item.header1}', '${item.header2}', '${item.kdheader}', '${item.noitem}', '${item.nmitem}', '${item.vol1}', '${item.sat1}', '${item.vol2}', '${item.sat2}', '${item.vol3}', '${item.sat3}', '${item.vol4}', '${item.sat4}', '${item.volkeg}', '${item.satkeg}', '${item.hargasat}', '${item.jumlah}', '${item.paguphln}', '${item.pagurmp}', '${item.pagurkp}', '${item.kdblokir}', '${item.blokirphln}', '${item.blokirrmp}', '${item.blokirrkp}', '${item.rphblokir}', '${item.kdcopy}', '${item.kdabt}', '${item.kdsbu}', '${item.volsbk}', '${item.volrkakl}', '${item.blnkontrak}', '${item.nokontrak}', '${item.tgkontrak}', '${item.nilkontrak}', '${item.januari}', '${item.pebruari}', '${item.maret}', '${item.april}', '${item.mei}', '${item.juni}', '${item.juli}', '${item.agustus}', '${item.september}', '${item.oktober}', '${item.nopember}', '${item.desember}', '${item.jmltunda}', '${item.kdluncuran}', '${item.jmlabt}', '${item.norev}', '${item.kdubah}', '${item.kurs}', '${item.indexkpjm}', '${item.kdib}', '${item.jumlah2}', '${item.kdstatus}', '${item.levelrev}', '${item.revrkaklke}', '${item.revdipake}');`;
        await connection.query(insertSQL);
      }
      console.log('Data insertion completed.');
    }
  } catch (err) {
    console.error('Error:', err);
  } finally {
    if (connection) await connection.end();
  }
})();
