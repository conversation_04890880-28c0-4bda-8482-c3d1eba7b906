import { BaseJobHandler } from "../base/BaseJobHandler";
import type {
  <PERSON><PERSON><PERSON><PERSON>,
  JobExecutionContext,
  FileProcessingResult,
} from "../types";
import type { JobDefinition } from "../../jobManager";
import { logger } from "../../jobManager";
import Client from "ssh2-sftp-client";
import fs from "fs";
import path from "path";
import {
  saveFileMetadata as saveFileMetadataToCustomDb,
  ensureFileTrackingTable,
  FileMetadataRecord,
} from "../../fileTrackingDatabase";

/**
 * Job handler for SFTP operations
 */
export class SftpJobHandler extends BaseJobHandler {
  public readonly jobType = "sftp";

  /**
   * Execute SFTP job
   */
  public async execute(context: JobExecutionContext): Promise<JobResult> {
    const { jobDefinition } = context;
    const sftpConfig = jobDefinition.dataSource.sftp;

    if (!sftpConfig) {
      throw new Error("SFTP configuration is required for SFTP data source");
    }

    // Validate configuration
    this.validateSftpConfig(sftpConfig);

    const startTime = await this.logOperationStart(
      context,
      "SFTP connection",
      `${sftpConfig.host}:${sftpConfig.port}`
    );

    // Check if this is an ADK RKAKL bulk download job
    if (jobDefinition.dataSource.options?.bulkDownload) {
      return await this.executeBulkDownload(context, sftpConfig);
    }

    // Regular SFTP processing
    return await this.executeRegularSftp(context, sftpConfig, startTime);
  }

  /**
   * Execute bulk download operations
   */
  private async executeBulkDownload(
    context: JobExecutionContext,
    sftpConfig: NonNullable<JobDefinition["dataSource"]["sftp"]>
  ): Promise<JobResult> {
    await context.addLog(
      `Starting ${context.jobDefinition.name} bulk download process...`
    );

    try {
      // Use the directory browsing approach for bulk downloads
      const result = await this.executeRegularSftp(
        context,
        sftpConfig,
        Date.now()
      );
      const fileCount = Array.isArray(result.data) ? result.data.length : 0;

      await context.addLog(
        `ADK RKAKL bulk download completed. Found ${fileCount} files total`
      );

      // Return a compatible result structure
      return this.createJobResult(fileCount, {
        type: "bulk_download",
        unitsProcessed: 0, // No longer using organizational units
        status: "completed",
        downloadPath:
          context.jobDefinition.destination.localPath ||
          "C:/KUMPULAN_ADK/ADK_2025_DIPA",
        filesDownloaded: fileCount,
        errors: [],
        recordsProcessed: fileCount,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown ADK download error";
      await context.addLog(`ADK RKAKL bulk download failed: ${errorMessage}`);
      throw new Error(`ADK RKAKL bulk download failed: ${errorMessage}`);
    }
  }

  /**
   * Execute regular SFTP operations
   */
  private async executeRegularSftp(
    context: JobExecutionContext,
    sftpConfig: NonNullable<JobDefinition["dataSource"]["sftp"]>,
    startTime: number
  ): Promise<JobResult> {
    try {
      await context.addLog(
        `Connecting to SFTP server at ${sftpConfig.host}:${sftpConfig.port}...`
      );

      // Create SFTP client for real connection
      const sftp = new Client();

      // Attempt real SFTP connection
      await sftp.connect({
        host: sftpConfig.host,
        port: sftpConfig.port,
        username: sftpConfig.username,
        password: sftpConfig.password,
        readyTimeout: 10000, // 10 second timeout
        retries: 1,
      });

      await context.addLog("Connected to SFTP server successfully");
      await context.addLog(`Browsing directory: ${sftpConfig.remotePath}`);

      await context.checkCancellation();

      // Check if we're in test mode with a specific path
      const testPath = context.jobDefinition.dataSource.options
        ?.testPath as string;
      let twoDigitFolders: Array<{ name: string; type: string }> = [];

      if (testPath) {
        twoDigitFolders = await this.processTestPath(
          context,
          sftp,
          sftpConfig,
          testPath
        );
      } else {
        twoDigitFolders = await this.processAllFolders(
          context,
          sftp,
          sftpConfig
        );
      }

      await context.checkCancellation();

      // Process each 2-digit folder recursively
      const processedFiles: FileProcessingResult[] = [];

      for (const folder of twoDigitFolders) {
        try {
          await context.addLog(`Processing 2-digit folder: ${folder.name}`);
          await context.checkCancellation();

          const folderFiles = await this.processTwoDigitFolder(
            context,
            sftp,
            sftpConfig,
            folder.name,
            testPath
          );

          processedFiles.push(...folderFiles);
        } catch (folderError) {
          const errorMessage =
            folderError instanceof Error
              ? folderError.message
              : "Unknown folder processing error";
          await context.addLog(
            `Error processing folder ${folder.name}: ${errorMessage}`
          );
        }
      }

      await context.addLog(
        `Successfully processed ${twoDigitFolders.length} organizational folders and found ${processedFiles.length} files total`
      );

      // Close SFTP connection
      await sftp.end();

      await this.logOperationComplete(
        context,
        "SFTP operation",
        startTime,
        `${processedFiles.length} files processed`
      );

      return this.processFileResults(
        processedFiles.map((f) => ({
          name: f.name,
          size: f.size,
          status: f.status,
        }))
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown SFTP error";
      await context.addLog(`SFTP connection failed: ${errorMessage}`);
      throw new Error(`SFTP connection failed: ${errorMessage}`);
    }
  }

  /**
   * Process test path for specific directory testing
   */
  private async processTestPath(
    context: JobExecutionContext,
    sftp: Client,
    sftpConfig: NonNullable<JobDefinition["dataSource"]["sftp"]>,
    testPath: string
  ): Promise<Array<{ name: string; type: string }>> {
    // Parse test path like "34/152/2025" or "34"
    const pathParts = testPath.split("/");
    const twoDigitFolder = pathParts[0];

    await context.addLog(`Test mode: Processing specific path ${testPath}`);

    // Verify the 2-digit folder exists
    const rootList = await sftp.list(sftpConfig.remotePath);
    const targetFolder = rootList.find(
      (item) => item.type === "d" && item.name === twoDigitFolder
    );

    if (targetFolder) {
      await context.addLog(`Test mode: Found target folder ${twoDigitFolder}`);
      return [targetFolder];
    } else {
      await context.addLog(
        `Test mode: Target folder ${twoDigitFolder} not found`
      );
      return [];
    }
  }

  /**
   * Process all folders in normal mode
   */
  private async processAllFolders(
    context: JobExecutionContext,
    sftp: Client,
    sftpConfig: NonNullable<JobDefinition["dataSource"]["sftp"]>
  ): Promise<Array<{ name: string; type: string }>> {
    // List all directories in the remote directory (looking for 2-digit folders)
    const rootList = await sftp.list(sftpConfig.remotePath);

    // Filter for 2-digit directories only (exclude 3-digit directories)
    const twoDigitFolders = rootList.filter((item) => {
      return item.type === "d" && /^[0-9]{2}$/.test(item.name);
    });

    await context.addLog(
      `Found ${twoDigitFolders.length} 2-digit organizational folders to process`
    );

    return twoDigitFolders;
  }

  /**
   * Process a single 2-digit folder
   */
  private async processTwoDigitFolder(
    context: JobExecutionContext,
    sftp: Client,
    sftpConfig: NonNullable<JobDefinition["dataSource"]["sftp"]>,
    folderName: string,
    testPath?: string
  ): Promise<FileProcessingResult[]> {
    const processedFiles: FileProcessingResult[] = [];

    // Browse 3-digit subfolders within this 2-digit folder
    const twoDigitPath = `${sftpConfig.remotePath}/${folderName}`;
    const subfolderList = await sftp.list(twoDigitPath);

    // Filter for 3-digit directories, optionally filtering by test path
    let threeDigitFolders = subfolderList.filter((item) => {
      return item.type === "d" && /^[0-9]{3}$/.test(item.name);
    });

    // If in test mode, filter to specific 3-digit folder
    if (testPath) {
      const pathParts = testPath.split("/");
      if (pathParts.length > 1) {
        const targetThreeDigit = pathParts[1];
        threeDigitFolders = threeDigitFolders.filter(
          (item) => item.name === targetThreeDigit
        );

        if (threeDigitFolders.length > 0) {
          await context.addLog(
            `Test mode: Found target 3-digit folder ${targetThreeDigit} in ${folderName}`
          );
        } else {
          await context.addLog(
            `Test mode: Target 3-digit folder ${targetThreeDigit} not found in ${folderName}`
          );
        }
      }
    }

    await context.addLog(
      `Found ${threeDigitFolders.length} 3-digit subfolders in ${folderName}${
        testPath ? " (filtered by test path)" : ""
      }`
    );

    // Process each 3-digit subfolder
    for (const subfolder of threeDigitFolders) {
      try {
        await context.checkCancellation();

        const subfolderFiles = await this.processThreeDigitFolder(
          context,
          sftp,
          sftpConfig,
          folderName,
          subfolder.name,
          testPath
        );

        processedFiles.push(...subfolderFiles);
      } catch (subfolderError) {
        const errorMessage =
          subfolderError instanceof Error
            ? subfolderError.message
            : "Unknown subfolder processing error";
        await context.addLog(
          `Error processing subfolder ${folderName}/${subfolder.name}: ${errorMessage}`
        );
      }
    }

    return processedFiles;
  }

  /**
   * Process a single 3-digit folder
   */
  private async processThreeDigitFolder(
    context: JobExecutionContext,
    sftp: Client,
    sftpConfig: NonNullable<JobDefinition["dataSource"]["sftp"]>,
    twoDigitFolder: string,
    threeDigitFolder: string,
    testPath?: string
  ): Promise<FileProcessingResult[]> {
    const processedFiles: FileProcessingResult[] = [];
    const subfolderPath = `${sftpConfig.remotePath}/${twoDigitFolder}/${threeDigitFolder}`;

    // Look for year folders within the 3-digit folder
    const yearFolderList = await sftp.list(subfolderPath);
    let yearFolders = yearFolderList.filter((item) => {
      return item.type === "d" && /^20[0-9]{2}$/.test(item.name); // Match year format like 2024, 2025, etc.
    });

    // If in test mode, filter to specific year folder
    if (testPath) {
      const pathParts = testPath.split("/");
      if (pathParts.length > 2) {
        const targetYear = pathParts[2];
        yearFolders = yearFolders.filter((item) => item.name === targetYear);

        if (yearFolders.length > 0) {
          await context.addLog(
            `Test mode: Found target year folder ${targetYear} in ${twoDigitFolder}/${threeDigitFolder}`
          );
        } else {
          await context.addLog(
            `Test mode: Target year folder ${targetYear} not found in ${twoDigitFolder}/${threeDigitFolder}`
          );
        }
      }
    }

    if (yearFolders.length === 0) {
      await context.addLog(
        `No year folders found in ${twoDigitFolder}/${threeDigitFolder}${
          testPath ? " (after test path filtering)" : ""
        }`
      );
      return processedFiles;
    }

    await context.addLog(
      `Found ${
        yearFolders.length
      } year folders in ${twoDigitFolder}/${threeDigitFolder}${
        testPath ? " (filtered by test path)" : ""
      }`
    );

    // Process each year folder
    for (const yearFolder of yearFolders) {
      try {
        await context.checkCancellation();

        const yearFolderPath = `${subfolderPath}/${yearFolder.name}`;
        const yearFolderFiles = await this.browseFolderRecursively(
          sftp,
          yearFolderPath,
          sftpConfig.filePattern || "*",
          context
        );

        // Download the files to local directory
        if (yearFolderFiles.length > 0) {
          await context.addLog(
            `Downloading ${yearFolderFiles.length} files from ${twoDigitFolder}/${threeDigitFolder}/${yearFolder.name}...`
          );

          const downloadedFiles = await this.downloadFiles(
            context,
            sftp,
            yearFolderFiles,
            `${twoDigitFolder}/${threeDigitFolder}/${yearFolder.name}`
          );

          processedFiles.push(...downloadedFiles);
        }
      } catch (yearFolderError) {
        const errorMessage =
          yearFolderError instanceof Error
            ? yearFolderError.message
            : "Unknown year folder processing error";
        await context.addLog(
          `Error processing year folder ${twoDigitFolder}/${threeDigitFolder}/${yearFolder.name}: ${errorMessage}`
        );
      }
    }

    return processedFiles;
  }

  /**
   * Recursively browse a folder and its subdirectories to find files matching the pattern
   */
  private async browseFolderRecursively(
    sftp: Client,
    folderPath: string,
    pattern: string,
    context: JobExecutionContext
  ): Promise<
    Array<{
      name: string;
      size: number;
      lastModified: Date;
      remotePath: string;
    }>
  > {
    const allFiles: Array<{
      name: string;
      size: number;
      lastModified: Date;
      remotePath: string;
    }> = [];

    try {
      // List contents of the current folder
      const folderContents = await sftp.list(folderPath);

      // Process files in current folder
      const files = folderContents.filter((item) => {
        if (item.type !== "-") return false; // Only regular files

        if (pattern === "*") return true;

        // Simple pattern matching
        const regex = new RegExp(pattern.replace(/\*/g, ".*"));
        return regex.test(item.name);
      });

      // Add files from current folder
      files.forEach((file) => {
        allFiles.push({
          name: file.name,
          size: file.size,
          lastModified: new Date(file.modifyTime),
          remotePath: `${folderPath}/${file.name}`,
        });
      });

      // Process subdirectories recursively
      const subdirectories = folderContents.filter((item) => item.type === "d");

      for (const subdir of subdirectories) {
        try {
          await context.checkCancellation();

          const subdirPath = `${folderPath}/${subdir.name}`;
          const subdirFiles = await this.browseFolderRecursively(
            sftp,
            subdirPath,
            pattern,
            context
          );

          allFiles.push(...subdirFiles);
        } catch (subdirError) {
          // Log subdirectory errors but continue processing other directories
          const errorMessage =
            subdirError instanceof Error
              ? subdirError.message
              : "Unknown subdirectory error";
          await context.addLog(
            `Warning: Could not access subdirectory ${subdir.name}: ${errorMessage}`
          );
        }
      }
    } catch (error) {
      // Log folder access errors but don't throw to allow processing of other folders
      const errorMessage =
        error instanceof Error ? error.message : "Unknown folder access error";
      await context.addLog(
        `Warning: Could not access folder ${folderPath}: ${errorMessage}`
      );
    }

    return allFiles;
  }

  /**
   * Download files and create metadata
   */
  private async downloadFiles(
    context: JobExecutionContext,
    sftp: Client,
    files: Array<{
      name: string;
      size: number;
      lastModified: Date;
      remotePath: string;
    }>,
    folderPath: string
  ): Promise<FileProcessingResult[]> {
    const processedFiles: FileProcessingResult[] = [];
    const localBasePath =
      context.jobDefinition.destination.localPath ||
      "C:/KUMPULAN_ADK/ADK_2025_DIPA";
    const localFolderPath = `${localBasePath}/${folderPath}`;

    // Ensure local directory exists
    if (!fs.existsSync(localFolderPath)) {
      fs.mkdirSync(localFolderPath, { recursive: true });
      await context.addLog(`Created local directory: ${localFolderPath}`);
    }

    // Download each file and create metadata
    let downloadedCount = 0;
    const executionId = context.executionId;

    for (const file of files) {
      try {
        await context.checkCancellation();

        const localFilePath = path.join(localFolderPath, file.name);

        // Skip if file already exists
        if (fs.existsSync(localFilePath)) {
          // Still create metadata for existing files
          await this.createSftpFileMetadata(
            context,
            file,
            localFilePath,
            folderPath,
            executionId
          );

          processedFiles.push({
            name: file.name,
            size: file.size,
            lastModified: file.lastModified,
            remotePath: file.remotePath,
            localPath: localFilePath,
            status: "skipped",
          });
          continue;
        }

        await sftp.get(file.remotePath, localFilePath);
        downloadedCount++;

        // Create metadata for successfully downloaded file
        await this.createSftpFileMetadata(
          context,
          file,
          localFilePath,
          folderPath,
          executionId
        );

        processedFiles.push({
          name: file.name,
          size: file.size,
          lastModified: file.lastModified,
          remotePath: file.remotePath,
          localPath: localFilePath,
          status: "processed",
        });

        if (downloadedCount % 50 === 0) {
          await context.addLog(
            `Downloaded ${downloadedCount}/${files.length} files from ${folderPath}`
          );
        }
      } catch (downloadError) {
        const errorMessage =
          downloadError instanceof Error
            ? downloadError.message
            : "Unknown download error";

        processedFiles.push({
          name: file.name,
          size: file.size,
          lastModified: file.lastModified,
          remotePath: file.remotePath,
          status: "error",
          error: errorMessage,
        });

        await context.addLog(`Error downloading ${file.name}: ${errorMessage}`);
      }
    }

    await context.addLog(
      `Successfully downloaded ${downloadedCount}/${files.length} files to ${localFolderPath}`
    );

    return processedFiles;
  }

  /**
   * Create SFTP file metadata using the standard SFTP metadata logic
   * Now includes logic to only mark the latest version of files as "NEW"
   */
  private async createSftpFileMetadata(
    context: JobExecutionContext,
    file: {
      name: string;
      size: number;
      lastModified: Date;
      remotePath: string;
    },
    localFilePath: string,
    folderPath: string,
    executionId: string
  ): Promise<void> {
    try {
      // Create metadata using the same structure as sftpManager.ts
      const metadata = {
        folder: folderPath,
        filename: file.name,
        size: file.size,
        modifyTime: file.lastModified,
        localPath: localFilePath,
      };

      // Use the existing SFTP metadata saving logic
      const fileTrackingConfig =
        context.jobDefinition.destination?.fileTracking;

      if (fileTrackingConfig?.enabled && fileTrackingConfig.database) {
        // Ensure the file tracking table exists and has required columns
        await ensureFileTrackingTable(fileTrackingConfig.database);

        // Determine the appropriate status for this file
        const fileStatus = await this.determineFileStatus(
          fileTrackingConfig.database,
          metadata.folder,
          metadata.filename
        );

        // Prepare metadata record using the same format as sftpManager.ts
        const metadataRecord: FileMetadataRecord = {
          folder: metadata.folder,
          filename: metadata.filename,
          size: metadata.size,
          modifyTime: metadata.modifyTime,
          status: fileStatus,
          jobId: context.jobId,
          executionId: executionId,
        };

        // Save to the configured database using the existing function
        await saveFileMetadataToCustomDb(
          fileTrackingConfig.database,
          metadataRecord
        );

        logger.info("File metadata saved using SFTP tracking", {
          database: fileTrackingConfig.database.database,
          table: fileTrackingConfig.database.table,
          folder: metadata.folder,
          filename: metadata.filename,
          status: fileStatus,
          jobId: context.jobId,
          executionId,
        });
      } else {
        // File tracking is not enabled for this job
        logger.debug("File tracking not enabled for this job", {
          jobId: context.jobId,
          executionId,
          filename: metadata.filename,
        });
      }
    } catch (metadataError) {
      const errorMsg =
        metadataError instanceof Error
          ? metadataError.message
          : "Unknown metadata error";
      await context.addLog(
        `Warning: Failed to create metadata for ${file.name}: ${errorMsg}`
      );

      logger.error("Failed to save SFTP file metadata", {
        error: errorMsg,
        metadata: {
          folder: folderPath,
          filename: file.name,
          size: file.size,
        },
        jobId: context.jobId,
        executionId,
      });
    }
  }

  /**
   * Determine the appropriate status for a file based on version comparison
   * Only the latest version of files with the same base name gets "NEW" status
   */
  private async determineFileStatus(
    dbConfig: NonNullable<
      JobDefinition["destination"]["fileTracking"]
    >["database"],
    folder: string,
    filename: string
  ): Promise<string> {
    if (!dbConfig) {
      return "NEW";
    }

    try {
      // Extract base name and extension from filename
      const { baseName, extension } = this.parseFilename(filename);

      // If this file doesn't have a version extension, mark it as NEW
      if (!extension) {
        return "NEW";
      }

      // Get all files in the same folder with the same base name
      const mysql = await import("mysql2/promise");
      const connection = await mysql.createConnection({
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database,
        user: dbConfig.username,
        password: dbConfig.password,
      });

      try {
        const [rows] = await connection.query(
          `SELECT nmfile FROM ${dbConfig.table}
           WHERE folder = ? AND nmfile LIKE ?`,
          [folder, `${baseName}.%`]
        );

        const existingFiles = rows as Array<{ nmfile: string }>;

        // Add current file to the list for comparison
        const allFiles = [...existingFiles.map((row) => row.nmfile), filename];

        // Find the latest version
        const latestFile = this.findLatestVersion(allFiles);

        // Return "NEW" only if this is the latest version, otherwise "SUPERSEDED"
        return filename === latestFile ? "NEW" : "SUPERSEDED";
      } finally {
        await connection.end();
      }
    } catch (error) {
      // If there's any error in determining status, default to "NEW" to be safe
      logger.warn("Error determining file status, defaulting to NEW", {
        folder,
        filename,
        error: error instanceof Error ? error.message : String(error),
      });
      return "NEW";
    }
  }

  /**
   * Parse filename to extract base name and extension
   * Examples:
   * - "dipa_file.s25" -> { baseName: "dipa_file", extension: "s25" }
   * - "dipa_file.s2501" -> { baseName: "dipa_file", extension: "s2501" }
   * - "dipa_file.pdf" -> { baseName: "dipa_file", extension: "pdf" }
   */
  private parseFilename(filename: string): {
    baseName: string;
    extension: string | null;
  } {
    const lastDotIndex = filename.lastIndexOf(".");
    if (lastDotIndex === -1) {
      return { baseName: filename, extension: null };
    }

    const baseName = filename.substring(0, lastDotIndex);
    const extension = filename.substring(lastDotIndex + 1);

    return { baseName, extension };
  }

  /**
   * Find the latest version from a list of files with the same base name
   * Assumes extensions like s25, s2501, s2502 where higher numbers = newer
   */
  private findLatestVersion(filenames: string[]): string {
    if (filenames.length === 0) {
      throw new Error("No files provided for version comparison");
    }

    if (filenames.length === 1) {
      return filenames[0];
    }

    // Sort files by their version number (extracted from extension)
    const sortedFiles = filenames.sort((a, b) => {
      const versionA = this.extractVersionNumber(a);
      const versionB = this.extractVersionNumber(b);

      // Higher version number comes first (descending order)
      return versionB - versionA;
    });

    return sortedFiles[0];
  }

  /**
   * Extract version number from filename extension
   * Examples:
   * - "file.s25" -> 25
   * - "file.s2501" -> 2501
   * - "file.pdf" -> 0 (non-versioned files get lowest priority)
   */
  private extractVersionNumber(filename: string): number {
    const { extension } = this.parseFilename(filename);

    if (!extension) {
      return 0;
    }

    // Look for pattern like "s" followed by numbers
    const versionMatch = extension.match(/^s(\d+)$/i);
    if (versionMatch) {
      return parseInt(versionMatch[1], 10);
    }

    // For non-versioned extensions (like pdf), return 0
    return 0;
  }

  /**
   * Validate SFTP job configuration
   */
  public validateConfig(jobDef: JobDefinition): boolean {
    if (!super.validateConfig(jobDef)) {
      return false;
    }

    const sftpConfig = jobDef.dataSource.sftp;
    if (!sftpConfig) {
      return false;
    }

    try {
      this.validateSftpConfig(sftpConfig);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get required permissions for SFTP operations
   */
  public getRequiredPermissions(): string[] {
    return ["sftp:read", "filesystem:write"];
  }

  /**
   * Validate SFTP configuration fields
   */
  private validateSftpConfig(
    config: NonNullable<JobDefinition["dataSource"]["sftp"]>
  ): void {
    this.validateRequiredFields(
      config,
      ["host", "port", "username", "remotePath"],
      "SFTP configuration"
    );

    // Additional SFTP-specific validations
    if (
      typeof config.port !== "number" ||
      config.port <= 0 ||
      config.port > 65535
    ) {
      throw new Error("SFTP port must be a valid number between 1 and 65535");
    }

    if (!config.password && !config.privateKey) {
      throw new Error(
        "SFTP configuration must specify either password or privateKey"
      );
    }

    if (!config.remotePath.trim()) {
      throw new Error("SFTP remote path cannot be empty");
    }
  }
}
