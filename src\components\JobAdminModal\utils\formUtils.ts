import { JobDefinition, JobSequence } from "@/lib/jobManager";
import { DestinationOptions } from "./types";

/**
 * Updates nested object properties using dot notation path
 */
export const updateNestedField = (
  obj: Record<string, unknown>,
  path: string,
  value: unknown
): Record<string, unknown> => {
  const keys = path.split(".");
  const newObj = { ...obj };
  let current: Record<string, unknown> = newObj;

  for (let i = 0; i < keys.length - 1; i++) {
    if (!current[keys[i]]) {
      current[keys[i]] = {};
    }
    current = current[keys[i]] as Record<string, unknown>;
  }

  current[keys[keys.length - 1]] = value;

  // Auto-sync between metadataTable and fileTracking database for SFTP jobs
  if ((newObj as unknown as JobDefinition).destination.type === "local") {
    const job = newObj as unknown as JobDefinition;

    // When fileTracking database changes, update metadataTable
    if (path.startsWith("destination.fileTracking.database.")) {
      const fileTrackingDb = job.destination.fileTracking?.database;
      if (fileTrackingDb?.database && fileTrackingDb?.table) {
        // Update the metadataTable in options to match fileTracking
        if (!job.destination.options) {
          job.destination.options = {
            createDirectory: true,
            preserveStructure: true,
            trackInDatabase: true,
            metadataTable: "",
            errorLogTable: "log_ftp.error_logs",
          };
        }
        (
          job.destination.options as DestinationOptions
        ).metadataTable = `${fileTrackingDb.database}.${fileTrackingDb.table}`;
      }
    }

    // When metadataTable changes, update fileTracking database for SFTP jobs
    if (
      path === "destination.options.metadataTable" &&
      job.dataSource.type === "sftp" &&
      typeof value === "string" &&
      value.includes(".")
    ) {
      const [database, table] = value.split(".");
      if (database && table) {
        // Initialize fileTracking if it doesn't exist
        if (!job.destination.fileTracking) {
          job.destination.fileTracking = {
            enabled: true,
            database: {
              host: "localhost",
              port: 3306,
              username: "root",
              password: "",
              database: "",
              table: "",
            },
          };
        }

        // Update fileTracking database config
        if (job.destination.fileTracking.database) {
          job.destination.fileTracking.database.database = database;
          job.destination.fileTracking.database.table = table;
        }
      }
    }
  }

  return newObj;
};

/**
 * Creates a new job template with default values
 */
export const createNewJob = (): JobDefinition => {
  return {
    id: `job-${Date.now()}`,
    name: "New Job",
    description: "",
    schedule: "0 2 * * *",
    enabled: true,
    dataSource: {
      type: "database_admin",
      database_admin: {
        host: "localhost",
        port: 3306,
        database: "",
        username: "",
        password: "",
        connectionLimit: 10,
        acquireTimeout: 60000,
        timeout: 120000,
        operationMode: "table_management",
        operations: [],
        rawSql: `-- Example: Drop and recreate summary table
DROP TABLE IF EXISTS TEST_DROP_CREATE;

CREATE TABLE TEST_DROP_CREATE AS
SELECT
    a.kddept,
    b.nmdept,
    ROUND(SUM(a.pagu)/1, 0) AS PAGU_DIPA,
    ROUND(SUM(real1 + real2 + real3 + real4 + real5 + real6 + real7 + real8 + real9 + real10 + real11 + real12) / 1, 0) AS REALISASI,
    ROUND(SUM(a.blokir) / 1, 0) AS BLOKIR
FROM
    pagu_real_detail_harian_2025 a
LEFT JOIN
    t_dept_2025 b ON a.kddept = b.kddept
GROUP BY
    a.kddept;`,
      },
    },
    destination: {
      type: "database",
      database: {
        type: "mysql",
        host: "localhost",
        port: 3306,
        database: "monev2025",
        username: "",
        password: "",
        table: "job_results",
      },
    },
    retryConfig: {
      maxRetries: 3,
      retryDelay: 300,
    },
  };
};

/**
 * Creates a new SFTP job template with proper fileTracking configuration
 */
export const createNewSftpJob = (): JobDefinition => {
  return {
    id: `sftp-job-${Date.now()}`,
    name: "New SFTP Job",
    description: "Download files from SFTP server",
    schedule: "0 2 * * *",
    enabled: true,
    dataSource: {
      type: "sftp",
      sftp: {
        host: "",
        port: 22,
        username: "",
        password: "",
        remotePath: "",
        filePattern: "*.pdf",
      },
    },
    destination: {
      type: "local",
      localPath: "C:/downloads",
      options: {
        createDirectory: true,
        preserveStructure: true,
        trackInDatabase: true,
        metadataTable: "monev2025.file_metadata",
        errorLogTable: "log_ftp.error_logs",
      },
      fileTracking: {
        enabled: true,
        database: {
          host: "localhost",
          port: 3306,
          username: "root",
          password: "",
          database: "monev2025",
          table: "file_metadata",
        },
      },
    },
    retryConfig: {
      maxRetries: 3,
      retryDelay: 300,
    },
  };
};

/**
 * Creates a new sequence template with default values
 */
export const createNewSequence = (): JobSequence => {
  return {
    id: "",
    name: "",
    description: "",
    schedule: "",
    enabled: true,
    onFailure: "stop",
    maxRetries: 1,
    jobs: [],
  };
};

/**
 * Creates a duplicated job with safety modifications
 */
export const duplicateJob = (job: JobDefinition): JobDefinition => {
  const duplicatedJob: JobDefinition = {
    ...job,
    id: `job-${Date.now()}`,
    name: `${job.name} (Copy)`,
    enabled: false, // Start disabled for safety
  };

  // Ensure destination options and fileTracking are properly initialized for local destinations
  if (duplicatedJob.destination.type === "local") {
    if (!duplicatedJob.destination.options) {
      duplicatedJob.destination.options = {
        createDirectory: true,
        preserveStructure: true,
        trackInDatabase: true,
        metadataTable: "",
        errorLogTable: "",
      };
    }

    // Initialize fileTracking for SFTP jobs with local destination
    if (
      duplicatedJob.dataSource.type === "sftp" &&
      !duplicatedJob.destination.fileTracking
    ) {
      duplicatedJob.destination.fileTracking = {
        enabled: true,
        database: {
          host: "localhost",
          port: 3306,
          username: "root",
          password: "",
          database: "",
          table: "file_metadata",
        },
      };
    }

    // Sync metadataTable with fileTracking database for SFTP jobs
    if (
      duplicatedJob.dataSource.type === "sftp" &&
      duplicatedJob.destination.fileTracking?.database?.database &&
      duplicatedJob.destination.fileTracking?.database?.table
    ) {
      const fileTrackingDb = duplicatedJob.destination.fileTracking.database;
      (
        duplicatedJob.destination.options as DestinationOptions
      ).metadataTable = `${fileTrackingDb.database}.${fileTrackingDb.table}`;
    }
  }

  return duplicatedJob;
};
