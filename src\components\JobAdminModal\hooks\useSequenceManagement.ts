import { useState, useCallback } from "react";
import axios, { AxiosError } from "axios";
import { JobDefinition, JobSequence } from "@/lib/jobManager";
import { createNewSequence } from "../utils/formUtils";
import { SequenceForExecution } from "../utils/types";

export const useSequenceManagement = () => {
  const [sequences, setSequences] = useState<JobSequence[]>([]);
  const [selectedSequence, setSelectedSequence] = useState<JobSequence | null>(
    null
  );
  const [editedSequence, setEditedSequence] = useState<JobSequence | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeletingSequence, setIsDeletingSequence] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Sequence execution state
  const [selectedSequenceForExecution, setSelectedSequenceForExecution] =
    useState<SequenceForExecution | null>(null);
  const [isSequenceConfirmModalOpen, setIsSequenceConfirmModalOpen] =
    useState(false);

  const fetchSequences = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await axios.get("/api/admin/sequences");
      setSequences(response.data.sequences);
    } catch (error) {
      console.error("Error fetching sequences:", error);
      setError("Failed to fetch sequences");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleSequenceSelect = (sequence: JobSequence) => {
    setSelectedSequence(sequence);
    setEditedSequence({ ...sequence });
  };

  const handleCreateNewSequence = () => {
    const newSequence = createNewSequence();
    setEditedSequence(newSequence);
    setSelectedSequence(null);
  };

  const handleSequenceSave = async (
    isCreate: boolean,
    fetchJobs?: () => Promise<void>
  ) => {
    if (!editedSequence) return;

    setIsSaving(true);
    setError(null);
    try {
      if (isCreate) {
        // Create new sequence
        await axios.post("/api/admin/sequences", { sequence: editedSequence });
        setSequences([...sequences, editedSequence]);
      } else {
        // Update existing sequence
        await axios.put(`/api/admin/sequences/${editedSequence.id}`, {
          sequence: editedSequence,
        });
        setSequences(
          sequences.map((seq) =>
            seq.id === editedSequence.id ? editedSequence : seq
          )
        );
      }

      setSelectedSequence(editedSequence);
      setError(null);

      // Refresh jobs data to reflect updated sequence assignments
      if (fetchJobs) {
        await fetchJobs();
      }

      return true;
    } catch (error: unknown) {
      console.error("Error saving sequence:", error);
      const axiosError = error as AxiosError;
      setError(
        (axiosError.response?.data as { error?: string })?.error ||
          "Failed to save sequence. Please try again."
      );
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  const handleSequenceDelete = async () => {
    if (!selectedSequence) return;

    setIsDeletingSequence(true);
    setError(null);
    try {
      await axios.delete(`/api/admin/sequences/${selectedSequence.id}`);
      setSequences(sequences.filter((seq) => seq.id !== selectedSequence.id));
      setSelectedSequence(null);
      setEditedSequence(null);
      return true;
    } catch (error: unknown) {
      console.error("Error deleting sequence:", error);
      const axiosError = error as AxiosError;
      setError(
        (axiosError.response?.data as { error?: string })?.error ||
          "Failed to delete sequence. Please try again."
      );
      return false;
    } finally {
      setIsDeletingSequence(false);
    }
  };

  const handleSequenceExecution = (
    sequenceId: string,
    jobs: JobDefinition[]
  ) => {
    const sequence = sequences.find((seq) => seq.id === sequenceId);
    if (!sequence) return;

    const sequenceJobs = jobs.filter(
      (job) => job.sequenceConfig?.sequenceId === sequenceId
    );
    setSelectedSequenceForExecution({
      id: sequenceId,
      name: sequence.name,
      description: sequence.description,
      jobCount: sequenceJobs.length,
    });
    setIsSequenceConfirmModalOpen(true);
  };

  const executeSequence = async () => {
    if (!selectedSequenceForExecution) return;

    try {
      setIsLoading(true);
      const response = await axios.post(
        `/api/admin/sequences/${selectedSequenceForExecution.id}/execute`
      );

      if (response.data.executionId) {
        console.log(
          `Sequence ${selectedSequenceForExecution.id} execution started successfully. Execution ID: ${response.data.executionId}`
        );
        setError(null);
        return true;
      }
    } catch (error) {
      console.error("Error executing sequence:", error);
      const axiosError = error as AxiosError;
      setError(
        (axiosError.response?.data as { error?: string })?.error ||
          "Failed to execute sequence. Please try again."
      );
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const clearSequenceSelection = () => {
    setSelectedSequence(null);
    setEditedSequence(null);
  };

  return {
    // State
    sequences,
    selectedSequence,
    editedSequence,
    isLoading,
    isSaving,
    isDeletingSequence,
    error,
    selectedSequenceForExecution,
    isSequenceConfirmModalOpen,

    // Setters
    setSequences,
    setSelectedSequence,
    setEditedSequence,
    setError,
    setSelectedSequenceForExecution,
    setIsSequenceConfirmModalOpen,

    // Actions
    fetchSequences,
    handleSequenceSelect,
    handleCreateNewSequence,
    handleSequenceSave,
    handleSequenceDelete,
    handleSequenceExecution,
    executeSequence,
    clearSequenceSelection,
  };
};
