/**
 * Job Sequencing Feature Test Example
 * 
 * This file demonstrates how to test the job sequencing functionality.
 * Run this after setting up the database and starting the application.
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api/admin`;

// Test data
const testSequence = {
  id: 'test-sequence-1',
  name: 'Test ETL Sequence',
  description: 'A test sequence for demonstrating job sequencing',
  schedule: '0 */6 * * *', // Every 6 hours
  enabled: true,
  onFailure: 'stop',
  maxRetries: 2,
  jobs: [] // Will be populated with test jobs
};

const testJobs = [
  {
    id: 'test-extract-job',
    name: 'Test Extract Job',
    description: 'Extract test data',
    schedule: '0 1 * * *', // This will be ignored when in sequence
    enabled: true,
    dataSource: {
      type: 'database_admin',
      database_admin: {
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_pass',
        operationMode: 'data_extraction',
        operations: [{
          type: 'DATA_EXTRACTION',
          query: 'SELECT * FROM source_table LIMIT 10'
        }]
      }
    },
    destination: {
      type: 'database',
      database: {
        type: 'mysql',
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_pass',
        table: 'extracted_data'
      }
    },
    retryConfig: {
      maxRetries: 3,
      retryDelay: 30
    }
  },
  {
    id: 'test-transform-job',
    name: 'Test Transform Job',
    description: 'Transform extracted data',
    schedule: '0 2 * * *',
    enabled: true,
    dataSource: {
      type: 'database_admin',
      database_admin: {
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_pass',
        operationMode: 'data_extraction',
        operations: [{
          type: 'DATA_EXTRACTION',
          query: 'SELECT *, UPPER(name) as name_upper FROM extracted_data'
        }]
      }
    },
    destination: {
      type: 'database',
      database: {
        type: 'mysql',
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_pass',
        table: 'transformed_data'
      }
    },
    retryConfig: {
      maxRetries: 3,
      retryDelay: 30
    }
  },
  {
    id: 'test-load-job',
    name: 'Test Load Job',
    description: 'Load transformed data to final destination',
    schedule: '0 3 * * *',
    enabled: true,
    dataSource: {
      type: 'database_admin',
      database_admin: {
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_pass',
        operationMode: 'data_extraction',
        operations: [{
          type: 'DATA_EXTRACTION',
          query: 'SELECT * FROM transformed_data'
        }]
      }
    },
    destination: {
      type: 'database',
      database: {
        type: 'mysql',
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_pass',
        table: 'final_data'
      }
    },
    retryConfig: {
      maxRetries: 3,
      retryDelay: 30
    }
  }
];

async function runTests() {
  console.log('🚀 Starting Job Sequencing Tests...\n');

  try {
    // Step 1: Create test jobs
    console.log('📝 Step 1: Creating test jobs...');
    for (const job of testJobs) {
      try {
        await axios.post(`${API_BASE}/jobs`, job);
        console.log(`✅ Created job: ${job.name}`);
      } catch (error) {
        if (error.response?.status === 409) {
          console.log(`⚠️  Job already exists: ${job.name}`);
        } else {
          throw error;
        }
      }
    }

    // Step 2: Create test sequence
    console.log('\n📝 Step 2: Creating test sequence...');
    testSequence.jobs = testJobs.map(job => job.id);
    
    try {
      await axios.post(`${API_BASE}/sequences`, { sequence: testSequence });
      console.log(`✅ Created sequence: ${testSequence.name}`);
    } catch (error) {
      if (error.response?.status === 409) {
        console.log(`⚠️  Sequence already exists: ${testSequence.name}`);
        // Update existing sequence
        await axios.put(`${API_BASE}/sequences`, { sequence: testSequence });
        console.log(`✅ Updated sequence: ${testSequence.name}`);
      } else {
        throw error;
      }
    }

    // Step 3: Assign jobs to sequence
    console.log('\n📝 Step 3: Assigning jobs to sequence...');
    for (let i = 0; i < testJobs.length; i++) {
      const job = testJobs[i];
      await axios.put(`${API_BASE}/jobs/${job.id}/sequence`, {
        sequenceId: testSequence.id,
        order: i + 1
      });
      console.log(`✅ Assigned ${job.name} to sequence at position ${i + 1}`);
    }

    // Step 4: Get sequence status
    console.log('\n📝 Step 4: Checking sequence status...');
    const statusResponse = await axios.get(`${API_BASE}/sequences/${testSequence.id}/status`);
    const status = statusResponse.data.status;
    
    console.log(`✅ Sequence Status:`);
    console.log(`   Name: ${status.sequenceName}`);
    console.log(`   Enabled: ${status.enabled}`);
    console.log(`   Total Jobs: ${status.totalJobs}`);
    console.log(`   Scheduled: ${status.isScheduled}`);
    console.log(`   Running: ${status.isRunning}`);

    // Step 5: Execute sequence manually (optional - uncomment to test)
    /*
    console.log('\n📝 Step 5: Executing sequence manually...');
    const executeResponse = await axios.post(`${API_BASE}/sequences/${testSequence.id}/execute`);
    console.log(`✅ Sequence execution started: ${executeResponse.data.executionId}`);
    
    // Monitor execution
    console.log('\n📝 Monitoring execution...');
    let isRunning = true;
    while (isRunning) {
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      
      const currentStatus = await axios.get(`${API_BASE}/sequences/${testSequence.id}/status`);
      const current = currentStatus.data.status;
      
      if (current.isRunning && current.currentExecution) {
        console.log(`🔄 Running: Job ${current.currentExecution.currentJobOrder}/${current.totalJobs} - ${current.currentExecution.currentJobId}`);
      } else {
        isRunning = false;
        console.log(`✅ Sequence completed`);
      }
    }
    */

    // Step 6: List all sequences
    console.log('\n📝 Step 6: Listing all sequences...');
    const sequencesResponse = await axios.get(`${API_BASE}/sequences`);
    const sequences = sequencesResponse.data.sequences;
    
    console.log(`✅ Found ${sequences.length} sequences:`);
    sequences.forEach(seq => {
      console.log(`   - ${seq.name} (${seq.jobs.length} jobs, ${seq.enabled ? 'enabled' : 'disabled'})`);
    });

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Open the Job Administration UI');
    console.log('   2. Click "Sequences" to view the test sequence');
    console.log('   3. Manually execute the sequence to see it in action');
    console.log('   4. Monitor the execution progress');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

async function cleanup() {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    // Remove jobs from sequence
    for (const job of testJobs) {
      try {
        await axios.delete(`${API_BASE}/jobs/${job.id}/sequence`);
        console.log(`✅ Removed ${job.name} from sequence`);
      } catch (error) {
        console.log(`⚠️  Could not remove ${job.name} from sequence`);
      }
    }

    // Delete sequence
    try {
      await axios.delete(`${API_BASE}/sequences/${testSequence.id}`);
      console.log(`✅ Deleted sequence: ${testSequence.name}`);
    } catch (error) {
      console.log(`⚠️  Could not delete sequence: ${testSequence.name}`);
    }

    // Delete jobs
    for (const job of testJobs) {
      try {
        await axios.delete(`${API_BASE}/jobs/${job.id}`);
        console.log(`✅ Deleted job: ${job.name}`);
      } catch (error) {
        console.log(`⚠️  Could not delete job: ${job.name}`);
      }
    }

    console.log('✅ Cleanup completed');
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--cleanup')) {
  cleanup();
} else {
  runTests();
}

// Export for use in other test files
module.exports = {
  testSequence,
  testJobs,
  runTests,
  cleanup
};
