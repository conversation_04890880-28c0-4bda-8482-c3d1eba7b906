import { NextRequest, NextResponse } from "next/server";
import {
  loadJobSequence,
  saveJobSequence,
  deleteJobSequence,
  getLatestSequenceExecution,
  loadSequenceExecutions,
  updateJobSequenceAssignment,
} from "@/lib/sequencePersistence";
import { logger } from "@/lib/jobManager";
import { cronScheduler } from "@/lib/cronScheduler";
import JobSequenceManager from "@/lib/jobSequenceManager";

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// Helper function to update job assignments for a sequence
async function updateSequenceJobAssignments(
  sequenceId: string,
  jobIds: string[]
): Promise<void> {
  try {
    // Import database functions
    const { executeUpdate } = await import("@/lib/database");

    // First, remove all existing assignments for this sequence
    await executeUpdate(
      "UPDATE job_definitions SET sequence_id = NULL, sequence_order = NULL WHERE sequence_id = ?",
      [sequenceId]
    );
    logger.info(`Cleared existing job assignments for sequence ${sequenceId}`);

    // Then assign the new jobs in order
    for (let i = 0; i < jobIds.length; i++) {
      const jobId = jobIds[i];
      const order = i + 1;
      await updateJobSequenceAssignment(jobId, sequenceId, order);
      logger.info(
        `Assigned job ${jobId} to sequence ${sequenceId} at order ${order}`
      );
    }
  } catch (error) {
    logger.error(
      `Failed to update job assignments for sequence ${sequenceId}:`,
      error
    );
    throw error;
  }
}

// GET /api/admin/sequences/[id] - Get specific sequence with execution history
export async function GET(request: NextRequest, { params }: RouteParams) {
  const { id: sequenceId } = await params;
  try {
    // Load sequence definition
    const sequence = await loadJobSequence(sequenceId);
    if (!sequence) {
      return NextResponse.json(
        { error: `Sequence not found: ${sequenceId}` },
        { status: 404 }
      );
    }

    // Load latest execution
    const latestExecution = await getLatestSequenceExecution(sequenceId);

    // Load recent executions
    const executions = await loadSequenceExecutions(sequenceId, 10);

    // Get current status from sequence manager
    const sequenceManager = JobSequenceManager.getInstance();
    const currentStatus = sequenceManager.getSequenceStatus(sequenceId);

    return NextResponse.json({
      sequence,
      latestExecution,
      executions,
      isRunning: sequenceManager.isSequenceRunning(sequenceId),
      isScheduled: cronScheduler.isSequenceScheduled(sequenceId),
      currentStatus,
    });
  } catch (error) {
    logger.error(`Failed to get sequence ${sequenceId}:`, error);
    return NextResponse.json(
      { error: "Failed to retrieve sequence" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/sequences/[id] - Update specific sequence
export async function PUT(request: NextRequest, { params }: RouteParams) {
  const { id: sequenceId } = await params;
  try {
    const body = await request.json();
    const sequence = body.sequence;

    if (!sequence) {
      return NextResponse.json(
        { error: "Sequence data is required" },
        { status: 400 }
      );
    }

    // Ensure the sequence ID matches the URL parameter
    if (sequence.id !== sequenceId) {
      return NextResponse.json(
        { error: "Sequence ID mismatch" },
        { status: 400 }
      );
    }

    // Check if sequence exists
    const existingSequence = await loadJobSequence(sequenceId);
    if (!existingSequence) {
      return NextResponse.json(
        { error: `Sequence not found: ${sequenceId}` },
        { status: 404 }
      );
    }

    // Save updated sequence
    await saveJobSequence(sequence);

    // Update job assignments
    await updateSequenceJobAssignments(sequenceId, sequence.jobs || []);

    // Update scheduling
    if (sequence.enabled && sequence.schedule) {
      cronScheduler.rescheduleSequence(
        sequenceId,
        sequence.schedule,
        sequence.name
      );
    } else {
      cronScheduler.unscheduleSequence(sequenceId);
    }

    logger.info(`Sequence updated: ${sequenceId} - ${sequence.name}`);

    // Broadcast both job and sequence updates to all connected clients
    try {
      const { broadcastAllUpdates } = await import("@/lib/sseManager");
      await broadcastAllUpdates();
    } catch (error) {
      logger.debug("Could not broadcast updates after sequence change", {
        error,
      });
    }

    return NextResponse.json({
      message: "Sequence updated successfully",
      sequence,
    });
  } catch (error) {
    logger.error(`Failed to update sequence ${sequenceId}:`, error);
    return NextResponse.json(
      { error: "Failed to update sequence" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/sequences/[id] - Delete specific sequence
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  const { id: sequenceId } = await params;
  try {
    // Check if sequence exists
    const existingSequence = await loadJobSequence(sequenceId);
    if (!existingSequence) {
      return NextResponse.json(
        { error: `Sequence not found: ${sequenceId}` },
        { status: 404 }
      );
    }

    // Check if sequence is currently running
    const sequenceManager = JobSequenceManager.getInstance();
    if (sequenceManager.isSequenceRunning(sequenceId)) {
      return NextResponse.json(
        { error: "Cannot delete a running sequence. Stop it first." },
        { status: 409 }
      );
    }

    // Unschedule sequence
    cronScheduler.unscheduleSequence(sequenceId);

    // Delete sequence
    await deleteJobSequence(sequenceId);

    logger.info(`Sequence deleted: ${sequenceId}`);

    // Broadcast both job and sequence updates to all connected clients
    try {
      const { broadcastAllUpdates } = await import("@/lib/sseManager");
      await broadcastAllUpdates();
    } catch (error) {
      logger.debug("Could not broadcast updates after sequence deletion", {
        error,
      });
    }

    return NextResponse.json({
      message: "Sequence deleted successfully",
    });
  } catch (error) {
    logger.error(`Failed to delete sequence ${sequenceId}:`, error);
    return NextResponse.json(
      { error: "Failed to delete sequence" },
      { status: 500 }
    );
  }
}
