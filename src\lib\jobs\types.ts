import type { JobDefinition } from "../jobManager";

/**
 * Standard result interface for all job handlers
 */
export interface JobResult {
  recordsProcessed: number;
  data?: unknown;
  metadata?: Record<string, unknown>;
}

/**
 * Job execution context containing shared state and utilities
 */
export interface JobExecutionContext {
  jobId: string;
  executionId: string;
  jobDefinition: JobDefinition;

  // Utility functions
  addLog: (message: string) => Promise<void>;
  checkCancellation: () => Promise<void>;
  addProgressLog: (
    current: number,
    total: number,
    operation: string
  ) => Promise<void>;
  executeWithCancellationCheck: <T>(
    operation: () => Promise<T>,
    operationName: string
  ) => Promise<T>;

  // Cancellation state
  isCancelled: () => boolean;
}

/**
 * Base interface for all job handlers
 */
export interface JobHandler {
  /**
   * The job type this handler supports
   */
  readonly jobType: string;

  /**
   * Execute the job with the given context
   */
  execute(context: JobExecutionContext): Promise<JobResult>;

  /**
   * Validate the job configuration for this handler
   */
  validateConfig(jobDef: JobDefinition): boolean;

  /**
   * Get required permissions for this job type (optional)
   */
  getRequiredPermissions?(): string[];
}

/**
 * Job handler registry type
 */
export type JobHandlerRegistry = Map<string, new () => JobHandler>;

/**
 * Job handler factory interface
 */
export interface JobHandlerFactory {
  /**
   * Get a handler for the specified job type
   */
  getHandler(jobType: string): JobHandler;

  /**
   * Register a new job handler
   */
  registerHandler(jobType: string, handlerClass: new () => JobHandler): void;

  /**
   * Get all supported job types
   */
  getSupportedJobTypes(): string[];
}

/**
 * Database operation result (used by database-related handlers)
 */
export interface DatabaseOperationResult {
  operationId?: string;
  operationName?: string;
  type?: string;
  operationType?: string;
  success: boolean;
  result?: unknown;
  executionTimeMs?: number;
  sqlAnalysis?: {
    type: string;
    tableName?: string;
    operation: string;
    details: string;
  };
  error?: string;
  statementIndex?: number;
  sql?: string;
}

/**
 * File processing result (used by file-related handlers)
 */
export interface FileProcessingResult {
  name: string;
  size: number;
  lastModified: Date;
  remotePath?: string;
  localPath?: string;
  status: "processed" | "skipped" | "error";
  error?: string;
}

/**
 * Bulk operation result (used by handlers that process multiple items)
 */
export interface BulkOperationResult extends JobResult {
  successCount: number;
  errorCount: number;
  skippedCount: number;
  errors: Array<{
    item: string;
    error: string;
  }>;
}
