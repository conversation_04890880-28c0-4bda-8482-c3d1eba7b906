# Database Administration Workflow Guide

This guide explains how to use the comprehensive database management workflow within the job system for performing DDL operations, complex JOIN operations, and database administration tasks.

## Overview

The Database Administration feature provides a specialized job type that allows you to:

- Perform DDL operations (CREATE, DROP, ALTER tables)
- Execute complex JOIN operations between tables
- Chain multiple SQL operations within a single job execution
- Use visual query builder for complex queries
- Schedule database maintenance operations

## Key Features

### 1. **Enhanced Job Flow for Database Operations**
- Specialized `database_admin` job type
- Support for multi-step database operations with dependencies
- Automatic operation ordering based on dependencies
- Error handling with continue-on-error options

### 2. **Multiple Operation Modes**
- **Raw SQL**: Direct SQL command execution
- **Visual Query Builder**: Drag-and-drop interface for building queries
- **Multi-Step Operations**: Define complex workflows with dependencies

### 3. **Advanced Capabilities**
- CREATE TABLE AS SELECT operations with complex JOINs
- Table existence checking and conditional DROP operations
- Same database as both source and destination
- Real-time SQL syntax validation
- Table schema browsing and column inspection

## Getting Started

### Creating a Database Administration Job

1. **Open Job Creation Modal**
   - Click "Create New Job" in the job management interface
   - The system will default to Database Administration mode

2. **Configure Basic Settings**
   - **Job Name**: `Database Maintenance - TEST_DROP_CREATE`
   - **Description**: `Recreate summary table with department data`
   - **Schedule**: `0 3 * * *` (Daily at 3 AM)
   - **Enabled**: `true`

3. **Configure Database Connection**
   - **Data Source Type**: Select "Database Administration"
   - **Host**: Your MySQL server (e.g., `localhost`)
   - **Port**: `3306`
   - **Database**: `monev2025`
   - **Username**: Database user with DDL privileges
   - **Password**: User password
   - **Connection Limit**: `10`
   - **Acquire Timeout**: `60000ms`
   - **Query Timeout**: `120000ms` (2 minutes for DDL operations)

## Operation Modes

### 1. Raw SQL Mode

Perfect for your existing DROP/CREATE table procedure:

```sql
-- Drop existing table if it exists
DROP TABLE IF EXISTS TEST_DROP_CREATE;

-- Create new table with aggregated data
CREATE TABLE TEST_DROP_CREATE AS
SELECT 
    a.kddept,
    b.nmdept,
    ROUND(SUM(a.pagu)/1, 0) AS PAGU_DIPA,
    ROUND(SUM(real1 + real2 + real3 + real4 + real5 + real6 + real7 + real8 + real9 + real10 + real11 + real12) / 1, 0) AS REALISASI,
    ROUND(SUM(a.blokir) / 1, 0) AS BLOKIR
FROM 
    pagu_real_detail_harian_2025 a
LEFT JOIN 
    t_dept_2025 b ON a.kddept = b.kddept
GROUP BY 
    a.kddept;
```

**Features:**
- Multiple statements separated by semicolons
- Full DDL support (CREATE, DROP, ALTER, etc.)
- Variable support with SET statements
- Prepared statement execution
- Detailed logging for each statement

### 2. Visual Query Builder Mode

Build complex queries using a visual interface:

**Table Selection:**
- Browse available tables from dropdown
- Add multiple source tables
- Visual table relationship mapping

**JOIN Configuration:**
- Define relationships between tables
- Support for INNER, LEFT, RIGHT, FULL JOINs
- Visual connection indicators
- Automatic alias generation

**Column Selection:**
- Choose specific columns from each table
- Apply aggregation functions (SUM, COUNT, AVG, etc.)
- Custom expressions with ROUND, CONCAT, etc.
- Column aliases for clean output

**Conditions & Filtering:**
- WHERE clause builder
- Multiple condition support
- Logical operators (AND, OR)
- Comparison operators (=, !=, >, <, LIKE, IN)

**Grouping & Sorting:**
- GROUP BY clause configuration
- ORDER BY with ASC/DESC options
- Multiple column grouping

### 3. Multi-Step Operations Mode

Define complex workflows with dependencies:

```json
{
  "operations": [
    {
      "id": "backup_table",
      "type": "CREATE_TABLE_AS_SELECT",
      "name": "Backup Existing Table",
      "sql": "CREATE TABLE TEST_DROP_CREATE_BACKUP AS SELECT * FROM TEST_DROP_CREATE",
      "continueOnError": true
    },
    {
      "id": "drop_table",
      "type": "DROP_TABLE", 
      "name": "Drop Existing Table",
      "sql": "DROP TABLE IF EXISTS TEST_DROP_CREATE",
      "dependsOn": ["backup_table"]
    },
    {
      "id": "create_table",
      "type": "CREATE_TABLE_AS_SELECT",
      "name": "Create New Table",
      "sql": "CREATE TABLE TEST_DROP_CREATE AS SELECT ...",
      "dependsOn": ["drop_table"]
    }
  ]
}
```

## Advanced Features

### Database Schema Browser

- **Table Discovery**: Automatically discover all tables and views
- **Column Inspection**: View column types, constraints, and relationships
- **Search & Filter**: Find tables by name or type
- **Relationship Mapping**: Understand foreign key relationships

### SQL Validation & Preview

- **Real-time Validation**: Check SQL syntax as you type
- **Query Preview**: See generated SQL before execution
- **Execution Plan**: Understand query performance impact
- **Copy to Clipboard**: Export generated SQL

### Execution Monitoring

- **Step-by-Step Logging**: Detailed logs for each operation
- **Progress Tracking**: Real-time execution status
- **Error Handling**: Graceful error recovery
- **Result Capture**: Store execution results and metadata

## Use Case Examples

### 1. Daily Summary Table Recreation

**Scenario**: Recreate `TEST_DROP_CREATE` table daily with fresh data

**Configuration**:
- **Schedule**: `0 3 * * *` (3 AM daily)
- **Mode**: Raw SQL
- **Operations**: DROP → CREATE AS SELECT with JOINs
- **Destination**: Local file for execution logs

### 2. Data Warehouse ETL

**Scenario**: Extract, transform, and load data between multiple tables

**Configuration**:
- **Mode**: Multi-Step Operations
- **Steps**: 
  1. Validate source data
  2. Transform with complex JOINs
  3. Load into target tables
  4. Update metadata tables

### 3. Database Maintenance

**Scenario**: Regular database cleanup and optimization

**Configuration**:
- **Mode**: Multi-Step Operations
- **Steps**:
  1. Archive old data
  2. Drop temporary tables
  3. Rebuild indexes
  4. Update statistics

## Best Practices

### Security
- **Principle of Least Privilege**: Use database users with minimal required permissions
- **Environment Variables**: Store credentials in environment variables
- **Connection Limits**: Set appropriate connection pool limits
- **Timeout Configuration**: Set reasonable timeouts for DDL operations

### Performance
- **Off-Peak Scheduling**: Schedule heavy operations during low-usage periods
- **Batch Processing**: Group related operations together
- **Index Management**: Consider index impact during table recreation
- **Resource Monitoring**: Monitor database performance during execution

### Reliability
- **Backup Strategy**: Always backup before destructive operations
- **Error Handling**: Configure appropriate continue-on-error settings
- **Dependency Management**: Define clear operation dependencies
- **Testing**: Test operations on development databases first

### Monitoring
- **Execution Logs**: Review detailed execution logs
- **Performance Metrics**: Monitor execution times and resource usage
- **Alert Configuration**: Set up alerts for failed operations
- **Result Validation**: Verify operation results

## Troubleshooting

### Common Issues

1. **Permission Denied**
   - Ensure database user has CREATE/DROP privileges
   - Check schema-level permissions
   - Verify connection credentials

2. **Table Lock Errors**
   - Schedule operations during low-usage periods
   - Check for long-running transactions
   - Consider using LOCK TABLES statements

3. **Timeout Errors**
   - Increase query timeout settings
   - Optimize complex queries
   - Consider breaking large operations into smaller steps

4. **Dependency Errors**
   - Review operation dependencies
   - Check for circular dependencies
   - Verify operation ordering

### Debug Steps

1. **Test Connection**: Verify database connectivity
2. **Validate SQL**: Check SQL syntax manually
3. **Review Logs**: Examine detailed execution logs
4. **Check Permissions**: Verify user privileges
5. **Monitor Resources**: Check database performance metrics

## Migration from Simple MySQL Jobs

If you have existing MySQL jobs that perform DDL operations:

1. **Create New Database Admin Job**
2. **Copy SQL Commands**: Move SQL from MySQL job to Raw SQL mode
3. **Update Configuration**: Set appropriate timeouts and connection settings
4. **Test Execution**: Run manual test before scheduling
5. **Update Monitoring**: Adjust log monitoring for new job type

The Database Administration workflow provides a powerful, user-friendly interface for complex database operations while maintaining the robustness and reliability of the existing job execution framework.
