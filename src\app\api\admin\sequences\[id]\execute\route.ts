import { NextRequest, NextResponse } from "next/server";
import { loadJobSequence } from "@/lib/sequencePersistence";
import { logger } from "@/lib/jobManager";
import JobSequenceManager from "@/lib/jobSequenceManager";

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// POST /api/admin/sequences/[id]/execute - Execute a sequence manually
export async function POST(request: NextRequest, { params }: RouteParams) {
  const { id: sequenceId } = await params;
  try {
    // Check if sequence exists
    const sequence = await loadJobSequence(sequenceId);
    if (!sequence) {
      return NextResponse.json(
        { error: `Sequence not found: ${sequenceId}` },
        { status: 404 }
      );
    }

    if (!sequence.enabled) {
      return NextResponse.json(
        { error: `Sequence ${sequenceId} is disabled` },
        { status: 400 }
      );
    }

    // Check if sequence is already running
    const sequenceManager = JobSequenceManager.getInstance();
    if (sequenceManager.isSequenceRunning(sequenceId)) {
      return NextResponse.json(
        { error: `Sequence ${sequenceId} is already running` },
        { status: 409 }
      );
    }

    // Execute the sequence
    const executionId = await sequenceManager.executeSequence(
      sequenceId,
      "manual"
    );

    logger.info(`Manual sequence execution started: ${sequenceId}`, {
      sequenceId,
      executionId,
    });

    return NextResponse.json({
      message: "Sequence execution started",
      sequenceId,
      executionId,
    });
  } catch (error) {
    logger.error(`Failed to execute sequence ${sequenceId}:`, error);
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to execute sequence",
      },
      { status: 500 }
    );
  }
}
