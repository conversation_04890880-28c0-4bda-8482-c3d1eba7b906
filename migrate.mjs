#!/usr/bin/env node

/**
 * Database Migration Script for Sintesa Data Puller
 *
 * This script will create the necessary database tables and initialize
 * default settings for the application.
 *
 * Usage:
 *   node migrate.js
 *
 * Environment Variables:
 *   DB_HOST - Database host (default: localhost)
 *   DB_PORT - Database port (default: 3399)
 *   DB_USER - Database user (default: root)
 *   DB_PASSWORD - Database password (default: '')
 *   DB_NAME - Database name (default: pulltest)
 */

import mysql from "mysql2/promise";
import path from "path";
import { fileURLToPath } from "url";
import { dirname } from "path";
import dotenv from "dotenv";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config({ path: path.join(__dirname, ".env.local") });

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3399"),
  user: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "",
  database: process.env.DB_NAME || "pulltest",
};

console.log("🚀 Starting database migration for Sintesa Data Puller");
console.log(
  `📍 Connecting to: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`
);

async function runMigration() {
  let connection;

  try {
    // Connect to database
    connection = await mysql.createConnection(dbConfig);
    console.log("✅ Database connection established");

    // Create tables
    await createTables(connection);

    // Initialize default settings
    await initializeDefaultSettings(connection);

    // Initialize default job definitions
    await initializeDefaultJobs(connection);

    console.log("🎉 Migration completed successfully!");
    console.log("");
    console.log("📋 Summary:");
    console.log(
      "   - Created 7 database tables (including log_ftp.error_logs)"
    );
    console.log("   - Initialized default system settings");
    console.log(
      "   - Created 6 default job definitions (production-ready configurations):"
    );
    console.log(
      "     • Job 1: Oracle data pulling (Tarik Pagu Real) - 2025 data"
    );
    console.log("     • Job 2: MySQL data extraction (Sample MySQL Query)");
    console.log(
      "     • Job 3: Database Administration (Table Maintenance) - ENABLED"
    );
    console.log(
      "     • Job 4: SFTP bulk download (ADK RKAKL 2025) with file tracking"
    );
    console.log(
      "     • Job 5: PDF DIPA Extraction (2025 budget information from PDF files)"
    );
    console.log(
      "     • Job 6: ADK Processing (2025 XML extraction from archives)"
    );
    console.log("");
    console.log("⚠️  Job Configuration:");
    console.log("   - Jobs use current production configurations as defaults");
    console.log(
      "   - Most jobs are disabled by default for safety (except maintenance)"
    );
    console.log("   - All configurations updated to 2025 data sources");
    console.log(
      "   - Jobs are scheduled at different times to avoid conflicts"
    );
    console.log("");
    console.log("🔧 File Tracking:");
    console.log("   - Job 4 configured with monev2025.file_metadata tracking");
    console.log("   - File tracking is fully configurable per job");
    console.log("   - All jobs updated to use 2025 databases and paths");
    console.log("");
    console.log("🔗 You can now start your application:");
    console.log("   npm run dev");
  } catch (error) {
    console.error("❌ Migration failed:", error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log("📴 Database connection closed");
    }
  }
}

async function createTables(connection) {
  console.log("📊 Creating database tables...");

  // App configuration table
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS app_config (
      id INT PRIMARY KEY AUTO_INCREMENT,
      config_key VARCHAR(255) UNIQUE NOT NULL,
      config_value TEXT,
      description TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_config_key (config_key)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `);
  console.log("   ✓ app_config table created");

  // Job definitions table
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS job_definitions (
      id VARCHAR(36) PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      schedule_cron VARCHAR(100) NOT NULL,
      enabled BOOLEAN DEFAULT TRUE,
      data_source_type ENUM('oracle', 'sftp', 'mysql', 'database_admin', 'pdf_dipa', 'adk_processing') NOT NULL,
      data_source_config JSON,
      destination_config JSON,
      retry_config JSON,
      sequence_id VARCHAR(36) NULL,
      sequence_order INT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_job_enabled (enabled),
      INDEX idx_job_type (data_source_type),
      INDEX idx_job_sequence (sequence_id, sequence_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `);
  console.log("   ✓ job_definitions table created");

  // Update the data_source_type ENUM to include all supported types
  try {
    await connection.execute(`
      ALTER TABLE job_definitions
      MODIFY COLUMN data_source_type ENUM('oracle', 'sftp', 'mysql', 'database_admin', 'pdf_dipa', 'adk_processing') NOT NULL
    `);
    console.log("   ✓ job_definitions data_source_type ENUM updated");
  } catch (error) {
    // If the ENUM already has all values, this might fail, which is okay
    console.log(
      "   ✓ job_definitions data_source_type ENUM (already up to date)"
    );
  }

  // Job executions table
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS job_executions (
      id VARCHAR(100) PRIMARY KEY,
      job_id VARCHAR(36) NOT NULL,
      status ENUM('running', 'completed', 'failed', 'scheduled', 'stopped') NOT NULL,
      trigger_type ENUM('manual', 'automatic') NOT NULL,
      start_time TIMESTAMP NOT NULL,
      end_time TIMESTAMP NULL,
      duration_seconds INT NULL,
      records_processed INT NULL,
      error_message TEXT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (job_id) REFERENCES job_definitions(id) ON DELETE CASCADE,
      INDEX idx_job_executions_job_id (job_id),
      INDEX idx_job_executions_status (status),
      INDEX idx_job_executions_start_time (start_time)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `);
  console.log("   ✓ job_executions table created");

  // Job execution logs table
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS job_execution_logs (
      id BIGINT PRIMARY KEY AUTO_INCREMENT,
      execution_id VARCHAR(100) NOT NULL,
      log_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      log_level ENUM('info', 'warn', 'error', 'debug') DEFAULT 'info',
      message TEXT NOT NULL,
      FOREIGN KEY (execution_id) REFERENCES job_executions(id) ON DELETE CASCADE,
      INDEX idx_job_logs_execution_id (execution_id),
      INDEX idx_job_logs_timestamp (log_timestamp),
      INDEX idx_job_logs_level (log_level)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `);
  console.log("   ✓ job_execution_logs table created");

  // Job sequences table
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS job_sequences (
      id VARCHAR(36) PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      schedule_cron VARCHAR(100) NULL,
      enabled BOOLEAN DEFAULT TRUE,
      on_failure ENUM('stop', 'continue', 'retry') DEFAULT 'stop',
      max_retries INT DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_sequence_enabled (enabled),
      INDEX idx_sequence_schedule (schedule_cron)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `);
  console.log("   ✓ job_sequences table created");

  // Job sequence executions table
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS job_sequence_executions (
      id VARCHAR(100) PRIMARY KEY,
      sequence_id VARCHAR(36) NOT NULL,
      status ENUM('running', 'completed', 'failed', 'stopped') NOT NULL,
      current_job_id VARCHAR(36) NULL,
      current_job_order INT NULL,
      start_time TIMESTAMP NOT NULL,
      end_time TIMESTAMP NULL,
      duration_seconds INT NULL,
      trigger_type ENUM('manual', 'automatic') NOT NULL,
      error_message TEXT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (sequence_id) REFERENCES job_sequences(id) ON DELETE CASCADE,
      FOREIGN KEY (current_job_id) REFERENCES job_definitions(id) ON DELETE SET NULL,
      INDEX idx_sequence_executions_sequence_id (sequence_id),
      INDEX idx_sequence_executions_status (status),
      INDEX idx_sequence_executions_start_time (start_time)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `);
  console.log("   ✓ job_sequence_executions table created");

  // Add foreign key constraint for job_definitions.sequence_id
  try {
    await connection.execute(`
      ALTER TABLE job_definitions
      ADD CONSTRAINT fk_job_sequence
      FOREIGN KEY (sequence_id) REFERENCES job_sequences(id) ON DELETE SET NULL
    `);
    console.log("   ✓ job_definitions sequence foreign key added");
  } catch (error) {
    // Ignore error if constraint already exists
    console.log("   ✓ job_definitions sequence foreign key (already exists)");
  }

  // System settings table
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS system_settings (
      id INT PRIMARY KEY AUTO_INCREMENT,
      setting_category VARCHAR(100) NOT NULL,
      setting_key VARCHAR(255) NOT NULL,
      setting_value TEXT,
      setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
      description TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      UNIQUE KEY unique_category_key (setting_category, setting_key),
      INDEX idx_settings_category (setting_category)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `);
  console.log("   ✓ system_settings table created");

  // Job schedule history table
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS job_schedule_history (
      id BIGINT PRIMARY KEY AUTO_INCREMENT,
      job_id VARCHAR(36) NOT NULL,
      scheduled_time TIMESTAMP NOT NULL,
      actual_start_time TIMESTAMP NULL,
      execution_id VARCHAR(100) NULL,
      status ENUM('scheduled', 'started', 'skipped', 'failed_to_start') NOT NULL,
      notes TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (job_id) REFERENCES job_definitions(id) ON DELETE CASCADE,
      FOREIGN KEY (execution_id) REFERENCES job_executions(id) ON DELETE SET NULL,
      INDEX idx_schedule_history_job_id (job_id),
      INDEX idx_schedule_history_scheduled_time (scheduled_time),
      INDEX idx_schedule_history_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `);
  console.log("   ✓ job_schedule_history table created");

  // Create log_ftp database if it doesn't exist
  await connection.execute(`CREATE DATABASE IF NOT EXISTS log_ftp`);
  console.log("   ✓ log_ftp database created");

  // Error logs table for PDF processing and other FTP operations
  await connection.execute(`
    CREATE TABLE IF NOT EXISTS log_ftp.error_logs (
      id BIGINT PRIMARY KEY AUTO_INCREMENT,
      folder VARCHAR(255) NOT NULL,
      nmfile VARCHAR(255) NOT NULL,
      error_message TEXT NOT NULL,
      stack_trace TEXT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_error_logs_folder (folder),
      INDEX idx_error_logs_nmfile (nmfile),
      INDEX idx_error_logs_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `);
  console.log("   ✓ log_ftp.error_logs table created");
}

async function initializeDefaultSettings(connection) {
  console.log("⚙️  Initializing default system settings...");

  const defaultSettings = [
    // Application settings
    [
      "app",
      "max_concurrent_jobs",
      "3",
      "number",
      "Maximum number of jobs that can run concurrently",
    ],
    [
      "app",
      "job_timeout_minutes",
      "60",
      "number",
      "Default timeout for job execution in minutes",
    ],
    [
      "app",
      "enable_auto_cleanup",
      "true",
      "boolean",
      "Enable automatic cleanup of old job data",
    ],
    [
      "app",
      "cleanup_days_to_keep",
      "30",
      "number",
      "Number of days to keep job execution history",
    ],

    // Logging settings
    [
      "logging",
      "log_level",
      "info",
      "string",
      "Default logging level (debug, info, warn, error)",
    ],
    [
      "logging",
      "max_log_files",
      "5",
      "number",
      "Maximum number of log files to keep",
    ],
    [
      "logging",
      "max_log_size_mb",
      "5",
      "number",
      "Maximum size of each log file in MB",
    ],

    // Notification settings
    [
      "notifications",
      "enable_email_alerts",
      "false",
      "boolean",
      "Enable email notifications for job failures",
    ],
    [
      "notifications",
      "email_recipients",
      "[]",
      "json",
      "List of email addresses for notifications",
    ],
    [
      "notifications",
      "slack_webhook_url",
      "",
      "string",
      "Slack webhook URL for notifications",
    ],

    // Database settings
    [
      "database",
      "connection_timeout",
      "60000",
      "number",
      "Database connection timeout in milliseconds",
    ],
    [
      "database",
      "max_connections",
      "10",
      "number",
      "Maximum number of database connections in pool",
    ],
    [
      "database",
      "retry_attempts",
      "3",
      "number",
      "Number of retry attempts for failed database operations",
    ],

    // Scheduler settings
    [
      "scheduler",
      "enable_scheduler",
      "true",
      "boolean",
      "Enable the cron job scheduler",
    ],
    [
      "scheduler",
      "timezone",
      "UTC",
      "string",
      "Timezone for cron job scheduling",
    ],
    [
      "scheduler",
      "max_missed_runs",
      "3",
      "number",
      "Maximum number of missed runs before disabling a job",
    ],
  ];

  for (const [category, key, value, type, description] of defaultSettings) {
    await connection.execute(
      `
      INSERT INTO system_settings (setting_category, setting_key, setting_value, setting_type, description)
      VALUES (?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        setting_value = VALUES(setting_value),
        description = VALUES(description),
        updated_at = CURRENT_TIMESTAMP
    `,
      [category, key, value, type, description]
    );
  }

  console.log(`   ✓ Initialized ${defaultSettings.length} system settings`);
}

async function initializeDefaultJobs(connection) {
  console.log("📋 Initializing default job definitions...");

  // Default job configurations extracted from current database setup
  // These represent the standard job templates for all new installations
  const defaultJobs = [
    {
      id: "1",
      name: "Tarik Pagu Real",
      description: "Pull pagu real data from Oracle PA database",
      schedule: "0 2 * * *",
      enabled: false,
      dataSource: {
        type: "oracle",
        oracle: {
          host: process.env.ORACLE_HOST || "**************",
          port: parseInt(process.env.ORACLE_PORT || "1521"),
          serviceName: process.env.ORACLE_SERVICE_NAME || "olap23",
          username: process.env.ORACLE_USERNAME || "USRPA",
          password: process.env.ORACLE_PASSWORD || "pdpsipa",
          query: "SELECT * FROM USRPA.PA_BELANJA_2025 WHERE ROWNUM <= 5000",
          schema: "USRPA",
        },
      },
      destination: {
        type: "database",
        database: {
          type: "mysql",
          host: process.env.DB_HOST || "localhost",
          port: parseInt(process.env.DB_PORT || "3399"),
          database: process.env.DB_NAME || "pulltest",
          username: process.env.DB_USER || "root",
          password: process.env.DB_PASSWORD || "",
          table: "pagu_real",
        },
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 300,
      },
    },
    {
      id: "2",
      name: "Sample MySQL Query",
      description: "Extract data from MySQL database with configurable query",
      schedule: "0 5 * * *",
      enabled: false,
      dataSource: {
        type: "mysql",
        mysql: {
          host: process.env.MYSQL_HOST || "localhost",
          port: parseInt(process.env.MYSQL_PORT || "3399"),
          database: process.env.MYSQL_DATABASE || "pulltest",
          username: process.env.MYSQL_USERNAME || "root",
          password: process.env.MYSQL_PASSWORD || "",
          query:
            "SELECT * FROM sample_table WHERE created_at >= CURDATE() - INTERVAL 1 DAY LIMIT 1000",
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
        },
      },
      destination: {
        type: "database",
        database: {
          type: "mysql",
          host: process.env.DB_HOST || "localhost",
          port: parseInt(process.env.DB_PORT || "3399"),
          database: process.env.DB_NAME || "pulltest",
          username: process.env.DB_USER || "root",
          password: process.env.DB_PASSWORD || "",
          table: "mysql_extracted_data",
        },
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 300,
      },
    },
    {
      id: "3",
      name: "Database Table Maintenance",
      description:
        "Perform database administration tasks like table optimization and cleanup",
      schedule: "0 6 * * 0",
      enabled: true,
      dataSource: {
        type: "database_admin",
        database_admin: {
          host: process.env.DB_HOST || "localhost",
          port: parseInt(process.env.DB_PORT || "3399"),
          database: process.env.DB_NAME || "pulltest",
          username: process.env.DB_USER || "root",
          password: process.env.DB_PASSWORD || "",
          operations: [
            {
              type: "OPTIMIZE_ANALYZE",
              sql: "OPTIMIZE TABLE job_executions, job_execution_logs",
              description:
                "Optimize job execution tables for better performance",
            },
            {
              type: "DATA_CLEANUP",
              sql: "DELETE FROM job_execution_logs WHERE log_timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY)",
              description:
                "Clean up old job execution logs (older than 30 days)",
            },
            {
              type: "DATA_CLEANUP",
              sql: "DELETE FROM job_executions WHERE start_time < DATE_SUB(NOW(), INTERVAL 90 DAY)",
              description:
                "Clean up old job execution records (older than 90 days)",
            },
          ],
        },
      },
      destination: {
        type: "database",
        database: {
          type: "mysql",
          host: process.env.DB_HOST || "localhost",
          port: parseInt(process.env.DB_PORT || "3399"),
          database: process.env.DB_NAME || "pulltest",
          username: process.env.DB_USER || "root",
          password: process.env.DB_PASSWORD || "",
          table: "maintenance_results",
        },
      },
      retryConfig: {
        maxRetries: 2,
        retryDelay: 600,
      },
    },
    {
      id: "4",
      name: "ADK RKAKL 2025 Bulk Download",
      description:
        "Download ADK RKAKL 2025 files from all organizational units via SFTP and track in database",
      schedule: "0 1 * * *",
      enabled: false,
      dataSource: {
        type: "sftp",
        sftp: {
          host: "aksesdata-anggaran.kemenkeu.go.id",
          port: 54321,
          username: "PA_DJPBN",
          password: "Sinergi100Persen",
          remotePath: "adk_rkakl2025",
          filePattern: "*",
        },
        options: {
          organisationalUnits: [
            "010012024",
            "010022024",
            "010032024",
            "010042024",
            "010052024",
            "020042024",
            "020052024",
            "020062024",
            "020072024",
            "020082024",
            "030012024",
            "030022024",
            "030032024",
            "030042024",
            "030052024",
            "040012024",
            "040022024",
            "040032024",
            "040042024",
            "040052024",
            "050012024",
            "050022024",
            "050032024",
            "050042024",
            "050052024",
          ],
          skipExisting: true,
          createDirectories: true,
          trackMetadata: true,
        },
      },
      destination: {
        type: "local",
        localPath:
          process.env.ADK_BASE_PATH || "C:\\\\KUMPULAN_ADK\\\\ADK_2025_DIPA",
        options: {
          createDirectory: true,
          preserveStructure: true,
          metadataTable: "monev2025.file_metadata",
          errorLogTable: "log_ftp.error_logs",
        },
        fileTracking: {
          enabled: true,
          database: {
            host: process.env.MONEV_DB_HOST || "localhost",
            port: parseInt(process.env.MONEV_DB_PORT || "3399"),
            username: process.env.MONEV_DB_USER || "root",
            password: process.env.MONEV_DB_PASSWORD || "",
            database: process.env.MONEV_DB_NAME || "monev2025",
            table: "file_metadata",
          },
        },
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 900,
      },
    },
    {
      id: "5",
      name: "PDF DIPA Extraction",
      description:
        "Extract budget information from Indonesian government DIPA PDF files",
      schedule: "0 3 * * *",
      enabled: false,
      dataSource: {
        type: "pdf_dipa",
        pdf_dipa: {
          sourceDirectory: "C:\\\\KUMPULAN_ADK\\\\ADK_2025_DIPA",
          fileMetadataDatabase: {
            host: "localhost",
            port: parseInt(process.env.DB_PORT || "3399"),
            database: "monev2025",
            username: "root",
            password: "",
            table: "file_metadata",
          },
          fileStatusFilter: "NEW",
          errorLogTable: "log_ftp.error_logs",
        },
      },
      destination: {
        type: "database",
        database: {
          type: "mysql",
          host: "localhost",
          port: 3306,
          database: "monev2025",
          username: "root",
          password: "",
          table: "ftp_pagu_pdf_2025",
        },
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 300,
      },
    },
    // ADK Processing Job - Extracts XML from compressed archives and processes data
    // Destination: dbdipa25 database (for ADK data tables: d_akun, d_item, etc.)
    // File Tracking: monev2025.file_metadata (for tracking processed files)
    {
      id: "6",
      name: "ADK Processing",
      description:
        "Extract and process XML files from compressed ADK archives with configurable parameters",
      schedule: "0 4 * * *",
      enabled: false,
      dataSource: {
        type: "adk_processing",
        adk_processing: {
          sourceDirectory: "C:\\\\KUMPULAN_ADK\\\\ADK_2025_DIPA",
          extractionPath: "C:\\\\KUMPULAN_ADK\\\\XML",
          rarToolPath: "C:\\\\KUMPULAN_ADK\\\\TOOLS\\\\Rar.exe",
          fileListDatabase: {
            host: "localhost",
            port: parseInt(process.env.DB_PORT || "3399"),
            database: "monev2025",
            username: "root",
            password: "",
            table: "file_metadata",
          },
          fileFilter: {
            startsWith: ["d", "D"],
            excludeExtensions: ["pdf"],
          },
          processingOptions: {
            deleteOldXmlFiles: true,
            continueOnError: true,
            batchSize: 10,
          },
        },
      },
      destination: {
        type: "database",
        database: {
          type: "mysql",
          host: "localhost",
          port: 3306,
          database: "dbdipa25",
          username: "root",
          password: "",
          table: "d_item",
        },
        fileTracking: {
          enabled: true,
          database: {
            host: "localhost",
            port: 3306,
            username: "root",
            password: "",
            database: "monev2025",
            table: "file_metadata",
          },
        },
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 600,
      },
    },
  ];

  for (const job of defaultJobs) {
    await connection.execute(
      `
      INSERT INTO job_definitions (
        id, name, description, schedule_cron, enabled, 
        data_source_type, data_source_config, destination_config, retry_config
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        description = VALUES(description),
        schedule_cron = VALUES(schedule_cron),
        enabled = VALUES(enabled),
        data_source_type = VALUES(data_source_type),
        data_source_config = VALUES(data_source_config),
        destination_config = VALUES(destination_config),
        retry_config = VALUES(retry_config),
        updated_at = CURRENT_TIMESTAMP
    `,
      [
        job.id,
        job.name,
        job.description,
        job.schedule,
        job.enabled,
        job.dataSource.type,
        JSON.stringify(job.dataSource),
        JSON.stringify(job.destination),
        JSON.stringify(job.retryConfig),
      ]
    );
  }

  console.log(
    `   ✓ Initialized ${defaultJobs.length} production-ready job definitions (current configurations as defaults)`
  );
}

// Run the migration
runMigration();
