import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // External packages that should not be bundled (works for both Webpack and Turbopack)
  serverExternalPackages: ["oracledb", "ssh2-sftp-client"],

  // Turbopack configuration for development
  turbopack: {
    // Handle optional dependencies for oracledb in Turbopack
    resolveExtensions: [".ts", ".tsx", ".js", ".jsx"],
  },

  // Webpack configuration (used for production builds only)
  webpack: (config, { isServer, dev }) => {
    // Only apply webpack config for production builds
    if (dev) {
      return config;
    }

    // Ignore optional dependencies that oracledb tries to load
    config.externals = config.externals || [];

    if (isServer) {
      config.externals.push({
        // Azure dependencies (optional for oracledb)
        "@azure/app-configuration": "commonjs @azure/app-configuration",
        "@azure/identity": "commonjs @azure/identity",
        "@azure/keyvault-secrets": "commonjs @azure/keyvault-secrets",

        // OCI dependencies (optional for oracledb)
        "oci-common": "commonjs oci-common",
        "oci-objectstorage": "commonjs oci-objectstorage",

        // Other optional dependencies
        "aws-sdk": "commonjs aws-sdk",
      });
    }

    // Ignore these modules completely if they're not available
    config.resolve.fallback = {
      ...config.resolve.fallback,
      "@azure/app-configuration": false,
      "@azure/identity": false,
      "@azure/keyvault-secrets": false,
      "oci-common": false,
      "oci-objectstorage": false,
      "aws-sdk": false,
    };

    return config;
  },
};

export default nextConfig;
