import { useState, useCallback } from "react";
import axios, { AxiosError } from "axios";
import { addToast } from "@heroui/react";
import { JobDefinition } from "@/lib/jobManager";
import { createNewJob, duplicateJob } from "../utils/formUtils";

export const useJobManagement = () => {
  const [jobs, setJobs] = useState<JobDefinition[]>([]);
  const [selectedJob, setSelectedJob] = useState<JobDefinition | null>(null);
  const [editedJob, setEditedJob] = useState<JobDefinition | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchJobs = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await axios.get("/api/admin/jobs");
      setJobs(response.data.jobs);

      // Show success toast notification
      addToast({
        title: "Jobs Loaded",
        description: `Successfully loaded ${response.data.jobs.length} jobs`,
        color: "success",
        timeout: 3000,
      });
    } catch (error) {
      console.error("Error fetching jobs:", error);
      setError("Failed to fetch jobs");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleJobSelect = (job: JobDefinition) => {
    setSelectedJob(job);
    setEditedJob({ ...job });
  };

  const handleCreateNew = () => {
    const newJob = createNewJob();
    setEditedJob(newJob);
    setSelectedJob(null);
  };

  const handleDuplicate = () => {
    if (!selectedJob) return;
    const duplicatedJob = duplicateJob(selectedJob);
    setEditedJob(duplicatedJob);
    setSelectedJob(null);
  };

  const handleSave = async (isCreate: boolean) => {
    if (!editedJob) return;

    setIsSaving(true);
    setError(null);
    try {
      if (isCreate) {
        // Create new job
        await axios.post("/api/admin/jobs", editedJob);
        setJobs([...jobs, editedJob]);
      } else {
        // Update existing job
        await axios.put(`/api/admin/jobs/${editedJob.id}`, editedJob);
        setJobs(jobs.map((job) => (job.id === editedJob.id ? editedJob : job)));
      }

      setSelectedJob(editedJob);
      setError(null);
      return true;
    } catch (error: unknown) {
      console.error("Error saving job:", error);
      const axiosError = error as AxiosError;
      setError(
        (axiosError.response?.data as { error?: string })?.error ||
          "Failed to save job. Please try again."
      );
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!selectedJob) return;

    setIsDeleting(true);
    setError(null);
    try {
      await axios.delete(`/api/admin/jobs/${selectedJob.id}`);
      setJobs(jobs.filter((job) => job.id !== selectedJob.id));
      setSelectedJob(null);
      setEditedJob(null);
      return true;
    } catch (error: unknown) {
      console.error("Error deleting job:", error);
      const axiosError = error as AxiosError;
      setError(
        (axiosError.response?.data as { error?: string })?.error ||
          "Failed to delete job. Please try again."
      );
      return false;
    } finally {
      setIsDeleting(false);
    }
  };

  const clearSelection = useCallback(() => {
    setSelectedJob(null);
    setEditedJob(null);
  }, []);

  return {
    // State
    jobs,
    selectedJob,
    editedJob,
    isLoading,
    isSaving,
    isDeleting,
    error,

    // Setters
    setJobs,
    setSelectedJob,
    setEditedJob,
    setError,

    // Actions
    fetchJobs,
    handleJobSelect,
    handleCreateNew,
    handleDuplicate,
    handleSave,
    handleDelete,
    clearSelection,
  };
};
