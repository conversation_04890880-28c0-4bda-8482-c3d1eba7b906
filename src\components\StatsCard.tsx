"use client";

// ============================================================================
// IMPORTS
// ============================================================================

// UI library imports
import { Card, CardBody } from "@heroui/react";

// Animation imports
import { motion } from "framer-motion";

// Type imports
import { LucideIcon } from "lucide-react";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface StatsCardProps {
  title: string;
  value: number;
  icon: LucideIcon;
  iconColor?: string;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const StatsCard = ({
  title,
  value,
  icon: Icon,
  iconColor = "text-blue-500",
}: StatsCardProps) => {
  return (
    <motion.div
      whileHover={{
        scale: 1.02,
        transition: { duration: 0.2 },
      }}
      whileTap={{ scale: 0.98 }}
    >
      <Card className="bg-background border-2 border-slate-100 dark:border-slate-600 rounded-2xl transition-all duration-300 group">
        <CardBody className="p-[var(--spacing-lg)]">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-xs font-medium text-muted-foreground mb-1">
                {title}
              </p>
              <motion.p
                key={value}
                initial={{ scale: 1.2, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="text-2xl font-bold text-foreground"
              >
                {value}
              </motion.p>
            </div>
            <div className="ml-[var(--spacing-md)]">
              <div className="w-8 h-8 bg-muted rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Icon className={`w-4 h-4 ${iconColor}`} />
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};
