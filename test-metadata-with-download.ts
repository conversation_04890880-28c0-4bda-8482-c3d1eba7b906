import { performBulkSftpDownload } from "./src/lib/sftpManager";
import { initializeDatabase } from "./src/lib/database";
import { executeQuery } from "./src/lib/database";

interface FileTransferRecord {
  id: number;
  job_id: string;
  remote_file_name: string;
  file_size_bytes: number;
  transfer_status: string;
  created_at: string;
}

async function testMetadataWithActualDownload() {
  try {
    console.log("🔧 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized successfully");

    // Test with a minimal set of organizational units
    const testUnits = ["010012024"]; // Just one unit for testing
    const localBasePath = "C:/KUMPULAN_ADK/ADK_2024_DIPA_TEST";
    const testJobId = "test-metadata";

    console.log("\n🚀 Testing SFTP download with metadata saving...");
    console.log(`Testing with organizational unit: ${testUnits[0]}`);

    try {
      const result = await performBulkSftpDownload(
        testUnits,
        localBasePath,
        testJobId
      );

      console.log("\n📊 Download Result:");
      console.log(`- Status: ${result.status}`);
      console.log(`- Units Processed: ${result.unitsProcessed}`);
      console.log(`- Files Downloaded: ${result.filesDownloaded}`);
      console.log(`- Errors: ${result.errors.length}`);
      if (result.errors.length > 0) {
        console.log("Error details:", result.errors);
      }
    } catch (downloadError) {
      console.log(
        "⚠️ Download failed (expected if SFTP server is unreachable):"
      );
      console.log(
        downloadError instanceof Error
          ? downloadError.message
          : String(downloadError)
      );
    }

    // Check if metadata was saved regardless of download success/failure
    console.log("\n📊 Checking saved metadata...");
    const transfers = await executeQuery<FileTransferRecord>(
      "SELECT id, job_id, remote_file_name, file_size_bytes, transfer_status, created_at FROM sftp_file_transfers WHERE job_id = ? ORDER BY created_at DESC",
      [testJobId]
    );

    console.log(
      `Found ${transfers.length} file transfer records for job ${testJobId}:`
    );
    transfers.forEach((transfer) => {
      console.log(
        `- ID: ${transfer.id}, File: ${transfer.remote_file_name}, Size: ${transfer.file_size_bytes}, Status: ${transfer.transfer_status}`
      );
    });

    // Check error logs
    const errorLogs = await executeQuery(
      "SELECT execution_id, message, log_timestamp FROM job_execution_logs WHERE execution_id LIKE ? ORDER BY log_timestamp DESC",
      [`${testJobId}%`]
    );

    console.log(`\n📝 Found ${errorLogs.length} log entries for this test`);

    if (transfers.length > 0) {
      console.log("\n✅ SUCCESS: Metadata is now being saved correctly!");
    } else {
      console.log(
        "\n⚠️  No metadata found - this could be due to connection issues or no files in the test directory"
      );
    }
  } catch (error) {
    console.error("❌ Error in metadata test:", error);
    if (error instanceof Error) {
      console.error("Stack trace:", error.stack);
    }
  } finally {
    process.exit(0);
  }
}

// Run the test
testMetadataWithActualDownload();
