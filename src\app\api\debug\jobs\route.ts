import { NextResponse } from "next/server";
import { getRunningJobsDebugInfo } from "@/lib/jobRunner";
import JobSequenceManager from "@/lib/jobSequenceManager";
import { isDataBotStarted } from "@/lib/startup";
import { logger } from "@/lib/jobManager";

export async function GET() {
  try {
    // Get debug information from job runner
    const jobRunnerDebug = getRunningJobsDebugInfo();

    // Get debug information from sequence manager
    const sequenceManager = JobSequenceManager.getInstance();
    const sequenceDebug = sequenceManager.getDebugInfo();

    // Get system status
    const systemStarted = isDataBotStarted();

    const debugInfo = {
      system: {
        started: systemStarted,
        timestamp: new Date().toISOString(),
      },
      jobs: jobRunnerDebug,
      sequences: sequenceDebug,
    };

    logger.info("Debug information requested", debugInfo);

    return NextResponse.json(debugInfo);
  } catch (error) {
    logger.error("Failed to get debug information:", error);
    return NextResponse.json(
      { error: "Failed to get debug information" },
      { status: 500 }
    );
  }
}

// POST endpoint to force clear all running states (emergency use)
export async function POST() {
  try {
    logger.warn("Emergency clear of all running states requested");

    // Clear all running job states
    const { clearAllRunningJobs } = await import("@/lib/jobRunner");
    clearAllRunningJobs();

    // Clear all active sequence states
    const sequenceManager = JobSequenceManager.getInstance();
    sequenceManager.clearAllActiveSequences();

    logger.info("Emergency clear completed");

    return NextResponse.json({
      message: "All running states cleared successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Failed to clear running states:", error);
    return NextResponse.json(
      { error: "Failed to clear running states" },
      { status: 500 }
    );
  }
}
