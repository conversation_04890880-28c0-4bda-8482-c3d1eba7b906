const { getJobDefinitions, getJobDefinition } = require('./src/lib/jobManager');
const { jobHandlerFactory } = require('./src/lib/jobs');

async function debugAdkJob() {
  console.log('🔍 Debugging ADK Processing Job Issue...\n');

  try {
    // Get all job definitions
    console.log('📋 Loading all job definitions...');
    const allJobs = await getJobDefinitions();
    console.log(`Found ${allJobs.length} total jobs\n`);

    // Find ADK processing jobs
    const adkJobs = allJobs.filter(job => job.dataSource.type === 'adk_processing');
    console.log(`📊 Found ${adkJobs.length} ADK processing jobs:`);
    
    for (const job of adkJobs) {
      console.log(`  - ${job.id}: ${job.name} (enabled: ${job.enabled})`);
    }
    console.log();

    // Check the specific failing job
    const failingJobId = 'job-1753091554181';
    console.log(`🔍 Checking specific failing job: ${failingJobId}`);
    const failingJob = await getJobDefinition(failingJobId);
    
    if (!failingJob) {
      console.log(`❌ Job ${failingJobId} not found in database`);
      return;
    }

    console.log(`📄 Job Details:`);
    console.log(`  Name: ${failingJob.name}`);
    console.log(`  Type: ${failingJob.dataSource.type}`);
    console.log(`  Enabled: ${failingJob.enabled}`);
    console.log();

    // Check job handler registration
    console.log('🔧 Checking job handler registration...');
    const supportedTypes = jobHandlerFactory.getSupportedJobTypes();
    console.log(`Supported job types: ${supportedTypes.join(', ')}`);
    
    const isAdkSupported = jobHandlerFactory.isJobTypeSupported('adk_processing');
    console.log(`ADK processing supported: ${isAdkSupported}`);
    console.log();

    // Try to get the handler and validate
    if (isAdkSupported) {
      console.log('🧪 Testing job handler validation...');
      try {
        const handler = jobHandlerFactory.getHandler('adk_processing');
        console.log(`Handler found: ${handler.constructor.name}`);
        console.log(`Handler jobType: ${handler.jobType}`);
        
        // Test validation
        const isValid = handler.validateConfig(failingJob);
        console.log(`Configuration valid: ${isValid}`);
        
        if (!isValid) {
          console.log('\n❌ Configuration validation failed. Checking details...');
          
          // Check basic validation
          const basicValid = failingJob.dataSource.type === handler.jobType;
          console.log(`  Basic type check: ${basicValid} (${failingJob.dataSource.type} === ${handler.jobType})`);
          
          // Check ADK config exists
          const adkConfig = failingJob.dataSource.adk_processing;
          console.log(`  ADK config exists: ${!!adkConfig}`);
          
          if (adkConfig) {
            console.log(`  ADK config details:`);
            console.log(`    sourceDirectory: ${adkConfig.sourceDirectory || 'MISSING'}`);
            console.log(`    extractionPath: ${adkConfig.extractionPath || 'MISSING'}`);
            console.log(`    rarToolPath: ${adkConfig.rarToolPath || 'MISSING'}`);
            console.log(`    fileListDatabase: ${!!adkConfig.fileListDatabase}`);
            
            if (adkConfig.fileListDatabase) {
              const db = adkConfig.fileListDatabase;
              console.log(`      host: ${db.host || 'MISSING'}`);
              console.log(`      port: ${db.port || 'MISSING'}`);
              console.log(`      database: ${db.database || 'MISSING'}`);
              console.log(`      username: ${db.username || 'MISSING'}`);
              console.log(`      password: ${db.password ? '[SET]' : 'MISSING'}`);
              console.log(`      table: ${db.table || 'MISSING'}`);
            }
          }
          
          // Check destination config
          console.log(`  Destination config:`);
          console.log(`    type: ${failingJob.destination?.type || 'MISSING'}`);
          console.log(`    fileTracking: ${!!failingJob.destination?.fileTracking}`);
          
          if (failingJob.destination?.fileTracking) {
            const ft = failingJob.destination.fileTracking;
            console.log(`      enabled: ${ft.enabled}`);
            console.log(`      database: ${!!ft.database}`);
            
            if (ft.database) {
              const db = ft.database;
              console.log(`        host: ${db.host || 'MISSING'}`);
              console.log(`        port: ${db.port || 'MISSING'}`);
              console.log(`        database: ${db.database || 'MISSING'}`);
              console.log(`        username: ${db.username || 'MISSING'}`);
              console.log(`        password: ${db.password ? '[SET]' : 'MISSING'}`);
              console.log(`        table: ${db.table || 'MISSING'}`);
            }
          }
        }
        
      } catch (error) {
        console.log(`❌ Error getting/testing handler: ${error.message}`);
      }
    }

    // Show full job configuration for debugging
    console.log('\n📋 Full Job Configuration:');
    console.log(JSON.stringify(failingJob, null, 2));

  } catch (error) {
    console.error('❌ Error during debugging:', error);
  }
}

// Run the debug function
debugAdkJob().catch(console.error);
