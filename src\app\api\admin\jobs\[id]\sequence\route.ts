import { NextRequest, NextResponse } from "next/server";
import { loadJobDefinition, saveJobDefinition } from "@/lib/jobPersistence";
import { updateJobSequenceAssignment } from "@/lib/sequencePersistence";
import { logger } from "@/lib/jobManager";

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// PUT /api/admin/jobs/[id]/sequence - Assign job to sequence
export async function PUT(request: NextRequest, { params }: RouteParams) {
  const { id: jobId } = await params;
  try {
    const body = await request.json();
    const { sequenceId, order } = body;

    if (!sequenceId || typeof order !== "number") {
      return NextResponse.json(
        { error: "sequenceId and order are required" },
        { status: 400 }
      );
    }

    // Check if job exists
    const job = await loadJobDefinition(jobId);
    if (!job) {
      return NextResponse.json(
        { error: `Job not found: ${jobId}` },
        { status: 404 }
      );
    }

    // Update job sequence assignment in database
    await updateJobSequenceAssignment(jobId, sequenceId, order);

    // Update job definition object
    job.sequenceConfig = {
      sequenceId,
      order,
    };

    // Save updated job definition
    await saveJobDefinition(job);

    logger.info(
      `Job ${jobId} assigned to sequence ${sequenceId} at order ${order}`
    );

    // Broadcast both job and sequence updates to all connected clients
    try {
      const { broadcastAllUpdates } = await import("@/lib/sseManager");
      await broadcastAllUpdates();
    } catch (error) {
      logger.debug(
        "Could not broadcast updates after job sequence assignment",
        {
          error,
        }
      );
    }

    return NextResponse.json({
      message: "Job assigned to sequence successfully",
      jobId,
      sequenceId,
      order,
    });
  } catch (error) {
    logger.error(`Failed to assign job ${jobId} to sequence:`, error);
    return NextResponse.json(
      { error: "Failed to assign job to sequence" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/jobs/[id]/sequence - Remove job from sequence
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  const { id: jobId } = await params;
  try {
    // Check if job exists
    const job = await loadJobDefinition(jobId);
    if (!job) {
      return NextResponse.json(
        { error: `Job not found: ${jobId}` },
        { status: 404 }
      );
    }

    if (!job.sequenceConfig) {
      return NextResponse.json(
        { error: `Job ${jobId} is not assigned to any sequence` },
        { status: 400 }
      );
    }

    // Remove sequence assignment
    await updateJobSequenceAssignment(jobId, null, null);

    // Update job definition object
    job.sequenceConfig = undefined;

    // Save updated job definition
    await saveJobDefinition(job);

    logger.info(`Job ${jobId} removed from sequence`);

    // Broadcast both job and sequence updates to all connected clients
    try {
      const { broadcastAllUpdates } = await import("@/lib/sseManager");
      await broadcastAllUpdates();
    } catch (error) {
      logger.debug("Could not broadcast updates after job sequence removal", {
        error,
      });
    }

    return NextResponse.json({
      message: "Job removed from sequence successfully",
      jobId,
    });
  } catch (error) {
    logger.error(`Failed to remove job ${jobId} from sequence:`, error);
    return NextResponse.json(
      { error: "Failed to remove job from sequence" },
      { status: 500 }
    );
  }
}
