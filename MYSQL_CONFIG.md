# MySQL Data Source Configuration

This document explains how to configure your data pulling jobs to work with MySQL databases.

## Prerequisites

For MySQL connectivity, you'll need to install the following package:

```bash
# For MySQL database connectivity
npm install mysql2
```

## Job Configuration Schema

### MySQL Database Source

```typescript
{
  dataSource: {
    type: "mysql",
    mysql: {
      host: "your-mysql-host.com",
      port: 3306,
      database: "your_database_name",
      username: "your_username",
      password: "your_password",
      query: "SELECT * FROM your_table WHERE condition",
      connectionLimit: 10, // optional, default: 10
      acquireTimeout: 60000, // optional, default: 60000ms
      timeout: 60000 // optional, default: 60000ms
    }
  }
}
```

### Configuration Parameters

- **host**: MySQL server hostname or IP address
- **port**: MySQL server port (default: 3306)
- **database**: Name of the MySQL database to connect to
- **username**: MySQL username for authentication
- **password**: MySQL password for authentication
- **query**: SQL query to execute for data retrieval
- **connectionLimit** (optional): Maximum number of connections in the pool (default: 10)
- **acquireTimeout** (optional): Maximum time to wait for a connection (default: 60000ms)
- **timeout** (optional): Maximum time to wait for query execution (default: 60000ms)

## Example Job Definition

```typescript
{
  id: "daily-sales-mysql",
  name: "Daily Sales Data from MySQL",
  description: "Pull daily sales data from MySQL CRM database",
  schedule: "0 2 * * *", // Daily at 2 AM
  enabled: true,
  dataSource: {
    type: "mysql",
    mysql: {
      host: process.env.MYSQL_HOST || "localhost",
      port: parseInt(process.env.MYSQL_PORT || "3306"),
      database: process.env.MYSQL_DATABASE || "crm_db",
      username: process.env.MYSQL_USERNAME || "user",
      password: process.env.MYSQL_PASSWORD || "password",
      query: `
        SELECT order_id, customer_id, order_date, total_amount, status
        FROM sales_orders
        WHERE order_date >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND order_date < CURDATE()
      `,
      connectionLimit: 15,
      acquireTimeout: 30000,
      timeout: 45000
    }
  },
  destination: {
    type: "database",
    database: {
      type: "mysql",
      host: "localhost",
      port: 3306,
      database: "data_warehouse",
      username: "warehouse_user",
      password: "warehouse_pass",
      table: "daily_sales",
      schema: "analytics"
    }
  },
  retryConfig: {
    maxRetries: 3,
    retryDelay: 30
  }
}
```

## Environment Variables

You can use environment variables for sensitive configuration:

```bash
# MySQL source database
MYSQL_HOST=your-mysql-server.com
MYSQL_PORT=3306
MYSQL_DATABASE=source_database
MYSQL_USERNAME=source_user
MYSQL_PASSWORD=source_password

# MySQL destination database (if using database destination)
DEST_MYSQL_HOST=your-destination-server.com
DEST_MYSQL_PORT=3306
DEST_MYSQL_DATABASE=destination_database
DEST_MYSQL_USERNAME=dest_user
DEST_MYSQL_PASSWORD=dest_password
```

## SQL Query Guidelines

### Best Practices

1. **Use parameterized queries** when possible to prevent SQL injection
2. **Limit result sets** using LIMIT clause to prevent memory issues
3. **Use indexes** on columns used in WHERE clauses for better performance
4. **Test queries** in MySQL client before adding to job definition

### Example Queries

```sql
-- Daily data pull with date filtering
SELECT id, name, created_at, updated_at
FROM users
WHERE DATE(created_at) = CURDATE() - INTERVAL 1 DAY;

-- Incremental data pull using timestamp
SELECT order_id, customer_id, order_total, created_at
FROM orders
WHERE created_at > '2024-01-01 00:00:00'
ORDER BY created_at ASC
LIMIT 10000;

-- Aggregated data pull
SELECT 
  DATE(order_date) as order_day,
  COUNT(*) as total_orders,
  SUM(total_amount) as total_revenue
FROM orders
WHERE order_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY DATE(order_date)
ORDER BY order_day DESC;
```

## Connection Pool Configuration

The MySQL data source uses connection pooling for better performance:

- **connectionLimit**: Controls maximum concurrent connections
- **acquireTimeout**: Maximum time to wait for an available connection
- **timeout**: Maximum time for query execution
- **queueLimit**: Set to 0 (unlimited) for job processing

## Error Handling

The MySQL data source includes comprehensive error handling:

1. **Connection errors**: Logged with host, port, and database information
2. **Query errors**: Logged with query details (first 100 characters)
3. **Timeout errors**: Handled with configurable timeout settings
4. **Pool cleanup**: Ensures connections are properly closed

## Security Considerations

1. **Use environment variables** for sensitive credentials
2. **Limit database permissions** to only required tables and operations
3. **Use SSL connections** when connecting over networks
4. **Regularly rotate passwords** and update job configurations
5. **Monitor connection logs** for suspicious activity

## Troubleshooting

### Common Issues

1. **Connection timeout**: Increase `acquireTimeout` and `timeout` values
2. **Too many connections**: Reduce `connectionLimit` or check for connection leaks
3. **Query timeout**: Optimize query or increase `timeout` value
4. **Permission denied**: Verify user has SELECT permissions on target tables

### Debugging

Enable detailed logging by setting log level to debug in your environment:

```bash
LOG_LEVEL=debug
```

This will provide detailed information about:
- Connection attempts and results
- Query execution times
- Error details and stack traces
- Pool status and connection counts
