# ADK File Version Handling Update

## Problem Statement

Previously, when the SFTP job discovered multiple ADK files with the same base name but different extensions (e.g., `dipa_file.s25`, `dipa_file.s2501`, `dipa_file.s2502`), all files were marked with status `"NEW"` in the `file_metadata` table. This caused the ADK processing job to process all versions of the same file, leading to:

- Redundant data processing
- Potential duplicate records
- Unnecessary resource consumption

## Solution

Modified the SFTP job handler to implement intelligent version detection that:

1. **Groups files by base name** (filename without extension)
2. **Identifies the latest version** based on extension numbering
3. **Marks only the latest version as "NEW"** for processing
4. **Marks older versions as "SUPERSEDED"** to skip processing

## Implementation Details

### File Version Logic

The system now recognizes version patterns in file extensions:

- **s25** → Version 25
- **s2501** → Version 2501  
- **s2502** → Version 2502
- **pdf** → Version 0 (non-versioned files)

Higher numbers indicate newer versions.

### Status Assignment

| File Status | Description | Processing |
|-------------|-------------|------------|
| `NEW` | Latest version of the file | ✅ Processed by ADK job |
| `SUPERSEDED` | Older version of the file | ❌ Skipped by ADK job |
| `PROCESSED` | Successfully processed | ❌ Skipped by ADK job |
| `ERROR` | Processing failed | ❌ Skipped by ADK job |

### Example Scenario

For files in folder `34/152/2025`:
```
dipa_budget.s25      → Status: SUPERSEDED (version 25)
dipa_budget.s2501    → Status: SUPERSEDED (version 2501)  
dipa_budget.s2502    → Status: NEW (version 2502) ← Only this gets processed
```

## Code Changes

### Modified Files

1. **`src/lib/jobs/sftp/SftpJobHandler.ts`**
   - Added `determineFileStatus()` method
   - Added `parseFilename()` helper method
   - Added `findLatestVersion()` helper method
   - Added `extractVersionNumber()` helper method
   - Modified `createSftpFileMetadata()` to use version-aware status assignment

2. **`src/lib/jobs/adk-processing/AdkProcessingJobHandler.ts`**
   - Added documentation comments explaining the new behavior
   - Updated log messages to clarify "latest versions only"

### New Methods

```typescript
// Determines if a file should be marked as NEW or SUPERSEDED
private async determineFileStatus(dbConfig, folder, filename): Promise<string>

// Extracts base name and extension from filename
private parseFilename(filename): { baseName: string; extension: string | null }

// Finds the latest version from a list of files
private findLatestVersion(filenames: string[]): string

// Extracts numeric version from extension (e.g., "s2502" → 2502)
private extractVersionNumber(filename: string): number
```

## Benefits

1. **Eliminates duplicate processing** - Only the latest version of each file is processed
2. **Reduces resource usage** - Fewer files to extract and process
3. **Maintains data integrity** - No risk of processing outdated data
4. **Backward compatible** - Existing files continue to work normally
5. **Automatic detection** - No manual intervention required

## Testing

The implementation has been tested with various file naming scenarios:

- ✅ Files with s-extensions (s25, s2501, s2502)
- ✅ Mixed extensions (pdf, s25, s26)
- ✅ Single files
- ✅ Files without extensions
- ✅ Version number extraction accuracy

## Migration Notes

- **Existing data**: No migration required for existing `file_metadata` records
- **New downloads**: The new logic applies automatically to newly discovered files
- **Status updates**: Older versions will be marked as `SUPERSEDED` during the next SFTP job run
- **ADK processing**: Will automatically process only `NEW` status files (latest versions)

## Monitoring

To monitor the new behavior:

```sql
-- Check file status distribution
SELECT status, COUNT(*) as count 
FROM file_metadata 
GROUP BY status;

-- Find superseded files
SELECT folder, nmfile, status 
FROM file_metadata 
WHERE status = 'SUPERSEDED' 
ORDER BY folder, nmfile;

-- Verify latest versions are marked as NEW
SELECT folder, nmfile, status 
FROM file_metadata 
WHERE nmfile LIKE '%.s%' AND status = 'NEW'
ORDER BY folder, nmfile;
```

This update ensures that only the most current version of each ADK file is processed, significantly improving efficiency and data accuracy.
