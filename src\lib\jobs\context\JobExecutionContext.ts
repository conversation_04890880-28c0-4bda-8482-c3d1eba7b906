import type { JobDefinition } from "../../jobManager";
import type { JobExecutionContext as IJobExecutionContext } from "../types";
import { addJobLog } from "../../jobManager";
import { isJobCancelled } from "../../jobRunner";

/**
 * Implementation of JobExecutionContext that provides shared utilities
 * and state management for job handlers
 */
export class JobExecutionContext implements IJobExecutionContext {
  public readonly jobId: string;
  public readonly executionId: string;
  public readonly jobDefinition: JobDefinition;

  constructor(
    jobId: string,
    executionId: string,
    jobDefinition: JobDefinition
  ) {
    this.jobId = jobId;
    this.executionId = executionId;
    this.jobDefinition = jobDefinition;
  }

  /**
   * Add a log entry for this job
   */
  async addLog(message: string): Promise<void> {
    await addJobLog(this.jobId, message);
  }

  /**
   * Check if the job has been cancelled and throw an error if so
   */
  async checkCancellation(): Promise<void> {
    if (this.isCancelled()) {
      await this.addLog("Job cancellation requested by user");
      throw new Error("Job was cancelled by user");
    }
  }

  /**
   * Check if the job is currently cancelled
   */
  isCancelled(): boolean {
    return isJobCancelled(this.jobId);
  }

  /**
   * Create a child context for sub-operations (useful for complex jobs)
   */
  createChildContext(subOperationName: string): JobExecutionContext {
    return new JobExecutionContext(
      this.jobId,
      `${this.executionId}-${subOperationName}`,
      this.jobDefinition
    );
  }

  /**
   * Add a log with timing information
   */
  async addTimedLog(message: string, startTime: number): Promise<void> {
    const duration = Date.now() - startTime;
    await this.addLog(`${message} (${duration}ms)`);
  }

  /**
   * Add a progress log for operations with known total count
   */
  async addProgressLog(
    current: number,
    total: number,
    operation: string
  ): Promise<void> {
    const percentage = Math.round((current / total) * 100);
    await this.addLog(`${operation}: ${current}/${total} (${percentage}%)`);
  }

  /**
   * Execute an operation with automatic cancellation checking and timing
   */
  async executeWithCancellationCheck<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    await this.checkCancellation();
    const startTime = Date.now();

    try {
      const result = await operation();
      await this.addTimedLog(`✅ ${operationName} completed`, startTime);
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      await this.addTimedLog(
        `❌ ${operationName} failed: ${errorMessage}`,
        startTime
      );
      throw error;
    }
  }

  /**
   * Execute an operation with retry logic
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    maxRetries: number = 3,
    retryDelay: number = 1000
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.checkCancellation();

        if (attempt > 1) {
          await this.addLog(
            `🔄 Retrying ${operationName} (attempt ${attempt}/${maxRetries})`
          );
        }

        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt < maxRetries) {
          await this.addLog(
            `⚠️ ${operationName} failed (attempt ${attempt}/${maxRetries}): ${lastError.message}`
          );
          await new Promise((resolve) => setTimeout(resolve, retryDelay));
        }
      }
    }

    await this.addLog(
      `❌ ${operationName} failed after ${maxRetries} attempts`
    );
    throw (
      lastError || new Error(`Operation failed after ${maxRetries} attempts`)
    );
  }
}
