import { NextRequest, NextResponse } from "next/server";
import {
  loadJobSequence,
  getLatestSequenceExecution,
  loadSequenceExecutions,
} from "@/lib/sequencePersistence";
import { logger } from "@/lib/jobManager";
import { cronScheduler } from "@/lib/cronScheduler";
import JobSequenceManager from "@/lib/jobSequenceManager";

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/admin/sequences/[id]/status - Get detailed sequence status
export async function GET(request: NextRequest, { params }: RouteParams) {
  const { id: sequenceId } = await params;
  try {
    // Check if sequence exists
    const sequence = await loadJobSequence(sequenceId);
    if (!sequence) {
      return NextResponse.json(
        { error: `Sequence not found: ${sequenceId}` },
        { status: 404 }
      );
    }

    // Get sequence manager instance
    const sequenceManager = JobSequenceManager.getInstance();

    // Get current execution state
    const currentState = sequenceManager.getSequenceStatus(sequenceId);

    // Get latest execution from database
    const latestExecution = await getLatestSequenceExecution(sequenceId);

    // Get recent executions
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const recentExecutions = await loadSequenceExecutions(sequenceId, limit);

    // Build status response
    const status = {
      sequenceId,
      sequenceName: sequence.name,
      enabled: sequence.enabled,
      isRunning: sequenceManager.isSequenceRunning(sequenceId),
      isScheduled: cronScheduler.isSequenceScheduled(sequenceId),
      schedule: sequence.schedule,
      onFailure: sequence.onFailure,
      maxRetries: sequence.maxRetries,
      totalJobs: sequence.jobs.length,
      jobs: sequence.jobs,

      // Current execution state (if running)
      currentExecution: currentState
        ? {
            executionId: currentState.execution.id,
            status: currentState.execution.status,
            currentJobId: currentState.execution.currentJobId,
            currentJobOrder: currentState.execution.currentJobOrder,
            currentJobIndex: currentState.currentJobIndex,
            retryCount: currentState.retryCount,
            startTime: currentState.execution.startTime,
            triggerType: currentState.execution.triggerType,
          }
        : null,

      // Latest completed execution
      latestExecution,

      // Recent execution history
      recentExecutions,
    };

    return NextResponse.json({ status });
  } catch (error) {
    logger.error(`Failed to get sequence status ${sequenceId}:`, error);
    return NextResponse.json(
      { error: "Failed to retrieve sequence status" },
      { status: 500 }
    );
  }
}
