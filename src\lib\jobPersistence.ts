import { executeQuery, executeUpdate, executeTransaction } from "./database";
import { JobDefinition, JobExecution } from "./jobManager";
import { logger } from "./jobManager";
import mysql from "mysql2/promise";

// Types for database operations
interface DbJobDefinition {
  id: string;
  name: string;
  description: string | null;
  schedule_cron: string;
  enabled: boolean;
  data_source_type:
    | "oracle"
    | "sftp"
    | "mysql"
    | "database_admin"
    | "pdf_dipa"
    | "adk_processing";
  data_source_config: string; // JSON string
  destination_config: string; // JSON string
  retry_config: string; // JSON string
  sequence_id: string | null;
  sequence_order: number | null;
  created_at: Date;
  updated_at: Date;
}

interface DbJobExecution {
  id: string;
  job_id: string;
  status: "running" | "completed" | "failed" | "scheduled" | "stopped";
  trigger_type: "manual" | "automatic";
  start_time: Date;
  end_time: Date | null;
  duration_seconds: number | null;
  records_processed: number | null;
  error_message: string | null;
  created_at: Date;
  updated_at: Date;
}

interface DbJobExecutionLog {
  id: number;
  execution_id: string;
  log_timestamp: Date;
  log_level: "info" | "warn" | "error" | "debug";
  message: string;
}

// Job Definitions CRUD operations
export async function saveJobDefinition(job: JobDefinition): Promise<void> {
  try {
    const query = `
      INSERT INTO job_definitions (
        id, name, description, schedule_cron, enabled,
        data_source_type, data_source_config, destination_config, retry_config,
        sequence_id, sequence_order
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        description = VALUES(description),
        schedule_cron = VALUES(schedule_cron),
        enabled = VALUES(enabled),
        data_source_type = VALUES(data_source_type),
        data_source_config = VALUES(data_source_config),
        destination_config = VALUES(destination_config),
        retry_config = VALUES(retry_config),
        sequence_id = VALUES(sequence_id),
        sequence_order = VALUES(sequence_order),
        updated_at = CURRENT_TIMESTAMP
    `;

    const params = [
      job.id,
      job.name,
      job.description,
      job.schedule,
      job.enabled,
      job.dataSource.type,
      JSON.stringify(job.dataSource),
      JSON.stringify(job.destination),
      JSON.stringify(job.retryConfig),
      job.sequenceConfig?.sequenceId || null,
      job.sequenceConfig?.order || null,
    ];

    await executeUpdate(query, params);
    logger.info(`Job definition saved: ${job.id} - ${job.name}`);
  } catch (error) {
    logger.error(`Failed to save job definition ${job.id}:`, error);
    throw error;
  }
}

export async function loadJobDefinitions(): Promise<JobDefinition[]> {
  try {
    const query = "SELECT * FROM job_definitions ORDER BY name";
    const rows = await executeQuery<DbJobDefinition>(query);

    return rows.map((row) => ({
      id: row.id,
      name: row.name,
      description: row.description || "",
      schedule: row.schedule_cron,
      enabled: row.enabled,
      dataSource: typeof row.data_source_config === 'string' ? JSON.parse(row.data_source_config) : row.data_source_config,
      destination: typeof row.destination_config === 'string' ? JSON.parse(row.destination_config) : row.destination_config,
      retryConfig: typeof row.retry_config === 'string' ? JSON.parse(row.retry_config) : row.retry_config,
      sequenceConfig: row.sequence_id
        ? {
            sequenceId: row.sequence_id,
            order: row.sequence_order || 0,
          }
        : undefined,
    }));
  } catch (error) {
    logger.error("Failed to load job definitions:", error);
    throw error;
  }
}

export async function loadJobDefinition(
  jobId: string
): Promise<JobDefinition | null> {
  try {
    const query = "SELECT * FROM job_definitions WHERE id = ?";
    const rows = await executeQuery<DbJobDefinition>(query, [jobId]);

    if (rows.length === 0) {
      return null;
    }

    const row = rows[0];
    return {
      id: row.id,
      name: row.name,
      description: row.description || "",
      schedule: row.schedule_cron,
      enabled: row.enabled,
      dataSource: typeof row.data_source_config === 'string' ? JSON.parse(row.data_source_config) : row.data_source_config,
      destination: typeof row.destination_config === 'string' ? JSON.parse(row.destination_config) : row.destination_config,
      retryConfig: typeof row.retry_config === 'string' ? JSON.parse(row.retry_config) : row.retry_config,
      sequenceConfig: row.sequence_id
        ? {
            sequenceId: row.sequence_id,
            order: row.sequence_order || 0,
          }
        : undefined,
    };
  } catch (error) {
    logger.error(`Failed to load job definition ${jobId}:`, error);
    throw error;
  }
}

export async function updateJobEnabled(
  jobId: string,
  enabled: boolean
): Promise<void> {
  try {
    const query =
      "UPDATE job_definitions SET enabled = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    const result = await executeUpdate(query, [enabled, jobId]);

    if (result.affectedRows === 0) {
      throw new Error(`Job definition not found: ${jobId}`);
    }

    logger.info(`Job ${jobId} enabled state updated to ${enabled}`);
  } catch (error) {
    logger.error(`Failed to update job enabled state ${jobId}:`, error);
    throw error;
  }
}

export async function deleteJobDefinition(jobId: string): Promise<void> {
  try {
    await executeTransaction(async (connection) => {
      // First, check if job is part of a sequence and remove it
      const [jobRows] = await connection.execute(
        "SELECT sequence_id FROM job_definitions WHERE id = ?",
        [jobId]
      );

      const rows = jobRows as { sequence_id: string | null }[];
      if (rows.length > 0) {
        const job = rows[0];
        if (job.sequence_id) {
          logger.info(
            `Removing job ${jobId} from sequence ${job.sequence_id} before deletion`
          );
          // Remove sequence assignment
          await connection.execute(
            "UPDATE job_definitions SET sequence_id = NULL, sequence_order = NULL WHERE id = ?",
            [jobId]
          );
        }
      }

      // Delete job execution logs first
      await connection.execute(
        "DELETE jel FROM job_execution_logs jel JOIN job_executions je ON jel.execution_id = je.id WHERE je.job_id = ?",
        [jobId]
      );

      // Delete job executions
      await connection.execute("DELETE FROM job_executions WHERE job_id = ?", [
        jobId,
      ]);

      // Delete schedule history
      await connection.execute(
        "DELETE FROM job_schedule_history WHERE job_id = ?",
        [jobId]
      );

      // Delete SFTP file transfers if any
      await connection.execute(
        "DELETE FROM sftp_file_transfers WHERE job_id = ?",
        [jobId]
      );

      // Delete job definition
      const [result] = await connection.execute(
        "DELETE FROM job_definitions WHERE id = ?",
        [jobId]
      );
      const resultInfo = result as mysql.ResultSetHeader;

      if (resultInfo.affectedRows === 0) {
        throw new Error(`Job definition not found: ${jobId}`);
      }
    });

    logger.info(`Job definition deleted: ${jobId}`);
  } catch (error) {
    logger.error(`Failed to delete job definition ${jobId}:`, error);
    throw error;
  }
}

// Job Executions CRUD operations
export async function saveJobExecution(execution: JobExecution): Promise<void> {
  try {
    const query = `
      INSERT INTO job_executions (
        id, job_id, status, trigger_type, start_time, end_time,
        duration_seconds, records_processed, error_message
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        trigger_type = VALUES(trigger_type),
        end_time = VALUES(end_time),
        duration_seconds = VALUES(duration_seconds),
        records_processed = VALUES(records_processed),
        error_message = VALUES(error_message),
        updated_at = CURRENT_TIMESTAMP
    `;

    const params = [
      execution.id,
      execution.jobId,
      execution.status,
      execution.triggerType,
      execution.startTime,
      execution.endTime || null,
      execution.duration || null,
      execution.recordsProcessed || null,
      execution.errorMessage || null,
    ];

    await executeUpdate(query, params);
    logger.debug(`Job execution saved: ${execution.id}`);
  } catch (error) {
    logger.error(`Failed to save job execution ${execution.id}:`, error);
    throw error;
  }
}

export async function loadJobExecution(
  executionId: string
): Promise<JobExecution | null> {
  try {
    const query = "SELECT * FROM job_executions WHERE id = ?";
    const rows = await executeQuery<DbJobExecution>(query, [executionId]);

    if (rows.length === 0) {
      return null;
    }

    const row = rows[0];

    // Load logs for this execution
    const logs = await loadJobExecutionLogs(executionId);

    return {
      id: row.id,
      jobId: row.job_id,
      status: row.status,
      triggerType: row.trigger_type,
      startTime: row.start_time,
      endTime: row.end_time || undefined,
      duration: row.duration_seconds || undefined,
      recordsProcessed: row.records_processed || undefined,
      errorMessage: row.error_message || undefined,
      logs: logs.map(
        (log) => `${log.log_timestamp.toLocaleTimeString()} - ${log.message}`
      ),
    };
  } catch (error) {
    logger.error(`Failed to load job execution ${executionId}:`, error);
    throw error;
  }
}

export async function loadJobExecutions(
  jobId?: string,
  limit = 100
): Promise<JobExecution[]> {
  try {
    // Validate limit to prevent SQL injection
    const safeLimit = Math.max(1, Math.min(1000, Math.floor(limit)));
    
    let query = "SELECT * FROM job_executions";
    const params: unknown[] = [];

    if (jobId) {
      query += " WHERE job_id = ?";
      params.push(jobId);
    }

    query += ` ORDER BY start_time DESC LIMIT ${safeLimit}`;

    const rows = await executeQuery<DbJobExecution>(query, params);

    // Load logs for all executions
    const executions = await Promise.all(
      rows.map(async (row) => {
        const logs = await loadJobExecutionLogs(row.id);

        return {
          id: row.id,
          jobId: row.job_id,
          status: row.status,
          triggerType: row.trigger_type,
          startTime: row.start_time,
          endTime: row.end_time || undefined,
          duration: row.duration_seconds || undefined,
          recordsProcessed: row.records_processed || undefined,
          errorMessage: row.error_message || undefined,
          logs: logs.map(
            (log) =>
              `${log.log_timestamp.toLocaleTimeString()} - ${log.message}`
          ),
        };
      })
    );

    return executions;
  } catch (error) {
    logger.error("Failed to load job executions:", error);
    throw error;
  }
}

export async function getLatestJobExecution(
  jobId: string
): Promise<JobExecution | null> {
  try {
    const executions = await loadJobExecutions(jobId, 1);
    return executions.length > 0 ? executions[0] : null;
  } catch (error) {
    logger.error(`Failed to get latest job execution for ${jobId}:`, error);
    throw error;
  }
}

// Job Execution Logs CRUD operations
export async function addJobExecutionLog(
  executionId: string,
  message: string,
  level: "info" | "warn" | "error" | "debug" = "info"
): Promise<void> {
  try {
    const query = `
      INSERT INTO job_execution_logs (execution_id, message, log_level)
      VALUES (?, ?, ?)
    `;

    await executeUpdate(query, [executionId, message, level]);
    logger.debug(`Job execution log added: ${executionId} - ${message}`);
  } catch (error) {
    logger.error(`Failed to add job execution log for ${executionId}:`, error);
    throw error;
  }
}

export async function loadJobExecutionLogs(
  executionId: string
): Promise<DbJobExecutionLog[]> {
  try {
    const query = `
      SELECT * FROM job_execution_logs 
      WHERE execution_id = ? 
      ORDER BY log_timestamp ASC
    `;

    return await executeQuery<DbJobExecutionLog>(query, [executionId]);
  } catch (error) {
    logger.error(
      `Failed to load job execution logs for ${executionId}:`,
      error
    );
    throw error;
  }
}

// Cleanup old job executions and logs
export async function cleanupOldJobData(daysToKeep = 30): Promise<void> {
  try {
    await executeTransaction(async (connection) => {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      // Delete old execution logs
      const [logsResult] = await connection.execute(
        `DELETE jel FROM job_execution_logs jel 
         JOIN job_executions je ON jel.execution_id = je.id 
         WHERE je.start_time < ?`,
        [cutoffDate]
      );

      // Delete old executions
      const [executionsResult] = await connection.execute(
        "DELETE FROM job_executions WHERE start_time < ?",
        [cutoffDate]
      );

      // Delete old schedule history
      const [scheduleResult] = await connection.execute(
        "DELETE FROM job_schedule_history WHERE scheduled_time < ?",
        [cutoffDate]
      );

      const logsInfo = logsResult as mysql.ResultSetHeader;
      const executionsInfo = executionsResult as mysql.ResultSetHeader;
      const scheduleInfo = scheduleResult as mysql.ResultSetHeader;

      logger.info(
        `Cleanup completed - Deleted: ${logsInfo.affectedRows} logs, ${executionsInfo.affectedRows} executions, ${scheduleInfo.affectedRows} schedule entries older than ${daysToKeep} days`
      );
    });
  } catch (error) {
    logger.error("Failed to cleanup old job data:", error);
    throw error;
  }
}
