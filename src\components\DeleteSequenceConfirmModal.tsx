"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react";
import { Trash2, AlertTriangle, Maximize, Minimize, Users } from "lucide-react";
import { JobSequence } from "@/lib/jobManager";

interface DeleteSequenceConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  sequence: JobSequence | null;
  isLoading?: boolean;
}

export const DeleteSequenceConfirmModal = ({
  isOpen,
  onClose,
  onConfirm,
  sequence,
  isLoading = false,
}: DeleteSequenceConfirmModalProps) => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  if (!sequence) return null;

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size={isFullscreen ? "full" : "lg"}
      classNames={{
        backdrop: "bg-gradient-to-t from-zinc-900 to-zinc-900/10 backdrop-opacity-20"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Trash2 className="w-5 h-5 text-danger" />
              <span>Confirm Sequence Deletion</span>
            </div>
            <Button
              variant="flat"
              size="sm"
              isIconOnly
              startContent={
                isFullscreen ? (
                  <Minimize className="w-4 h-4" />
                ) : (
                  <Maximize className="w-4 h-4" />
                )
              }
              onPress={() => setIsFullscreen(!isFullscreen)}
              title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
            />
          </div>
        </ModalHeader>

        <ModalBody>
          <div className="space-y-4">
            <div className="flex items-start gap-3 p-4 bg-danger-50 dark:bg-danger-900/20 rounded-lg border border-danger-200 dark:border-danger-800">
              <AlertTriangle className="w-5 h-5 text-danger mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium text-danger">
                  This action cannot be undone!
                </p>
                <p className="text-sm text-danger-600 dark:text-danger-400 mt-1">
                  Deleting this sequence will permanently remove it and all associated configuration.
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-purple-600" />
                <span className="font-medium">Sequence: {sequence.name}</span>
              </div>
              
              {sequence.description && (
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <strong>Description:</strong> {sequence.description}
                </div>
              )}

              <div className="text-sm text-gray-600 dark:text-gray-400">
                <strong>Jobs in sequence:</strong> {sequence.jobs.length} job(s)
              </div>
            </div>

            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Are you sure you want to delete this sequence? This will remove:
              </p>
              <ul className="text-sm text-gray-600 dark:text-gray-400 mt-2 ml-4 space-y-1">
                <li>• Sequence configuration and settings</li>
                <li>• Schedule information</li>
                <li>• Job ordering and dependencies</li>
                <li>• All execution history for this sequence</li>
                <li className="text-warning-600 dark:text-warning-400">
                  • Jobs will be converted back to individual jobs
                </li>
              </ul>
            </div>
          </div>
        </ModalBody>

        <ModalFooter>
          <Button variant="flat" onPress={onClose} isDisabled={isLoading}>
            Cancel
          </Button>
          <Button
            color="danger"
            onPress={handleConfirm}
            startContent={<Trash2 className="w-4 h-4" />}
            isLoading={isLoading}
          >
            {isLoading ? "Deleting..." : "Delete Sequence"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
