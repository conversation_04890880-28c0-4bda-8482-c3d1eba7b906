import type {
  <PERSON><PERSON><PERSON><PERSON>,
  JobHandlerFactory as IJobHandlerFactory,
  JobHandlerRegistry,
} from "../types";

/**
 * Factory class for creating job handlers based on job type
 */
export class JobHandlerFactory implements IJobHandlerFactory {
  private registry: JobHandlerRegistry = new Map();
  private handlerInstances: Map<string, JobHandler> = new Map();

  /**
   * Get a handler for the specified job type
   */
  getHandler(jobType: string): JobHandler {
    // Return cached instance if available
    if (this.handlerInstances.has(jobType)) {
      return this.handlerInstances.get(jobType)!;
    }

    // Create new instance
    const HandlerClass = this.registry.get(jobType);
    if (!HandlerClass) {
      throw new Error(`No handler registered for job type: ${jobType}`);
    }

    const handler = new HandlerClass();

    // Validate that the handler's jobType matches the requested type
    if (handler.jobType !== jobType) {
      throw new Error(
        `Handler jobType mismatch: expected '${jobType}', got '${handler.jobType}'`
      );
    }

    // Cache the instance
    this.handlerInstances.set(jobType, handler);

    return handler;
  }

  /**
   * Register a new job handler
   */
  registerHandler(jobType: string, handlerClass: new () => JobHandler): void {
    if (this.registry.has(jobType)) {
      throw new Error(
        `Handler for job type '${jobType}' is already registered`
      );
    }

    // Validate that the handler class creates instances with the correct jobType
    try {
      const testInstance = new handlerClass();
      if (testInstance.jobType !== jobType) {
        throw new Error(
          `Handler class jobType mismatch: expected '${jobType}', got '${testInstance.jobType}'`
        );
      }
    } catch (error) {
      throw new Error(
        `Failed to validate handler class for '${jobType}': ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }

    this.registry.set(jobType, handlerClass);

    // Clear any cached instance since we're registering a new handler
    this.handlerInstances.delete(jobType);
  }

  /**
   * Get all supported job types
   */
  getSupportedJobTypes(): string[] {
    return Array.from(this.registry.keys()).sort();
  }

  /**
   * Check if a job type is supported
   */
  isJobTypeSupported(jobType: string): boolean {
    return this.registry.has(jobType);
  }

  /**
   * Unregister a job handler (useful for testing or dynamic loading)
   */
  unregisterHandler(jobType: string): boolean {
    const wasRegistered = this.registry.has(jobType);
    this.registry.delete(jobType);
    this.handlerInstances.delete(jobType);
    return wasRegistered;
  }

  /**
   * Clear all registered handlers
   */
  clearAllHandlers(): void {
    this.registry.clear();
    this.handlerInstances.clear();
  }

  /**
   * Get handler information for debugging
   */
  getHandlerInfo(jobType: string): {
    jobType: string;
    isRegistered: boolean;
    isInstantiated: boolean;
    handlerClass?: string;
  } {
    return {
      jobType,
      isRegistered: this.registry.has(jobType),
      isInstantiated: this.handlerInstances.has(jobType),
      handlerClass: this.registry.get(jobType)?.name,
    };
  }

  /**
   * Validate all registered handlers
   */
  validateAllHandlers(): Array<{
    jobType: string;
    isValid: boolean;
    error?: string;
  }> {
    const results: Array<{
      jobType: string;
      isValid: boolean;
      error?: string;
    }> = [];

    for (const [jobType, HandlerClass] of this.registry.entries()) {
      try {
        const instance = new HandlerClass();

        // Check if jobType matches
        if (instance.jobType !== jobType) {
          results.push({
            jobType,
            isValid: false,
            error: `jobType mismatch: expected '${jobType}', got '${instance.jobType}'`,
          });
          continue;
        }

        // Check if required methods exist
        if (typeof instance.execute !== "function") {
          results.push({
            jobType,
            isValid: false,
            error: "Missing execute method",
          });
          continue;
        }

        if (typeof instance.validateConfig !== "function") {
          results.push({
            jobType,
            isValid: false,
            error: "Missing validateConfig method",
          });
          continue;
        }

        results.push({ jobType, isValid: true });
      } catch (error) {
        results.push({
          jobType,
          isValid: false,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    return results;
  }
}

// Create and export a singleton instance
export const jobHandlerFactory = new JobHandlerFactory();
