import React from "react";
import { Input } from "@heroui/react";
import { RotateCcw } from "lucide-react";
import { JobDefinition } from "@/lib/jobManager";

interface RetryConfigFormProps {
  editedJob: JobDefinition;
  updateNestedField: (path: string, value: unknown) => void;
}

export const RetryConfigForm: React.FC<RetryConfigFormProps> = ({
  editedJob,
  updateNestedField,
}) => {
  return (
    <div className="form-section animate-fade-in">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 rounded-lg bg-orange-100 flex items-center justify-center">
          <RotateCcw className="w-4 h-4 text-orange-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Retry Policy</h3>
          <p className="text-sm text-gray-600">
            Configure error handling and retry behavior
          </p>
        </div>
      </div>

      <div className="form-group">
        {/* Compact two-column layout for retry settings */}
        <div className="form-row">
          <Input
            label="Max Retries"
            type="number"
            value={editedJob.retryConfig.maxRetries.toString()}
            onChange={(e) =>
              updateNestedField(
                "retryConfig.maxRetries",
                parseInt(e.target.value) || 0
              )
            }
            placeholder="3"
            description="Retry attempts on failure"
            min={0}
            max={10}
            isRequired
            classNames={{
              input: "text-sm",
              label: "text-sm font-medium text-gray-700",
              description: "text-xs text-gray-500",
            }}
          />
          <Input
            label="Retry Delay (seconds)"
            type="number"
            value={editedJob.retryConfig.retryDelay.toString()}
            onChange={(e) =>
              updateNestedField(
                "retryConfig.retryDelay",
                parseInt(e.target.value) || 0
              )
            }
            placeholder="300"
            description="Delay between attempts"
            min={0}
            isRequired
            classNames={{
              input: "text-sm",
              label: "text-sm font-medium text-gray-700",
              description: "text-xs text-gray-500",
            }}
          />
        </div>

        {/* Helpful guidance */}
        <div className="bg-blue-50/50 p-4 rounded-lg border border-blue-200">
          <div className="flex items-start gap-2">
            <div className="w-4 h-4 rounded-full bg-blue-500 flex-shrink-0 mt-0.5"></div>
            <div>
              <p className="text-sm font-medium text-blue-900">
                Recommendation
              </p>
              <p className="text-sm text-blue-700 mt-1">
                3 retries with 300 seconds delay works well for most jobs.
                Increase delay for external services.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
