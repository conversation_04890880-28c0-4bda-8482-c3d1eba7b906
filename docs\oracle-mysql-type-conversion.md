# Oracle-to-MySQL Type Conversion Enhancement

## Overview

This document describes the enhancements made to the Oracle-to-MySQL data transfer process in the Sintesa Data Puller application, specifically addressing issues with large financial values like those in the PAGU column.

## Problem Statement

The original implementation had limitations when transferring Oracle NUMBER data types to MySQL, particularly for:

1. Large integer values exceeding MySQL's INT range (e.g., PAGU values in billions)
2. Financial data requiring precise decimal handling
3. Oracle NUMBER columns with varying precision and scale
4. DateTime Format Issues: JavaScript Date objects being inserted as strings instead of proper MySQL DATETIME format, causing errors like "Incorrect datetime value: 'Tue Jan 14 2025 00:00:00 GMT+0700 (Indochina Time)' for column 'TGL_DOKUMEN'"

These limitations resulted in data transfer errors such as "Data out of range for column 'pagu'" when transferring large financial values.

## Solution Implemented

We've enhanced the data type conversion process at multiple levels:

## Solution Overview

We implemented a comprehensive four-part solution:

### 1. Oracle Data Extraction Enhancement
**File**: `src/tarikData/db/Oracle.js`

- Configure Oracle driver to fetch NUMBER types as strings using `fetchAsString: [oracledb.NUMBER]`
- Implement post-processing logic to handle financial fields with decimal precision
- Add safe conversion for large integers (keeping as string if exceeds safe range)
- **DateTime Handling**: Process Oracle DATE/TIMESTAMP values and date strings, converting them to JavaScript Date objects
- Preserve original values for non-numeric data

### 2. MySQL Schema Creation Enhancement
**File**: `src/lib/jobRunner.ts` (saveToMySQL function)

- Enhanced type detection logic to identify financial fields by name pattern
- Map large integers to BIGINT type for values exceeding standard INT range
- Use DECIMAL(20,2) for financial fields requiring precision
- **DateTime Schema**: Properly map JavaScript Date objects to MySQL DATETIME type
- Maintain backward compatibility with existing type mappings

### 3. Data Insertion Logic Enhancement
**File**: `src/lib/jobRunner.ts` (data insertion loop)

- Add comprehensive error handling for MySQL data type errors
- Implement fallback string conversion for problematic values
- **DateTime Conversion**: Convert JavaScript Date objects to MySQL DATETIME format (YYYY-MM-DD HH:MM:SS)
- **Date String Handling**: Parse and convert JavaScript date strings to proper MySQL format
- Include detailed error logging for troubleshooting
- Graceful handling of Oracle NUMBER to MySQL type conversion failures

### 4. DateTime Format Conversion
**Files**: `src/tarikData/db/Oracle.js` and `src/lib/jobRunner.ts`

- **Oracle Side**: Convert Oracle DATE/TIMESTAMP and date strings to JavaScript Date objects
- **MySQL Side**: Convert Date objects to MySQL DATETIME format using `toISOString().slice(0, 19).replace('T', ' ')`
- Handle problematic JavaScript date strings like "Tue Jan 14 2025 00:00:00 GMT+0700 (Indochina Time)"
- Provide fallback handling for unparseable date strings

### 1. Oracle Data Extraction (Oracle.js)

- Configured Oracle driver to fetch NUMBER types as strings using `oracledb.fetchAsString = [oracledb.NUMBER]`
- Added post-processing of query results to handle Oracle NUMBER types appropriately:
  - Financial fields (identified by name patterns like "pagu", "anggaran", etc.) are converted to JavaScript numbers with decimal precision preserved
  - Large integers exceeding JavaScript's safe integer range are kept as strings to preserve precision
  - Regular decimal values are converted to JavaScript numbers

### 2. MySQL Schema Creation (jobRunner.ts)

- Enhanced type detection logic to create appropriate MySQL column types:
  - BIGINT for large integers exceeding INT range
  - DECIMAL(20,2) for financial fields (identified by name patterns)
  - Dynamic precision/scale for other decimal fields based on actual data
  - Improved VARCHAR/TEXT selection based on string length

### 3. Data Insertion Logic (jobRunner.ts)

- Added special handling for large numbers and financial values during INSERT operations
- Implemented fallback mechanism for data type conversion errors:
  - Attempts string conversion for values causing range errors
  - Provides detailed error logging for troubleshooting
- Added proper handling of null/undefined values

## Implementation Details

### Financial Field Detection

We identify financial fields using a regular expression pattern that matches common financial column names in both English and Indonesian:

```javascript
const isFinancialField = /^(pagu|anggaran|budget|amount|nilai|harga|biaya|cost|rupiah)$/i.test(key);
```

These fields receive special handling with DECIMAL(20,2) type in MySQL to accommodate large currency values with 2 decimal places.

### DateTime Conversion Examples

The datetime conversion process handles various date formats:

```javascript
// Handle Date objects properly for MySQL DATETIME columns
if (value instanceof Date) {
  // Convert JavaScript Date to MySQL DATETIME format (YYYY-MM-DD HH:MM:SS)
  return value.toISOString().slice(0, 19).replace('T', ' ');
}

// Handle string dates that might be in JavaScript Date format
if (typeof value === 'string' && /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun)/.test(value)) {
  try {
    const dateObj = new Date(value);
    if (!isNaN(dateObj.getTime())) {
      // Convert to MySQL DATETIME format
      return dateObj.toISOString().slice(0, 19).replace('T', ' ');
    }
  } catch (error) {
    // If date parsing fails, keep original value
    console.warn(`Failed to parse date string: ${value}`);
  }
}
```

This handles conversions like:
- `new Date('2025-01-14')` → `'2025-01-14 00:00:00'`
- `'Tue Jan 14 2025 00:00:00 GMT+0700 (Indochina Time)'` → `'2025-01-14 00:00:00'`

### Large Integer Handling

For integer values, we check if they exceed the MySQL INT range (-2,147,483,648 to 2,147,483,647):

```javascript
if (Number.isInteger(value)) {
  if (value >= -2147483648 && value <= 2147483647) {
    sqlType = "INT";
  } else {
    sqlType = "BIGINT";
  }
}
```

### Error Recovery

We've implemented a fallback mechanism that attempts to recover from data type errors during insertion:

```javascript
if (error.code === 'ER_WARN_DATA_OUT_OF_RANGE' || error.code === 'ER_DATA_TOO_LONG') {
  const stringValues = columns.map(col => {
    const val = rowData[col];
    return val !== null && val !== undefined ? String(val) : null;
  });
  
  await addJobLog(jobDef.id, `Retrying with string conversion for problematic row`);
  await connection.execute(insertSQL, stringValues);
}
```

## Testing

A comprehensive test suite has been created in `test/oracle-mysql-type-conversion.test.js` to verify the enhanced type conversion logic, covering:

1. MySQL table creation with appropriate column types
2. Handling of large PAGU values during data insertion
3. Graceful recovery from data type conversion errors
4. Proper identification of financial fields by name pattern
5. Handling of mixed data types in the same column
6. DateTime conversion from Oracle DATE/TIMESTAMP to MySQL DATETIME
7. Handling of JavaScript date strings and their conversion to proper MySQL format
8. Error recovery for problematic date formats

## Future Considerations

1. **Configuration Options**: Consider adding configuration options to customize type mapping for specific columns or data sources
2. **Performance Optimization**: Monitor performance impact of string conversion for large datasets
3. **Additional Financial Field Patterns**: Expand the financial field detection pattern as needed
4. **Oracle Metadata**: Consider using Oracle metadata (when available) to determine precise NUMBER precision/scale

## Conclusion

These enhancements significantly improve the reliability of Oracle-to-MySQL data transfers, particularly for financial data with large values like the PAGU column. The solution maintains data integrity while providing graceful error handling for edge cases.