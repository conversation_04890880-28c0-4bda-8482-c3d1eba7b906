"use client";

// ============================================================================
// IMPORTS
// ============================================================================

// React imports
import { useState } from "react";

// UI library imports
import { Card, CardHeader, CardBody, Chip, Button } from "@heroui/react";

// Icon imports
import { Users, Play, Clock, Database } from "lucide-react";

// Animation imports
import { motion } from "framer-motion";

// HTTP client imports
import axios from "axios";

// Type imports
import { JobStatus, SystemStatus } from "@/types/job";

// Component imports
import { JobsTable } from "./JobsTable";
import { SequenceExecutionConfirmModal } from "./SequenceExecutionConfirmModal";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface JobSequence {
  id: string;
  name: string;
  description: string;
  schedule?: string;
  enabled: boolean;
  onFailure: "stop" | "continue" | "retry";
  maxRetries: number;
  jobs: string[];
}

interface EnhancedJobsDisplayProps {
  jobs: JobStatus[];
  sequences: JobSequence[];
  isJobRunning: boolean;
  systemStatus: SystemStatus;
  onRunJob: (jobId: string) => void;
  onCancelJob: (jobId: string) => void;
  onViewJobDetails: (job: JobStatus) => void;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const EnhancedJobsDisplay = ({
  jobs,
  sequences,
  isJobRunning,
  systemStatus,
  onRunJob,
  onCancelJob,
  onViewJobDetails,
}: EnhancedJobsDisplayProps) => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [isSequenceConfirmModalOpen, setIsSequenceConfirmModalOpen] =
    useState(false);
  const [selectedSequenceForExecution, setSelectedSequenceForExecution] =
    useState<{
      id: string;
      name: string;
      description?: string;
      jobCount: number;
    } | null>(null);

  // ============================================================================
  // DATA PROCESSING
  // ============================================================================

  // Group jobs by their sequence assignment
  const groupedJobs = {
    individual: jobs.filter((job) => !job.sequenceConfig),
    sequences: {} as Record<string, JobStatus[]>,
  };

  // Group sequenced jobs by sequence ID
  jobs.forEach((job) => {
    if (job.sequenceConfig) {
      const sequenceId = job.sequenceConfig.sequenceId;
      if (!groupedJobs.sequences[sequenceId]) {
        groupedJobs.sequences[sequenceId] = [];
      }
      groupedJobs.sequences[sequenceId].push(job);
    }
  });

  // Sort jobs within each sequence by their order
  Object.keys(groupedJobs.sequences).forEach((sequenceId) => {
    groupedJobs.sequences[sequenceId].sort(
      (a, b) => (a.sequenceConfig?.order || 0) - (b.sequenceConfig?.order || 0)
    );
  });

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  // Handle sequence execution
  const handleSequenceExecution = (sequenceId: string) => {
    const sequence = sequences.find((seq) => seq.id === sequenceId);
    if (!sequence) return;

    const sequenceJobs = groupedJobs.sequences[sequenceId] || [];
    setSelectedSequenceForExecution({
      id: sequenceId,
      name: sequence.name,
      description: sequence.description,
      jobCount: sequenceJobs.length,
    });
    setIsSequenceConfirmModalOpen(true);
  };

  const executeSequence = async () => {
    if (!selectedSequenceForExecution) return;

    try {
      await axios.post(
        `/api/admin/sequences/${selectedSequenceForExecution.id}/execute`
      );
      console.log(
        `Sequence ${selectedSequenceForExecution.id} execution started`
      );
    } catch (error) {
      console.error("Error executing sequence:", error);
    }
  };

  // ============================================================================
  // RENDER FUNCTIONS
  // ============================================================================

  // Render individual jobs table
  const renderIndividualJobsTable = () => {
    if (groupedJobs.individual.length === 0) {
      return (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <Database className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-semibold">Individual Jobs</h3>
                <Chip color="default" variant="flat" size="sm">
                  0 jobs
                </Chip>
              </div>
            </div>
          </CardHeader>
          <CardBody className="text-center py-8 text-gray-500">
            <p>No individual jobs available.</p>
            <p className="text-sm">All jobs are assigned to sequences.</p>
          </CardBody>
        </Card>
      );
    }

    return (
      <Card className="bg-background border-2 border-slate-100 dark:border-slate-600 rounded-2xl">
        <CardHeader className="pb-[var(--spacing-md)]">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-[var(--spacing-sm)]">
              <Database className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-semibold">Individual Jobs</h3>
              <Chip color="primary" variant="flat" size="sm">
                {groupedJobs.individual.length} jobs
              </Chip>
            </div>
          </div>
        </CardHeader>
        <CardBody className="p-0">
          <JobsTable
            jobs={groupedJobs.individual}
            isJobRunning={isJobRunning}
            systemStatus={systemStatus}
            onRunJob={onRunJob}
            onCancelJob={onCancelJob}
            onViewJobDetails={onViewJobDetails}
            isSequenceTable={false}
          />
        </CardBody>
      </Card>
    );
  };

  // Render sequence table
  const renderSequenceTable = (
    sequenceId: string,
    sequenceJobs: JobStatus[]
  ) => {
    const sequence = sequences.find((seq) => seq.id === sequenceId);
    const sequenceName = sequence?.name || `Sequence ${sequenceId}`;

    return (
      <Card
        key={sequenceId}
        className="bg-background border-2 border-slate-100 dark:border-slate-600 rounded-2xl"
      >
        <CardHeader className="pb-[var(--spacing-md)]">
          <div className="flex items-center justify-between w-full">
            <div className="flex flex-col items-start gap-1">
              <div className="flex items-center gap-[var(--spacing-sm)]">
                <Users className="w-5 h-5 text-purple-600" />
                <h3 className="text-lg font-semibold">{sequenceName}</h3>
                <Chip color="secondary" variant="flat" size="sm">
                  {sequenceJobs.length} jobs
                </Chip>
                {sequence && (
                  <Chip
                    color={sequence.enabled ? "success" : "default"}
                    variant="flat"
                    size="sm"
                  >
                    {sequence.enabled ? "Enabled" : "Disabled"}
                  </Chip>
                )}
              </div>
              {sequence?.description && (
                <p className="text-sm text-muted-foreground ml-7">
                  {sequence.description}
                </p>
              )}
            </div>
            <div className="flex items-center gap-2">
              {sequence && sequence.enabled && (
                <Button
                  size="sm"
                  color="primary"
                  variant="flat"
                  startContent={<Play className="w-3 h-3" />}
                  onPress={() => handleSequenceExecution(sequenceId)}
                >
                  Execute Sequence
                </Button>
              )}
              {sequence?.schedule && (
                <div className="flex items-center gap-1 text-xs text-gray-600">
                  <Clock className="w-3 h-3" />
                  <span className="font-mono">{sequence.schedule}</span>
                  <span className="text-gray-500">(sequence schedule)</span>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
        <CardBody className="p-0">
          <JobsTable
            jobs={sequenceJobs}
            isJobRunning={isJobRunning}
            systemStatus={systemStatus}
            onRunJob={onRunJob}
            onCancelJob={onCancelJob}
            onViewJobDetails={onViewJobDetails}
            sequenceSchedule={sequence?.schedule}
            isSequenceTable={true}
          />
        </CardBody>
      </Card>
    );
  };

  // ============================================================================
  // MAIN RENDER
  // ============================================================================

  return (
    <div className="space-y-6">
      {/* Individual Jobs Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {renderIndividualJobsTable()}
      </motion.div>

      {/* Sequence Tables */}
      {Object.entries(groupedJobs.sequences).map(
        ([sequenceId, sequenceJobs], index) => (
          <motion.div
            key={sequenceId}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: (index + 1) * 0.1 }}
          >
            {renderSequenceTable(sequenceId, sequenceJobs)}
          </motion.div>
        )
      )}

      {/* Empty state when no sequences exist */}
      {Object.keys(groupedJobs.sequences).length === 0 &&
        sequences.length === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardBody className="text-center py-8 text-gray-500">
                <Users className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                <p className="text-lg font-medium">No Job Sequences</p>
                <p className="text-sm">
                  Create job sequences to organize related jobs that should run
                  in order.
                </p>
              </CardBody>
            </Card>
          </motion.div>
        )}

      {/* Sequence Execution Confirmation Modal */}
      <SequenceExecutionConfirmModal
        isOpen={isSequenceConfirmModalOpen}
        onClose={() => setIsSequenceConfirmModalOpen(false)}
        onConfirm={executeSequence}
        sequenceName={selectedSequenceForExecution?.name || ""}
        sequenceDescription={selectedSequenceForExecution?.description}
        jobCount={selectedSequenceForExecution?.jobCount || 0}
      />
    </div>
  );
};
