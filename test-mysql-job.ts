/**
 * Test script for MySQL data source functionality
 * This script tests the MySQL job definition and execution
 */

import { JobDefinition } from "./src/lib/jobManager";

// Test MySQL job definition
const testMySQLJob: JobDefinition = {
  id: "test-mysql-job",
  name: "Test MySQL Data Pull",
  description: "Test job to verify MySQL data source functionality",
  schedule: "0 */5 * * *", // Every 5 minutes for testing
  enabled: true,
  dataSource: {
    type: "mysql",
    mysql: {
      host: process.env.MYSQL_HOST || "localhost",
      port: parseInt(process.env.MYSQL_PORT || "3399"),
      database: process.env.MYSQL_DATABASE || "test_db",
      username: process.env.MYSQL_USERNAME || "test_user",
      password: process.env.MYSQL_PASSWORD || "test_password",
      query: `
        SELECT 
          id,
          name,
          email,
          created_at,
          updated_at
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY created_at DESC
        LIMIT 100
      `,
      connectionLimit: 5,
      acquireTimeout: 30000,
      timeout: 45000,
    },
  },
  destination: {
    type: "local",
    localPath: "./test-output/mysql-data.json",
    options: {
      createDirectory: true,
      preserveStructure: true,
    },
  },
  retryConfig: {
    maxRetries: 3,
    retryDelay: 30,
  },
};

// Function to validate MySQL job definition
function validateMySQLJob(job: JobDefinition): boolean {
  console.log("🔍 Validating MySQL job definition...");

  // Check if it's a MySQL job
  if (job.dataSource.type !== "mysql") {
    console.error("❌ Job is not a MySQL data source");
    return false;
  }

  // Check MySQL configuration
  const mysql = job.dataSource.mysql;
  if (!mysql) {
    console.error("❌ MySQL configuration is missing");
    return false;
  }

  // Validate required fields
  const requiredFields = ["host", "port", "database", "username", "password", "query"];
  for (const field of requiredFields) {
    if (!mysql[field as keyof typeof mysql]) {
      console.error(`❌ Required MySQL field '${field}' is missing`);
      return false;
    }
  }

  // Validate port is a number
  if (typeof mysql.port !== "number" || mysql.port <= 0 || mysql.port > 65535) {
    console.error("❌ MySQL port must be a valid number between 1 and 65535");
    return false;
  }

  // Validate optional fields if present
  if (mysql.connectionLimit && (typeof mysql.connectionLimit !== "number" || mysql.connectionLimit <= 0)) {
    console.error("❌ MySQL connectionLimit must be a positive number");
    return false;
  }

  if (mysql.acquireTimeout && (typeof mysql.acquireTimeout !== "number" || mysql.acquireTimeout <= 0)) {
    console.error("❌ MySQL acquireTimeout must be a positive number");
    return false;
  }

  if (mysql.timeout && (typeof mysql.timeout !== "number" || mysql.timeout <= 0)) {
    console.error("❌ MySQL timeout must be a positive number");
    return false;
  }

  console.log("✅ MySQL job definition is valid");
  return true;
}

// Function to test MySQL connection (mock)
async function testMySQLConnection(job: JobDefinition): Promise<boolean> {
  console.log("🔗 Testing MySQL connection...");

  if (!job.dataSource.mysql) {
    console.error("❌ MySQL configuration not found");
    return false;
  }

  const mysql = job.dataSource.mysql;

  try {
    console.log(`📡 Attempting to connect to MySQL at ${mysql.host}:${mysql.port}/${mysql.database}`);
    console.log(`👤 Using username: ${mysql.username}`);
    console.log(`🔧 Connection settings:`);
    console.log(`   - Connection Limit: ${mysql.connectionLimit || 10}`);
    console.log(`   - Acquire Timeout: ${mysql.acquireTimeout || 60000}ms`);
    console.log(`   - Query Timeout: ${mysql.timeout || 60000}ms`);

    // In a real test, you would actually connect to MySQL here
    // For this test, we'll just validate the configuration format
    console.log("✅ MySQL connection configuration is properly formatted");
    return true;
  } catch (error) {
    console.error("❌ MySQL connection test failed:", error);
    return false;
  }
}

// Function to test SQL query syntax
function testSQLQuery(job: JobDefinition): boolean {
  console.log("📝 Testing SQL query syntax...");

  if (!job.dataSource.mysql?.query) {
    console.error("❌ SQL query is missing");
    return false;
  }

  const query = job.dataSource.mysql.query.trim();

  // Basic SQL validation
  if (!query.toLowerCase().startsWith("select")) {
    console.error("❌ Query must start with SELECT");
    return false;
  }

  if (query.toLowerCase().includes("drop") || 
      query.toLowerCase().includes("delete") || 
      query.toLowerCase().includes("update") ||
      query.toLowerCase().includes("insert")) {
    console.error("❌ Query contains potentially dangerous SQL operations");
    return false;
  }

  console.log("✅ SQL query appears to be safe and valid");
  console.log(`📋 Query preview: ${query.substring(0, 100)}...`);
  return true;
}

// Main test function
async function runMySQLTests(): Promise<void> {
  console.log("🚀 Starting MySQL Data Source Tests");
  console.log("=====================================");

  let allTestsPassed = true;

  // Test 1: Validate job definition
  console.log("\n📋 Test 1: Job Definition Validation");
  if (!validateMySQLJob(testMySQLJob)) {
    allTestsPassed = false;
  }

  // Test 2: Test connection configuration
  console.log("\n🔗 Test 2: Connection Configuration");
  if (!(await testMySQLConnection(testMySQLJob))) {
    allTestsPassed = false;
  }

  // Test 3: Test SQL query
  console.log("\n📝 Test 3: SQL Query Validation");
  if (!testSQLQuery(testMySQLJob)) {
    allTestsPassed = false;
  }

  // Test 4: JSON serialization
  console.log("\n📦 Test 4: JSON Serialization");
  try {
    const serialized = JSON.stringify(testMySQLJob, null, 2);
    const deserialized = JSON.parse(serialized);
    
    if (deserialized.dataSource.type === "mysql" && deserialized.dataSource.mysql) {
      console.log("✅ Job definition can be properly serialized and deserialized");
    } else {
      console.error("❌ Job definition serialization failed");
      allTestsPassed = false;
    }
  } catch (error) {
    console.error("❌ JSON serialization failed:", error);
    allTestsPassed = false;
  }

  // Summary
  console.log("\n📊 Test Summary");
  console.log("================");
  if (allTestsPassed) {
    console.log("🎉 All MySQL data source tests passed!");
    console.log("✅ MySQL integration is ready for use");
  } else {
    console.log("❌ Some tests failed. Please review the implementation.");
  }

  console.log("\n💡 Next Steps:");
  console.log("1. Set up a test MySQL database");
  console.log("2. Configure environment variables");
  console.log("3. Create a test job using the UI");
  console.log("4. Run the job and verify data extraction");
}

// Run tests if this file is executed directly
if (require.main === module) {
  runMySQLTests().catch(console.error);
}

export { testMySQLJob, validateMySQLJob, testMySQLConnection, testSQLQuery };
