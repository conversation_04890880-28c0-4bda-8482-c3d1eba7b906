"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react";
import { Play, AlertTriangle, Users } from "lucide-react";

interface SequenceExecutionConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  sequenceName: string;
  sequenceDescription?: string;
  jobCount: number;
  isLoading?: boolean;
}

export const SequenceExecutionConfirmModal = ({
  isOpen,
  onClose,
  onConfirm,
  sequenceName,
  sequenceDescription,
  jobCount,
  isLoading = false,
}: SequenceExecutionConfirmModalProps) => {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="md"
      classNames={{
        backdrop: "bg-gradient-to-t from-zinc-900 to-zinc-900/10 backdrop-opacity-20"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5 text-purple-600" />
            <span>Execute Sequence</span>
          </div>
        </ModalHeader>
        
        <ModalBody>
          <div className="space-y-4">
            <div className="flex items-start gap-3 p-3 bg-warning-50 border border-warning-200 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-warning-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-warning-800 font-medium text-sm">
                  Manual Sequence Execution
                </p>
                <p className="text-warning-700 text-sm mt-1">
                  You are about to manually execute an entire job sequence. All jobs will run in order.
                </p>
              </div>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Sequence Name:
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {sequenceName}
              </p>
              {sequenceDescription && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {sequenceDescription}
                </p>
              )}
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Jobs in Sequence:
              </p>
              <p className="text-sm text-gray-900 dark:text-gray-100">
                {jobCount} job{jobCount !== 1 ? 's' : ''} will be executed sequentially
              </p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Are you sure you want to execute this sequence manually?
              </p>
            </div>
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button 
            variant="flat" 
            onPress={onClose}
            isDisabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            color="primary" 
            onPress={handleConfirm}
            startContent={<Play className="w-4 h-4" />}
            isLoading={isLoading}
          >
            {isLoading ? "Starting..." : "Execute Sequence"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
