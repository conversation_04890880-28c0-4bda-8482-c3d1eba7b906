import { <PERSON><PERSON>ob<PERSON>and<PERSON> } from "../base/BaseJobHandler";
import type { <PERSON><PERSON><PERSON><PERSON>, JobExecutionContext } from "../types";
import type { JobDefinition } from "../../jobManager";
import { logger } from "../../jobManager";
import fs from "fs";
import path from "path";
import type { Connection } from "mysql2/promise";

/**
 * Job handler for PDF DIPA extraction and processing
 */
export class PdfDipaJobHandler extends BaseJobHandler {
  public readonly jobType = "pdf_dipa";

  /**
   * Execute PDF DIPA job
   */
  public async execute(context: JobExecutionContext): Promise<JobResult> {
    const { jobDefinition } = context;
    const config = jobDefinition.dataSource.pdf_dipa;

    if (!config) {
      throw new Error("PDF DIPA configuration is missing");
    }

    // Validate configuration
    this.validatePdfDipaConfig(config);

    const startTime = await this.logOperationStart(
      context,
      "PDF DIPA extraction",
      `Processing files with status '${config.fileStatusFilter}'`
    );

    try {
      // Import mysql2 for database connections
      const mysql = await import("mysql2/promise");

      // Connect to file metadata database
      const metadataConnection = await mysql.createConnection({
        host: config.fileMetadataDatabase.host,
        port: config.fileMetadataDatabase.port,
        database: config.fileMetadataDatabase.database,
        user: config.fileMetadataDatabase.username,
        password: config.fileMetadataDatabase.password,
      });

      // Connect to destination database for data insertion and error logging
      let destinationConnection: Connection | null = null;
      if (
        jobDefinition.destination.type === "database" &&
        jobDefinition.destination.database
      ) {
        destinationConnection = await mysql.createConnection({
          host: jobDefinition.destination.database.host,
          port: jobDefinition.destination.database.port,
          database: jobDefinition.destination.database.database,
          user: jobDefinition.destination.database.username,
          password: jobDefinition.destination.database.password,
        });

        // Ensure destination table exists
        await this.ensureDestinationTable(
          destinationConnection,
          jobDefinition.destination.database.table
        );
      }

      try {
        // Get PDF files with specified status (default to "NEW" for incremental processing)
        const statusFilter = config.fileStatusFilter || "NEW";
        const [rows] = await metadataConnection.query(
          `SELECT DISTINCT folder, nmfile FROM ${config.fileMetadataDatabase.table}
           WHERE RIGHT(nmfile,3)='pdf' AND status=? ORDER BY folder`,
          [statusFilter]
        );

        const files = rows as Array<{ folder: string; nmfile: string }>;
        await context.addLog(
          `Found ${files.length} PDF files with status '${statusFilter}' to process`
        );

        // Skip processing if status is "OLD" (already processed)
        if (statusFilter === "OLD") {
          await context.addLog(
            "Skipping processing - files with status 'OLD' are already processed"
          );
          return this.createJobResult(0, null);
        }

        let processedCount = 0;
        let errorCount = 0;

        // Process files with progress tracking
        await this.executeWithProgress(
          context,
          files,
          async (file, _index) => {
            try {
              const { folder, nmfile } = file;

              // Parse PDF file
              const pdfContent = await this.parsePdfFile(
                config.sourceDirectory,
                folder,
                nmfile
              );

              if (!pdfContent || !pdfContent.includes("Rp.")) {
                await context.addLog(
                  `Skipping ${nmfile}: content invalid or "Rp." not found`
                );
                return;
              }

              // Extract data from PDF content
              const extractedData = await this.extractPdfData(
                pdfContent,
                nmfile
              );

              // Extract data from filename
              const fileData = this.extractFilenameData(nmfile);

              // Combine extracted data
              const combinedData = {
                ...fileData,
                ...extractedData,
                status: "OLD",
              };

              // Insert data directly to destination database using INSERT IGNORE
              if (destinationConnection && jobDefinition.destination.database) {
                await this.insertDataToDestination(
                  destinationConnection,
                  jobDefinition.destination.database.table,
                  combinedData
                );
              }

              processedCount++;

              // Update file status to 'OLD' after successful processing
              await metadataConnection.query(
                `UPDATE ${config.fileMetadataDatabase.table}
                 SET status = 'OLD'
                 WHERE folder = ? AND nmfile = ?`,
                [folder, nmfile]
              );
            } catch (error) {
              errorCount++;
              const errorMessage =
                error instanceof Error ? error.message : "Unknown error";
              await context.addLog(
                `Error processing ${file.nmfile}: ${errorMessage}`
              );

              // Log error to database if destination connection is available
              if (destinationConnection) {
                await this.saveErrorLog(
                  destinationConnection,
                  config.errorLogTable || "log_ftp.error_logs",
                  file.folder,
                  file.nmfile,
                  error as Error
                );
              }
            }
          },
          "PDF processing",
          10 // Progress update every 10 files
        );

        await context.addLog(
          `PDF DIPA extraction completed. Processed: ${processedCount}, Errors: ${errorCount}`
        );

        await this.logOperationComplete(
          context,
          "PDF DIPA extraction",
          startTime,
          `${processedCount} files processed, ${errorCount} errors`
        );

        // Return result with null data since we handled destination saving directly
        return this.createJobResult(processedCount, null);
      } finally {
        await metadataConnection.end();
        if (destinationConnection) {
          await destinationConnection.end();
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown PDF DIPA error";
      await context.addLog(`PDF DIPA extraction failed: ${errorMessage}`);

      logger.error(`PDF DIPA extraction failed for job ${context.jobId}`, {
        jobId: context.jobId,
        error: errorMessage,
        sourceDirectory: config.sourceDirectory,
        stack: error instanceof Error ? error.stack : undefined,
      });

      throw new Error(`PDF DIPA extraction failed: ${errorMessage}`);
    }
  }

  /**
   * Parse PDF file and extract text content
   */
  private async parsePdfFile(
    sourceDirectory: string,
    folder: string,
    filename: string
  ): Promise<string | null> {
    // Handle both formats: "34/152/2025" (path format) or "341522025" (concatenated format)
    let dept: string, unit: string, year: string;

    if (folder.includes("/")) {
      // Path format: "34/152/2025"
      const parts = folder.split("/");
      if (parts.length !== 3) {
        throw new Error(
          `Folder "${folder}" has invalid path format. Expected "dept/unit/year" like "34/152/2025"`
        );
      }
      dept = parts[0];
      unit = parts[1];
      year = parts[2];
    } else {
      // Concatenated format: "341522025" (fallback for legacy data)
      if (folder.length < 9) {
        throw new Error(
          `Folder "${folder}" is too short (${folder.length} chars). Expected 9 digits: 2 for dept + 3 for unit + 4 for year`
        );
      }
      dept = folder.substring(0, 2); // First 2 digits (e.g., "34")
      unit = folder.substring(2, 5); // Next 3 digits (e.g., "152")
      year = folder.substring(5, 9); // Next 4 digits (e.g., "2025")
    }

    const pdfPath = path.join(sourceDirectory, dept, unit, year, filename);

    try {
      // Dynamic import to avoid initialization issues
      const pdfParse = (await import("pdf-parse")).default;
      const pdfBuffer = fs.readFileSync(pdfPath);
      const data = await pdfParse(pdfBuffer);
      const pages = data.text.split("\f");

      if (pages.length > 0) {
        return pages[0];
      } else {
        return null;
      }
    } catch (error) {
      throw new Error(
        `Error reading or parsing PDF ${filename}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Extract data from PDF content using regex patterns
   */
  private async extractPdfData(
    content: string,
    _filename: string
  ): Promise<{
    pagu: number;
    ds: string;
    kpa: string;
    bendahara: string;
    ppspm: string;
  }> {
    const patterns = {
      pagu: /Sebesar\s*([0-9.]+)/,
      ds: /DS:\s*(\S+)/,
      kpa: /Kuasa Pengguna Anggaran.*?:\s*(.*?)(?=\n|$)/,
      bendahara: /Bendahara Pengeluaran.*?:\s*(.*?)(?=\n|$)/,
      ppspm: /Pejabat Penanda Tangan SPM.*?:\s*(.*?)(?=\n|$)/,
    };

    const results: Record<string, string> = {};
    for (const [key, pattern] of Object.entries(patterns)) {
      const match = content.match(pattern);
      results[key] = match ? match[1].trim() : "";
    }

    // Process pagu (budget amount)
    let paguValue = 0;
    if (results.pagu) {
      const cleanPagu = results.pagu.replace(/\./g, "");
      paguValue = parseFloat(cleanPagu);
    }

    if (isNaN(paguValue) || !paguValue) {
      paguValue = 0;
    }

    return {
      pagu: paguValue,
      ds: results.ds || "",
      kpa: results.kpa || "",
      bendahara: results.bendahara || "",
      ppspm: results.ppspm || "",
    };
  }

  /**
   * Extract data from filename
   */
  private extractFilenameData(filename: string): {
    kdjendok: string;
    kddept: string;
    kdunit: string;
    kdsatker: string;
    kddekon: string;
    norev: string;
  } {
    return {
      kdjendok: filename.slice(5, 7),
      kddept: filename.slice(8, 11),
      kdunit: filename.slice(12, 14),
      kdsatker: filename.slice(15, 21),
      kddekon: filename.slice(29, 30),
      norev: filename.slice(31, 33),
    };
  }

  /**
   * Save error logs to database
   */
  private async saveErrorLog(
    connection: Connection,
    tableName: string,
    folder: string,
    filename: string,
    error: Error
  ): Promise<void> {
    try {
      // First, try to ensure the log_ftp database and table exist
      if (tableName.includes("log_ftp.")) {
        await connection.query("CREATE DATABASE IF NOT EXISTS log_ftp");
        await connection.query(`
          CREATE TABLE IF NOT EXISTS ${tableName} (
            id BIGINT PRIMARY KEY AUTO_INCREMENT,
            folder VARCHAR(255) NOT NULL,
            nmfile VARCHAR(255) NOT NULL,
            error_message TEXT NOT NULL,
            stack_trace TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_error_logs_folder (folder),
            INDEX idx_error_logs_nmfile (nmfile),
            INDEX idx_error_logs_created_at (created_at)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
      }

      const query = `
        INSERT INTO ${tableName} (folder, nmfile, error_message, stack_trace)
        VALUES (?, ?, ?, ?)
      `;
      const params = [folder, filename, error.message, error.stack || null];
      await connection.query(query, params);

      console.log(`Error logged for file: ${filename} in table: ${tableName}`);
    } catch (logError) {
      // If we can't log the error, at least log it to console
      console.error(`Failed to save error log for file: ${filename}`, logError);
      console.error(`Original error was:`, error.message);
    }
  }

  /**
   * Ensure destination table exists and create it if necessary
   */
  private async ensureDestinationTable(
    connection: Connection,
    tableName: string
  ): Promise<void> {
    try {
      // Check if table exists
      const [tableExistsResult] = await connection.query(
        `SELECT COUNT(*) as count FROM information_schema.tables
         WHERE table_schema = DATABASE() AND table_name = ?`,
        [tableName]
      );

      const tableExists =
        (tableExistsResult as Array<{ count: number }>)[0]?.count > 0;

      if (!tableExists) {
        // Create table with schema for PDF DIPA data
        await connection.query(`
          CREATE TABLE IF NOT EXISTS \`${tableName}\` (
            id BIGINT PRIMARY KEY AUTO_INCREMENT,
            kdjendok VARCHAR(10) NOT NULL,
            kddept VARCHAR(10) NOT NULL,
            kdunit VARCHAR(10) NOT NULL,
            kdsatker VARCHAR(20) NOT NULL,
            kddekon VARCHAR(10) NOT NULL,
            norev VARCHAR(10) NOT NULL,
            pagu DECIMAL(15,2) DEFAULT 0,
            ds VARCHAR(255) DEFAULT '',
            kpa TEXT DEFAULT '',
            bendahara TEXT DEFAULT '',
            ppspm TEXT DEFAULT '',
            status VARCHAR(10) DEFAULT 'OLD',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_pdf_record (kdjendok, kddept, kdunit, kdsatker, kddekon, norev),
            INDEX idx_kdsatker (kdsatker),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log(`Created destination table: ${tableName}`);
      }
    } catch (error) {
      console.error(`Failed to ensure destination table ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Insert data to destination table using INSERT IGNORE to handle duplicates
   */
  private async insertDataToDestination(
    connection: Connection,
    tableName: string,
    data: Record<string, unknown>
  ): Promise<void> {
    try {
      const columns = Object.keys(data);
      const values = Object.values(data);
      const placeholders = columns.map(() => "?").join(", ");

      const insertSQL = `
        INSERT IGNORE INTO \`${tableName}\` (\`${columns.join("`, `")}\`)
        VALUES (${placeholders})
      `;

      await connection.execute(insertSQL, values);
    } catch (error) {
      console.error(`Failed to insert data to ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Validate PDF DIPA job configuration
   */
  public validateConfig(jobDef: JobDefinition): boolean {
    if (!super.validateConfig(jobDef)) {
      return false;
    }

    const config = jobDef.dataSource.pdf_dipa;
    if (!config) {
      return false;
    }

    try {
      this.validatePdfDipaConfig(config);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get required permissions for PDF DIPA operations
   */
  public getRequiredPermissions(): string[] {
    return ["filesystem:read", "database:read", "database:write"];
  }

  /**
   * Validate PDF DIPA configuration fields
   */
  private validatePdfDipaConfig(
    config: NonNullable<JobDefinition["dataSource"]["pdf_dipa"]>
  ): void {
    this.validateRequiredFields(
      config,
      ["sourceDirectory", "fileMetadataDatabase"],
      "PDF DIPA configuration"
    );

    // Validate file metadata database configuration
    this.validateRequiredFields(
      config.fileMetadataDatabase,
      ["host", "port", "database", "username", "password", "table"],
      "File metadata database configuration"
    );

    // Additional validations
    if (
      typeof config.fileMetadataDatabase.port !== "number" ||
      config.fileMetadataDatabase.port <= 0 ||
      config.fileMetadataDatabase.port > 65535
    ) {
      throw new Error(
        "File metadata database port must be a valid number between 1 and 65535"
      );
    }

    if (!config.sourceDirectory.trim()) {
      throw new Error("Source directory cannot be empty");
    }

    // Check if source directory exists
    if (!fs.existsSync(config.sourceDirectory)) {
      throw new Error(
        `Source directory does not exist: ${config.sourceDirectory}`
      );
    }
  }
}
