# Data Bot Dashboard Setup Script for Windows
# Run this script in PowerShell

Write-Host "🚀 Setting up Data Bot Dashboard for Oracle & SFTP..." -ForegroundColor Green

# Create .env file if it doesn't exist
if (-not (Test-Path ".env")) {
    Write-Host "📝 Creating .env file from template..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "✅ .env file created. Please edit it with your actual credentials." -ForegroundColor Green
} else {
    Write-Host "⚠️  .env file already exists. Please update it with new Oracle and SFTP variables." -ForegroundColor Yellow
}

# Create data directories
Write-Host "📁 Creating data directories..." -ForegroundColor Yellow
$directories = @(
    "data\customer_imports",
    "data\inventory", 
    "data\analytics",
    "data\processed",
    "data\downloads",
    "logs"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "  Created: $dir" -ForegroundColor Gray
    }
}

Write-Host "✅ Directory structure created." -ForegroundColor Green

# Install optional dependencies for production
Write-Host "📦 Installing optional dependencies..." -ForegroundColor Yellow
Write-Host "ℹ️  Run the following commands to install Oracle and SFTP support:" -ForegroundColor Cyan
Write-Host ""
Write-Host "npm install oracledb ssh2-sftp-client fs-extra" -ForegroundColor White
Write-Host "npm install --save-dev @types/ssh2-sftp-client @types/fs-extra" -ForegroundColor White
Write-Host ""

# Oracle Instant Client installation info
Write-Host "🔧 Oracle Instant Client Installation:" -ForegroundColor Yellow
Write-Host "For Oracle connectivity, you'll need Oracle Instant Client:" -ForegroundColor Cyan
Write-Host ""
Write-Host "# Windows:" -ForegroundColor White
Write-Host "1. Download Oracle Instant Client from:" -ForegroundColor White
Write-Host "   https://www.oracle.com/database/technologies/instant-client/winx64-64-downloads.html" -ForegroundColor White
Write-Host "2. Extract to C:\oracle\instantclient_XX_X" -ForegroundColor White
Write-Host "3. Add the path to your PATH environment variable" -ForegroundColor White
Write-Host "4. Set ORACLE_HOME environment variable (optional)" -ForegroundColor White
Write-Host ""

Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Edit .env file with your Oracle and SFTP credentials" -ForegroundColor White
Write-Host "2. Install optional dependencies (see above)" -ForegroundColor White
Write-Host "3. Install Oracle Instant Client (if using Oracle)" -ForegroundColor White
Write-Host "4. Run 'npm run migrate' to set up database with production-ready job templates" -ForegroundColor White
Write-Host "5. Run 'npm run dev' to start the development server" -ForegroundColor White
Write-Host "6. Configure and enable specific jobs through the web interface" -ForegroundColor White
Write-Host ""
Write-Host "📖 See ORACLE_SFTP_CONFIG.md for detailed configuration instructions." -ForegroundColor Cyan
Write-Host ""
Write-Host "🎉 Setup complete!" -ForegroundColor Green

# Pause to allow user to read the output
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
