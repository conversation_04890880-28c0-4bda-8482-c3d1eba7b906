/**
 * Simple SFTP test using ssh2 library
 * Downloads a specific PDF file from the SFTP server
 */

import * as fs from "fs";
import * as path from "path";
import { Client } from "ssh2";
import * as crypto from "crypto";

// SFTP Configuration
const sftpConfig = {
  host: "aksesdata-anggaran.kemenkeu.go.id",
  port: 54321,
  username: "PA_DJPBN",
  password: "Sinergi100Persen",
};

// File to download
const remoteFilePath =
  "/adk_rkakl2025/01/001/2025/2025-01-004.01-450774-06-001-2-00.pdf";
const localDirectory = "C:/data/tarif-pdf-downloads/test";
const fileName = path.basename(remoteFilePath);
const localFilePath = path.join(localDirectory, fileName);

interface DownloadResult {
  success: boolean;
  filePath?: string;
  fileSize?: number;
  downloadTime?: number;
  hash?: string;
  error?: string;
}

function downloadFile(): Promise<DownloadResult> {
  return new Promise((resolve) => {
    const conn = new Client();
    const startTime = Date.now();

    conn.on("ready", () => {
      console.log("✅ SSH connection established");

      conn.sftp((err, sftp) => {
        if (err) {
          console.error("❌ SFTP session creation failed:", err.message);
          conn.end();
          return resolve({
            success: false,
            error: `SFTP session creation failed: ${err.message}`,
          });
        }

        console.log("✅ SFTP session created");
        console.log(`📁 Checking remote file: ${remoteFilePath}`);

        // Check if remote file exists first
        sftp.stat(remoteFilePath, (statErr, stats) => {
          if (statErr) {
            console.error("❌ Remote file not found:", statErr.message);
            conn.end();
            return resolve({
              success: false,
              error: `Remote file not found: ${statErr.message}`,
            });
          }

          console.log(`✅ Remote file found - Size: ${stats.size} bytes`);

          // Create local directory if it doesn't exist
          if (!fs.existsSync(localDirectory)) {
            fs.mkdirSync(localDirectory, { recursive: true });
            console.log(`✅ Created local directory: ${localDirectory}`);
          }

          console.log(`⬇️  Starting download to: ${localFilePath}`);

          // Create write stream for the local file
          const writeStream = fs.createWriteStream(localFilePath);

          // Create read stream from SFTP
          const readStream = sftp.createReadStream(remoteFilePath);

          readStream.on("error", (streamErr: Error) => {
            console.error("❌ Download stream error:", streamErr.message);
            writeStream.close();
            conn.end();

            // Clean up partial file
            if (fs.existsSync(localFilePath)) {
              fs.unlinkSync(localFilePath);
            }

            resolve({
              success: false,
              error: `Download stream error: ${streamErr.message}`,
            });
          });

          writeStream.on("error", (writeErr) => {
            console.error("❌ Write stream error:", writeErr.message);
            readStream.destroy();
            conn.end();

            // Clean up partial file
            if (fs.existsSync(localFilePath)) {
              fs.unlinkSync(localFilePath);
            }

            resolve({
              success: false,
              error: `Write stream error: ${writeErr.message}`,
            });
          });

          writeStream.on("finish", () => {
            const endTime = Date.now();
            const downloadTime = endTime - startTime;

            conn.end();

            try {
              // Verify local file
              const localStats = fs.statSync(localFilePath);
              console.log(`✅ Download completed!`);
              console.log(`   Local file size: ${localStats.size} bytes`);
              console.log(`   Download time: ${downloadTime}ms`);

              // Calculate file hash
              const fileBuffer = fs.readFileSync(localFilePath);
              const hash = crypto
                .createHash("md5")
                .update(fileBuffer)
                .digest("hex");
              console.log(`   File hash (MD5): ${hash}`);

              // Verify file sizes match
              if (localStats.size !== stats.size) {
                resolve({
                  success: false,
                  error: `File size mismatch: remote=${stats.size}, local=${localStats.size}`,
                });
                return;
              }

              resolve({
                success: true,
                filePath: localFilePath,
                fileSize: localStats.size,
                downloadTime,
                hash,
              });
            } catch (verifyErr) {
              const errorMessage =
                verifyErr instanceof Error
                  ? verifyErr.message
                  : String(verifyErr);
              resolve({
                success: false,
                error: `File verification failed: ${errorMessage}`,
              });
            }
          });

          // Pipe the read stream to write stream
          readStream.pipe(writeStream);
        });
      });
    });

    conn.on("error", (connErr) => {
      console.error("❌ SSH connection error:", connErr.message);
      resolve({
        success: false,
        error: `SSH connection error: ${connErr.message}`,
      });
    });

    console.log("🔗 Connecting to SFTP server...");
    console.log(`   Host: ${sftpConfig.host}:${sftpConfig.port}`);
    console.log(`   User: ${sftpConfig.username}`);

    conn.connect(sftpConfig);
  });
}

// Test connection only
function testConnection(): Promise<boolean> {
  return new Promise((resolve) => {
    const conn = new Client();

    conn.on("ready", () => {
      console.log("✅ SSH connection test successful");

      conn.sftp((err, sftp) => {
        if (err) {
          console.error("❌ SFTP session creation failed:", err.message);
          conn.end();
          return resolve(false);
        }

        console.log("✅ SFTP session test successful");

        // List parent directory
        const parentDir = path.dirname(remoteFilePath);
        console.log(`📁 Testing directory listing: ${parentDir}`);

        sftp.readdir(parentDir, (listErr, list) => {
          conn.end();

          if (listErr) {
            console.error("❌ Directory listing failed:", listErr.message);
            return resolve(false);
          }

          console.log(
            `✅ Directory listing successful - Found ${list.length} items`
          );

          // Look for our target file
          const targetFile = list.find((item) => item.filename === fileName);
          if (targetFile) {
            console.log(
              `✅ Target file found: ${targetFile.filename} (${targetFile.attrs.size} bytes)`
            );
          } else {
            console.log(`⚠️  Target file '${fileName}' not found in directory`);
          }

          resolve(true);
        });
      });
    });

    conn.on("error", (connErr) => {
      console.error("❌ SSH connection test failed:", connErr.message);
      resolve(false);
    });

    console.log("🧪 Testing SFTP connection...");
    conn.connect(sftpConfig);
  });
}

// Main execution
async function main() {
  console.log("=".repeat(60));
  console.log("🚀 SFTP Single File Download Test (using ssh2)");
  console.log("=".repeat(60));

  try {
    // Test connection first
    const connectionOk = await testConnection();

    if (!connectionOk) {
      console.log("❌ Connection test failed. Aborting download.");
      process.exit(1);
    }

    console.log("\n" + "=".repeat(60));
    console.log("📥 Starting File Download");
    console.log("=".repeat(60));

    // Download the file
    const result = await downloadFile();

    console.log("\n" + "=".repeat(60));
    console.log("📊 Download Summary");
    console.log("=".repeat(60));

    if (result.success) {
      console.log("✅ Status: SUCCESS");
      console.log(`📁 File: ${result.filePath}`);
      console.log(`📏 Size: ${result.fileSize} bytes`);
      console.log(`⏱️  Time: ${result.downloadTime}ms`);
      console.log(`🔐 Hash: ${result.hash}`);
    } else {
      console.log("❌ Status: FAILED");
      console.log(`💥 Error: ${result.error}`);
      process.exit(1);
    }
  } catch (error) {
    console.error("💥 Unexpected error:", error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

export { downloadFile, testConnection, sftpConfig };
