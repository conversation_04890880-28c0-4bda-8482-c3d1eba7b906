# SFTP File Transfer Database Schema

This document describes the database schema and workflow for tracking SFTP file transfers in the Sintesa Data Puller application.

## Overview

The system downloads files/folders from SFTP servers to local directories and tracks all file transfer details in a MySQL database. This provides complete visibility into what files were downloaded, when, and their processing status.

## Database Schema

### Table: `sftp_file_transfers`

This table tracks every file transferred from SFTP to local storage.

```sql
CREATE TABLE sftp_file_transfers (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  job_id VARCHAR(36) NOT NULL,
  execution_id VARCHAR(100) NOT NULL,
  remote_file_path VARCHAR(1000) NOT NULL,
  remote_file_name VARCHAR(500) NOT NULL,
  local_file_path VARCHAR(1000) NOT NULL,
  local_directory VARCHAR(500) NOT NULL,
  file_size_bytes BIGINT NULL,
  file_hash VARCHAR(64) NULL,
  transfer_status ENUM('pending', 'downloading', 'completed', 'failed', 'skipped') NOT NULL DEFAULT 'pending',
  transfer_start_time TIMESTAMP NULL,
  transfer_end_time TIMESTAMP NULL,
  transfer_duration_ms INT NULL,
  error_message TEXT NULL,
  file_modified_date TIMESTAMP NULL,
  file_created_date TIMESTAMP NULL,
  processing_status ENUM('not_processed', 'processing', 'processed', 'failed_processing') DEFAULT 'not_processed',
  processing_notes TEXT NULL,
  metadata JSON NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (job_id) REFERENCES job_definitions(id) ON DELETE CASCADE,
  FOREIGN KEY (execution_id) REFERENCES job_executions(id) ON DELETE CASCADE,

  INDEX idx_sftp_transfers_job_id (job_id),
  INDEX idx_sftp_transfers_execution_id (execution_id),
  INDEX idx_sftp_transfers_status (transfer_status),
  INDEX idx_sftp_transfers_processing_status (processing_status),
  INDEX idx_sftp_transfers_remote_file (remote_file_name),
  INDEX idx_sftp_transfers_created_at (created_at),
  UNIQUE KEY unique_execution_remote_file (execution_id, remote_file_path)
);
```

## Field Descriptions

### Core Identification

- **id**: Auto-incrementing primary key
- **job_id**: Links to the job definition that initiated the transfer
- **execution_id**: Links to the specific job execution instance

### File Information

- **remote_file_path**: Full path to the file on the SFTP server
- **remote_file_name**: Just the filename (extracted from path)
- **local_file_path**: Full path where the file was saved locally
- **local_directory**: Directory where the file was saved
- **file_size_bytes**: Size of the file in bytes
- **file_hash**: MD5/SHA256 hash for integrity verification

### Transfer Tracking

- **transfer_status**: Current status of the file transfer

  - `pending`: Queued for download
  - `downloading`: Currently being transferred
  - `completed`: Successfully downloaded
  - `failed`: Transfer failed
  - `skipped`: File was skipped (already exists, etc.)

- **transfer_start_time**: When the download started
- **transfer_end_time**: When the download completed/failed
- **transfer_duration_ms**: Transfer time in milliseconds
- **error_message**: Details if transfer failed

### File Metadata

- **file_modified_date**: Last modified date from SFTP server
- **file_created_date**: Creation date from SFTP server
- **metadata**: JSON field for additional file properties

### Processing Status

- **processing_status**: Status of post-download processing

  - `not_processed`: File downloaded but not processed
  - `processing`: Currently being processed
  - `processed`: Successfully processed
  - `failed_processing`: Processing failed

- **processing_notes**: Additional processing information

### Timestamps

- **created_at**: When the record was created
- **updated_at**: When the record was last modified

## Workflow Example

### 1. Job Configuration

```typescript
{
  id: "2",
  name: "Tarif Data PDF",
  description: "Download tarif data PDF files from SFTP server",
  schedule: "30 3 * * *", // Daily at 3:30 AM
  enabled: true,
  dataSource: {
    type: "sftp",
    sftp: {
      host: "aksesdata-anggaran.kemenkeu.go.id",
      port: 54321,
      username: "PA_DJPBN",
      password: "Sinergi100Persen",
      remotePath: "/exports/tarif_data/",
      filePattern: "*.pdf",
    },
  },
  destination: {
    type: "local",
    localPath: "C:/data/tarif-pdf-downloads",
    options: {
      createDirectory: true,
      preserveStructure: true,
      trackInDatabase: true,
    },
  },
}
```

### 2. File Transfer Process

1. **Job Execution Starts**

   - Creates a new `job_executions` record
   - Scans SFTP directory for files matching pattern

2. **For Each File Found**

   ```typescript
   // Create transfer record
   const transferId = await createSftpFileTransfer({
     jobId: "2",
     executionId: "2-1642345678901",
     remoteFilePath: "/exports/tarif_data/tarif_2025_01.pdf",
     remoteFileName: "tarif_2025_01.pdf",
     localFilePath: "C:/data/tarif-pdf-downloads/tarif_2025_01.pdf",
     localDirectory: "C:/data/tarif-pdf-downloads",
     transferStatus: "pending",
     processingStatus: "not_processed",
   });
   ```

3. **Download Process**

   ```typescript
   // Update status to downloading
   await updateSftpFileTransfer(transferId, {
     transferStatus: "downloading",
     transferStartTime: new Date(),
   });

   // Perform download...

   // Mark as completed
   await markTransferCompleted(transferId, fileSizeBytes, durationMs, fileHash);
   ```

4. **Query Transfer Results**

   ```typescript
   // Get all transfers for this execution
   const transfers = await getSftpFileTransfersByExecution("2-1642345678901");

   // Get summary statistics
   const summary = await getSftpTransferSummary("2-1642345678901");

   // Get files that need processing
   const toProcess = await getFilesForProcessing("2", 10);
   ```

## API Endpoints

### GET /api/sftp/transfers

Query parameters:

- `action`: Required. One of: `by-execution`, `by-job`, `summary`, `for-processing`
- `executionId`: Required for `by-execution` and `summary` actions
- `jobId`: Required for `by-job` action, optional for `for-processing`
- `limit`: Optional. Number of records to return (default varies by action)

Examples:

```bash
# Get all transfers for a specific execution
GET /api/sftp/transfers?action=by-execution&executionId=2-1642345678901

# Get recent transfers for a job
GET /api/sftp/transfers?action=by-job&jobId=2&limit=50

# Get transfer summary for an execution
GET /api/sftp/transfers?action=summary&executionId=2-1642345678901

# Get files that need processing
GET /api/sftp/transfers?action=for-processing&jobId=2&limit=10
```

## Usage in Job Runner

The SFTP file manager should be integrated into your job runner to:

1. Create transfer records before starting downloads
2. Update transfer status during downloads
3. Track file metadata and processing status
4. Provide visibility into transfer history and statistics

This schema provides complete audit trail and operational visibility for your SFTP file transfer operations.
