import { initializeCronScheduler, resetCronScheduler } from "./cronScheduler";
import { logger, initializeJobManager } from "./jobManager";
import { initializeDefaultSettings } from "./configPersistence";

let isStarted = false;

export async function startDataBotServices(): Promise<void> {
  if (isStarted) {
    logger.info("Data Bot services already started");
    return;
  }

  try {
    logger.info("Starting Data Bot services...");

    // Create logs directory if it doesn't exist
    const fs = await import("fs");
    const path = await import("path");

    const logsDir = path.join(process.cwd(), "logs");
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
      logger.info("Created logs directory");
    }

    // Initialize job manager with system database (pulltest)
    await initializeJobManager();

    // Initialize default settings
    await initializeDefaultSettings();

    // Initialize cron scheduler
    await initializeCronScheduler();

    isStarted = true;
    logger.info("Data Bot services started successfully");
  } catch (error) {
    logger.error("Failed to start Data Bot services", {
      error: error instanceof Error ? error.message : "Unknown error",
    });
    throw error;
  }
}

export async function stopDataBotServices(): Promise<void> {
  if (!isStarted) {
    logger.info("Data Bot services already stopped");
    return;
  }

  try {
    logger.info("Stopping Data Bot services...");

    // Stop all cron jobs and reset scheduler state
    resetCronScheduler();

    // Clear all running job states
    const { clearAllRunningJobs } = await import("./jobRunner");
    clearAllRunningJobs();

    // Clear all active sequence states
    const JobSequenceManager = (await import("./jobSequenceManager")).default;
    const sequenceManager = JobSequenceManager.getInstance();
    sequenceManager.clearAllActiveSequences();

    // Don't close database connections - keep them available for restart
    // await closeDatabase();

    isStarted = false;
    logger.info("Data Bot services stopped successfully");
  } catch (error) {
    logger.error("Failed to stop Data Bot services", {
      error: error instanceof Error ? error.message : "Unknown error",
    });
    throw error;
  }
}

export function isDataBotStarted(): boolean {
  return isStarted;
}
