export interface JobStatus {
  id: string;
  name: string;
  status: "running" | "completed" | "failed" | "scheduled" | "stopped";
  lastRun: Date;
  nextRun: Date;
  duration: number;
  logs: string[];
  enabled: boolean;
  lastRunStatus?: "completed" | "failed" | "cancelled";
  lastRunTriggerType?: "manual" | "automatic";
  sequenceConfig?: {
    sequenceId: string;
    order: number;
  };
  parentSequence?: {
    id: string;
    name: string;
    enabled: boolean;
  };
}

export interface SystemStatus {
  status: "running" | "stopped";
  message: string;
}

export type JobAction = "run" | "stop" | "cancel" | "start";

export type DisplayStatus =
  | "running"
  | "scheduled"
  | "disabled"
  | "service-stopped";

// Sequence-related types
export interface SequenceStatus {
  id: string;
  name: string;
  status: "running" | "completed" | "failed" | "stopped" | "scheduled";
  currentJob?: string;
  currentJobOrder?: number;
  totalJobs: number;
  lastRun?: Date;
  nextRun?: Date;
  duration: number;
  enabled: boolean;
  onFailure: "stop" | "continue" | "retry";
}

export type SequenceAction = "run" | "stop" | "cancel" | "start";
