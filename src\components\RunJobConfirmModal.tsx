"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react";
import { Play, AlertTriangle } from "lucide-react";
import { JobStatus } from "@/types/job";

interface RunJobConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  job: JobStatus | null;
  isLoading?: boolean;
}

export const RunJobConfirmModal = ({
  isOpen,
  onClose,
  onConfirm,
  job,
  isLoading = false,
}: RunJobConfirmModalProps) => {
  if (!job) return null;

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="md"
      backdrop="blur"
      isDismissable={!isLoading}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Play className="w-5 h-5 text-success" />
            <span>Confirm Manual Job Run</span>
          </div>
        </ModalHeader>
        
        <ModalBody>
          <div className="space-y-4">
            <div className="flex items-start gap-3 p-3 bg-warning-50 border border-warning-200 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-warning-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-warning-800 font-medium text-sm">
                  Manual Job Execution
                </p>
                <p className="text-warning-700 text-sm mt-1">
                  You are about to manually run this job outside of its scheduled time.
                </p>
              </div>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Job Name:
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {job.name}
              </p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Are you sure you want to run this job manually?
              </p>
            </div>
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button 
            variant="flat" 
            onPress={onClose}
            isDisabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            color="success" 
            onPress={handleConfirm}
            startContent={<Play className="w-4 h-4" />}
            isLoading={isLoading}
          >
            {isLoading ? "Starting..." : "Run Job"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
