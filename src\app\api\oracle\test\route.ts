import { NextRequest, NextResponse } from "next/server";
import { executeOracleQuery } from "@/tarikData/db/Oracle.js";
import { logger } from "@/lib/jobManager";

export async function POST(request: NextRequest) {
  try {
    const { query, maxRows = 10 } = await request.json();

    if (!query) {
      return NextResponse.json(
        { error: "SQL query is required" },
        { status: 400 }
      );
    }

    logger.info("Testing Oracle connection with query", {
      query: query.substring(0, 100) + "...",
    });

    // Test the Oracle connection with the provided query
    const result = await executeOracleQuery(query, [], {
      maxRows: Math.min(maxRows, 100), // Limit to prevent large responses
    });

    const recordCount = result.rows ? result.rows.length : 0;

    logger.info("Oracle test query executed successfully", {
      recordCount,
      metaData: result.metaData?.length || 0,
    });

    return NextResponse.json({
      success: true,
      recordCount,
      columns: result.metaData?.map((col: { name: string }) => col.name) || [],
      data: result.rows || [],
      message: `Successfully retrieved ${recordCount} records`,
    });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    logger.error("Oracle test connection failed", {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        message: "Failed to connect to Oracle database",
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: "Oracle Test API",
    usage: "POST with { query: 'SELECT * FROM your_table WHERE ROWNUM <= 10' }",
  });
}
