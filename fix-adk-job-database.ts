// Fix ADK Processing Job - Correct the database configuration
import { initializeDatabase } from './src/lib/database';
import { loadJobDefinition, saveJobDefinition } from './src/lib/jobPersistence';

async function fixAdkJobDatabase() {
  try {
    console.log('🔧 Fixing ADK Processing Job Database Configuration...\n');
    
    console.log('Initializing database...');
    await initializeDatabase();
    
    // Job 6 is the ADK Processing job
    const jobId = '6';
    console.log(`Loading job: ${jobId}`);
    const job = await loadJobDefinition(jobId);
    
    if (!job) {
      console.log(`❌ Job ${jobId} not found`);
      return;
    }
    
    console.log(`📄 Current job: ${job.name}`);
    console.log(`Current destination config:`, JSON.stringify(job.destination, null, 2));
    
    // Fix the destination configuration with correct databases
    const updatedJob = {
      ...job,
      destination: {
        type: "database" as const,
        database: {
          type: "mysql" as const,
          host: "localhost",
          port: 3306,
          database: "dbdipa25", // ✅ Correct: ADK data goes to dbdipa25
          username: "root",
          password: "",
          table: "d_item", // Default table, actual tables determined by XML file mapping
          schema: undefined
        },
        fileTracking: {
          enabled: true,
          database: {
            host: "localhost",
            port: 3306,
            username: "root", 
            password: "",
            database: "monev2025", // ✅ Correct: File tracking uses monev2025
            table: "file_metadata"
          }
        }
      }
    };
    
    console.log(`\n🔧 Updated destination config:`);
    console.log(`   - ADK data destination: dbdipa25 database`);
    console.log(`   - File tracking: monev2025.file_metadata table`);
    console.log(JSON.stringify(updatedJob.destination, null, 2));
    
    // Save the updated job
    console.log(`\n💾 Saving corrected job configuration...`);
    await saveJobDefinition(updatedJob);
    
    console.log(`✅ Job configuration corrected successfully!`);
    
    // Verify the fix by loading and validating again
    console.log(`\n🧪 Verifying the fix...`);
    const { jobHandlerFactory } = await import('./src/lib/jobs');
    const handler = jobHandlerFactory.getHandler('adk_processing');
    const isValid = handler.validateConfig(updatedJob);
    
    console.log(`Configuration now valid: ${isValid}`);
    
    if (isValid) {
      console.log('🎉 ADK Processing job is now properly configured!');
      console.log('\n📋 Configuration summary:');
      console.log('   ✅ File tracking: monev2025.file_metadata (for tracking processed files)');
      console.log('   ✅ ADK data storage: dbdipa25 (for d_akun, d_item, etc. tables)');
      console.log('   ✅ All required configurations present');
      console.log('\n💡 The job should now run without the "File tracking configuration is required" error');
    } else {
      console.log('❌ Configuration is still invalid. Please check the job definition.');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error fixing job:', error);
    process.exit(1);
  }
}

fixAdkJobDatabase();
