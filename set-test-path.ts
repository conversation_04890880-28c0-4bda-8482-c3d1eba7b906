// <PERSON>ript to set a test path for job 4
import { saveJobDefinition, loadJobDefinition } from "./src/lib/jobPersistence";
import { initializeDatabase } from "./src/lib/database";

async function setTestPath() {
  try {
    console.log("Initializing database...");
    await initializeDatabase();

    console.log("Loading current job 4 definition...");
    const job4 = await loadJobDefinition("4");

    if (!job4) {
      console.error("Job 4 not found in database");
      process.exit(1);
    }

    // Get test path from command line arguments
    const testPath = process.argv[2];
    
    if (!testPath) {
      console.log("Usage: npx tsx set-test-path.ts <test-path>");
      console.log("Examples:");
      console.log("  npx tsx set-test-path.ts 34/152/2025  # Test specific path");
      console.log("  npx tsx set-test-path.ts 34           # Test all subfolders in folder 34");
      console.log("  npx tsx set-test-path.ts clear        # Clear test mode (process all folders)");
      process.exit(1);
    }

    console.log(`Current configuration:`);
    console.log(`- Name: ${job4.name}`);
    console.log(`- Test Path: ${job4.dataSource.options?.testPath || 'None (full scan)'}`);

    if (testPath === 'clear') {
      // Remove test path to go back to full scan mode
      if (job4.dataSource.options?.testPath) {
        delete job4.dataSource.options.testPath;
        console.log("\n✅ Test mode cleared - job will now process all folders");
      } else {
        console.log("\n⚠️  Test mode was not set");
      }
    } else {
      // Validate test path format
      const pathParts = testPath.split('/');
      if (pathParts.length < 1 || pathParts.length > 3) {
        console.error("❌ Invalid test path format. Use: 2digit, 2digit/3digit, or 2digit/3digit/year");
        process.exit(1);
      }

      // Validate 2-digit folder
      if (!/^[0-9]{2}$/.test(pathParts[0])) {
        console.error("❌ First part must be a 2-digit number (e.g., 34)");
        process.exit(1);
      }

      // Validate 3-digit folder if provided
      if (pathParts.length > 1 && !/^[0-9]{3}$/.test(pathParts[1])) {
        console.error("❌ Second part must be a 3-digit number (e.g., 152)");
        process.exit(1);
      }

      // Validate year if provided
      if (pathParts.length > 2 && !/^20[0-9]{2}$/.test(pathParts[2])) {
        console.error("❌ Third part must be a year (e.g., 2025)");
        process.exit(1);
      }

      // Set the test path
      if (!job4.dataSource.options) {
        job4.dataSource.options = {};
      }
      job4.dataSource.options.testPath = testPath;

      console.log(`\n✅ Test path set to: ${testPath}`);
      console.log("This will limit the job to process only:");
      
      if (pathParts.length === 1) {
        console.log(`- All subfolders and years in folder ${pathParts[0]}`);
      } else if (pathParts.length === 2) {
        console.log(`- All years in folder ${pathParts[0]}/${pathParts[1]}`);
      } else {
        console.log(`- Only folder ${pathParts[0]}/${pathParts[1]}/${pathParts[2]}`);
      }
    }

    console.log("\nSaving job configuration...");
    await saveJobDefinition(job4);

    console.log("✅ Job 4 updated successfully!");
    console.log("\nUpdated configuration:");
    console.log(`- Name: ${job4.name}`);
    console.log(`- Test Path: ${job4.dataSource.options?.testPath || 'None (full scan)'}`);
    console.log(`- Remote Path: ${job4.dataSource.sftp?.remotePath}`);
    console.log(`- Local Path: ${job4.destination.localPath}`);

    console.log("\n🚀 You can now run the job to test with this specific path!");

    process.exit(0);
  } catch (error) {
    console.error("Error:", error);
    process.exit(1);
  }
}

setTestPath();
