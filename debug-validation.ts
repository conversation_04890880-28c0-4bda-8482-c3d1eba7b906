// Debug ADK processing validation in detail
import { loadJobDefinition } from "./src/lib/jobPersistence";
import { initializeDatabase } from "./src/lib/database";
import { AdkProcessingJobHandler } from "./src/lib/jobs/adk-processing/AdkProcessingJobHandler";

async function debugValidation() {
  try {
    console.log("🔍 Debugging ADK Processing Validation in Detail...\n");

    console.log("Initializing database...");
    await initializeDatabase();

    const jobId = "job-1753091554181";
    console.log(`Loading job: ${jobId}`);
    const job = await loadJobDefinition(jobId);

    if (!job) {
      console.log(`❌ Job ${jobId} not found`);
      return;
    }

    console.log(`📄 Job: ${job.name}`);

    // Create handler instance
    const handler = new AdkProcessingJobHandler();
    console.log(`<PERSON><PERSON> created: ${handler.constructor.name}`);
    console.log(`Handler jobType: ${handler.jobType}`);

    // Test basic validation first
    console.log("\n🧪 Testing basic validation...");
    const basicValid = job.dataSource.type === handler.jobType;
    console.log(`Basic type check: ${basicValid}`);

    if (!basicValid) {
      console.log(
        `❌ Basic validation failed: ${job.dataSource.type} !== ${handler.jobType}`
      );
      return;
    }

    // Test ADK config exists
    const adkConfig = job.dataSource.adk_processing;
    console.log(`ADK config exists: ${!!adkConfig}`);

    if (!adkConfig) {
      console.log(`❌ ADK config missing`);
      return;
    }

    // Test individual validation steps
    console.log("\n🔍 Testing individual validation steps...");

    try {
      // Test required fields
      console.log("Checking required fields...");
      const requiredFields = [
        "sourceDirectory",
        "extractionPath",
        "rarToolPath",
        "fileListDatabase",
      ] as const;
      const missingFields = requiredFields.filter(
        (field) => !adkConfig[field as keyof typeof adkConfig]
      );

      if (missingFields.length > 0) {
        console.log(`❌ Missing required fields: ${missingFields.join(", ")}`);
        return;
      }
      console.log("✅ All required fields present");

      // Test file list database config
      console.log("Checking fileListDatabase config...");
      const dbRequiredFields = [
        "host",
        "port",
        "database",
        "username",
        "password",
        "table",
      ] as const;
      const dbMissingFields = dbRequiredFields.filter((field) => {
        const value =
          adkConfig.fileListDatabase[
            field as keyof typeof adkConfig.fileListDatabase
          ];
        return value === undefined || value === null;
      });

      if (dbMissingFields.length > 0) {
        console.log(
          `❌ Missing fileListDatabase fields: ${dbMissingFields.join(", ")}`
        );
        return;
      }
      console.log("✅ All fileListDatabase fields present");

      // Test port validation
      console.log("Checking port validation...");
      const port = adkConfig.fileListDatabase.port;
      if (typeof port !== "number" || port <= 0 || port > 65535) {
        console.log(`❌ Invalid port: ${port} (type: ${typeof port})`);
        return;
      }
      console.log(`✅ Port valid: ${port}`);

      // Test string fields
      console.log("Checking string fields...");
      if (!adkConfig.sourceDirectory.trim()) {
        console.log(`❌ sourceDirectory is empty`);
        return;
      }
      if (!adkConfig.extractionPath.trim()) {
        console.log(`❌ extractionPath is empty`);
        return;
      }
      if (!adkConfig.rarToolPath.trim()) {
        console.log(`❌ rarToolPath is empty`);
        return;
      }
      console.log("✅ String fields valid");

      // Test file existence
      console.log("Checking file existence...");
      const fs = await import("fs");

      if (!fs.existsSync(adkConfig.sourceDirectory)) {
        console.log(
          `❌ Source directory does not exist: ${adkConfig.sourceDirectory}`
        );
        return;
      }
      console.log(`✅ Source directory exists: ${adkConfig.sourceDirectory}`);

      if (!fs.existsSync(adkConfig.rarToolPath)) {
        console.log(`❌ RAR tool not found: ${adkConfig.rarToolPath}`);
        return;
      }
      console.log(`✅ RAR tool exists: ${adkConfig.rarToolPath}`);

      console.log("\n✅ All individual validation steps passed!");
    } catch (error) {
      console.log(
        `❌ Error during individual validation: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      console.log(
        `Stack: ${
          error instanceof Error ? error.stack : "No stack trace available"
        }`
      );
      return;
    }

    // Now test the actual validateConfig method
    console.log("\n🧪 Testing actual validateConfig method...");
    try {
      const isValid = handler.validateConfig(job);
      console.log(`validateConfig result: ${isValid}`);

      if (!isValid) {
        console.log(
          "❌ validateConfig returned false despite individual checks passing"
        );
        console.log(
          "This suggests there might be an issue with the validation logic itself"
        );

        // Let's try calling validateAdkProcessingConfig directly to see the actual error
        console.log("\n🔍 Testing validateAdkProcessingConfig directly...");
        try {
          // Access the private method through any
          (handler as any).validateAdkProcessingConfig(adkConfig);
          console.log("✅ validateAdkProcessingConfig passed");
        } catch (validationError) {
          console.log(
            `❌ validateAdkProcessingConfig threw error: ${
              validationError instanceof Error
                ? validationError.message
                : String(validationError)
            }`
          );
          console.log(
            `Stack: ${
              validationError instanceof Error
                ? validationError.stack
                : "No stack trace available"
            }`
          );
        }
      }
    } catch (error) {
      console.log(
        `❌ Error in validateConfig: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      console.log(
        `Stack: ${
          error instanceof Error ? error.stack : "No stack trace available"
        }`
      );
    }

    process.exit(0);
  } catch (error) {
    console.error("❌ Error during debugging:", error);
    process.exit(1);
  }
}

debugValidation();
