"use client";

import React, { useEffect, useRef, useState, useMemo } from "react";

interface LogEntry {
  id: string;
  message: string;
  timestamp: string;
  level: "info" | "warn" | "error" | "debug";
}

interface VirtualizedLogListProps {
  logs: LogEntry[];
  searchTerm: string;
  itemHeight?: number;
  containerHeight?: number;
  onScrollToBottom?: () => void;
  isFullscreen?: boolean;
}

const LogEntryComponent = React.memo(
  ({ entry, searchTerm }: { entry: LogEntry; searchTerm: string }) => {
    const getLogColor = (level: LogEntry["level"]) => {
      switch (level) {
        case "error":
          return "text-red-600 dark:text-red-400";
        case "warn":
          return "text-yellow-600 dark:text-yellow-400";
        case "debug":
          return "text-gray-500 dark:text-gray-500";
        default:
          return "text-gray-700 dark:text-gray-300";
      }
    };

    const highlightText = (text: string, search: string) => {
      if (!search) return text;

      const regex = new RegExp(`(${search})`, "gi");
      const parts = text.split(regex);

      return parts.map((part, index) =>
        regex.test(part) ? (
          <span
            key={index}
            className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded"
          >
            {part}
          </span>
        ) : (
          part
        )
      );
    };

    return (
      <div
        className={`text-xs font-mono ${getLogColor(
          entry.level
        )} break-words py-1 leading-relaxed transition-colors duration-150 hover:bg-gray-300 dark:hover:bg-gray-800 px-2 rounded`}
      >
        <span className="text-gray-400 dark:text-gray-600 mr-2">
          {entry.timestamp}
        </span>
        {highlightText(entry.message, searchTerm)}
      </div>
    );
  }
);

LogEntryComponent.displayName = "LogEntryComponent";

export const VirtualizedLogList: React.FC<VirtualizedLogListProps> = ({
  logs,
  searchTerm,
  itemHeight = 24,
  containerHeight = 256,
  onScrollToBottom,
  isFullscreen = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(true);
  const [dynamicHeight, setDynamicHeight] = useState(containerHeight);

  // Calculate dynamic height for fullscreen mode
  useEffect(() => {
    if (isFullscreen && containerRef.current) {
      const updateHeight = () => {
        const container = containerRef.current;
        if (container) {
          let availableHeight = 0;

          // Find the LogViewer container (parent of this component)
          let logViewerContainer = container.parentElement;
          while (
            logViewerContainer &&
            !logViewerContainer.classList.contains("flex-1")
          ) {
            logViewerContainer = logViewerContainer.parentElement;
          }

          if (logViewerContainer) {
            const containerHeight = logViewerContainer.clientHeight;
            // Reserve minimal space for controls (they're positioned absolutely)
            const reservedSpace = 20; // Just for padding and margins
            availableHeight = Math.max(400, containerHeight - reservedSpace);
          } else {
            // Fallback: use viewport height minus modal chrome
            availableHeight = Math.max(400, window.innerHeight - 200);
          }

          const computedHeight =
            availableHeight > 0 ? availableHeight : containerHeight;
          setDynamicHeight(computedHeight);
        }
      };

      // Use setTimeout to ensure DOM is fully rendered
      const timeoutId = setTimeout(updateHeight, 100);

      // Update height on window resize
      window.addEventListener("resize", updateHeight);
      return () => {
        clearTimeout(timeoutId);
        window.removeEventListener("resize", updateHeight);
      };
    } else {
      setDynamicHeight(containerHeight);
    }
  }, [isFullscreen, containerHeight]);

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const effectiveHeight = isFullscreen ? dynamicHeight : containerHeight;
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(effectiveHeight / itemHeight) + 1,
      logs.length
    );
    return { startIndex, endIndex };
  }, [
    scrollTop,
    itemHeight,
    containerHeight,
    dynamicHeight,
    isFullscreen,
    logs.length,
  ]);

  // Get visible items
  const visibleItems = useMemo(() => {
    return logs.slice(visibleRange.startIndex, visibleRange.endIndex);
  }, [logs, visibleRange]);

  // Handle scroll events
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const newScrollTop = target.scrollTop;
    setScrollTop(newScrollTop);

    // Check if scrolled to bottom
    const isAtBottom =
      newScrollTop + target.clientHeight >= target.scrollHeight - 5;
    setIsScrolledToBottom(isAtBottom);

    if (isAtBottom && onScrollToBottom) {
      onScrollToBottom();
    }
  };

  // Auto-scroll to bottom when new logs are added (if user was at bottom)
  useEffect(() => {
    if (isScrolledToBottom && containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [logs.length, isScrolledToBottom]);

  // Total height for scrollbar
  const totalHeight = logs.length * itemHeight;

  // Offset for visible items
  const offsetY = visibleRange.startIndex * itemHeight;

  return (
    <div
      ref={containerRef}
      className="bg-slate-200 dark:bg-gray-900 rounded-lg p-2 overflow-auto"
      style={{
        height: isFullscreen ? dynamicHeight : containerHeight,
      }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: "relative" }}>
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {visibleItems.map((entry) => (
            <div key={entry.id} style={{ height: itemHeight }}>
              <LogEntryComponent entry={entry} searchTerm={searchTerm} />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
