/**
 * Test script to verify the job sequencing fixes
 * 
 * This script tests:
 * 1. Issue #1: Adding jobs to existing sequences persists properly (SSE updates)
 * 2. Issue #2: Real-time dashboard updates when new sequences are created (SSE updates)
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api/admin`;

// Test data
const testSequence = {
  id: 'fix-test-sequence',
  name: 'Fix Test Sequence',
  description: 'Testing sequence fixes for SSE updates',
  schedule: '0 */12 * * *',
  enabled: true,
  onFailure: 'stop',
  maxRetries: 1,
  jobs: []
};

const testJob = {
  id: 'fix-test-job',
  name: 'Fix Test Job',
  description: 'Test job for sequence fixes',
  schedule: '0 1 * * *',
  enabled: true,
  dataSource: {
    type: 'database_admin',
    database_admin: {
      host: 'localhost',
      port: 3306,
      database: 'test_db',
      username: 'test_user',
      password: 'test_pass',
      operationMode: 'data_extraction',
      operations: [{
        type: 'DATA_EXTRACTION',
        query: 'SELECT 1 as test_value'
      }]
    }
  },
  destination: {
    type: 'database',
    database: {
      type: 'mysql',
      host: 'localhost',
      port: 3306,
      database: 'test_db',
      username: 'test_user',
      password: 'test_pass',
      table: 'test_output'
    }
  },
  retryConfig: {
    maxRetries: 1,
    retryDelay: 10
  }
};

// SSE connection to monitor real-time updates
let sseEventSource = null;
let receivedSequenceUpdates = [];
let receivedJobUpdates = [];

function setupSSEMonitoring() {
  return new Promise((resolve) => {
    console.log('🔌 Setting up SSE monitoring...');
    
    sseEventSource = new (require('eventsource'))(`${BASE_URL}/api/sse`);
    
    sseEventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'sequenceUpdate') {
          receivedSequenceUpdates.push({
            timestamp: new Date(),
            sequences: data.sequences
          });
          console.log(`📡 Received sequence update: ${data.sequences.length} sequences`);
        } else if (data.type === 'jobUpdate') {
          receivedJobUpdates.push({
            timestamp: new Date(),
            jobs: data.jobs
          });
          console.log(`📡 Received job update: ${data.jobs.length} jobs`);
        }
      } catch (error) {
        console.error('Error parsing SSE message:', error);
      }
    };
    
    sseEventSource.onerror = (error) => {
      console.error('SSE connection error:', error);
    };
    
    // Give SSE a moment to connect
    setTimeout(resolve, 1000);
  });
}

function closeSSEMonitoring() {
  if (sseEventSource) {
    sseEventSource.close();
    console.log('🔌 Closed SSE connection');
  }
}

async function testIssue1_AddJobToExistingSequence() {
  console.log('\n🧪 Testing Issue #1: Adding jobs to existing sequences...');
  
  // Clear SSE update counters
  receivedSequenceUpdates = [];
  receivedJobUpdates = [];
  
  // Create a sequence without jobs first
  const emptySequence = { ...testSequence, jobs: [] };
  await axios.post(`${API_BASE}/sequences`, { sequence: emptySequence });
  console.log('✅ Created empty sequence');
  
  // Wait a moment for SSE
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Create a test job
  await axios.post(`${API_BASE}/jobs`, testJob);
  console.log('✅ Created test job');
  
  // Wait a moment for SSE
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Add job to the existing sequence using individual assignment API
  await axios.put(`${API_BASE}/jobs/${testJob.id}/sequence`, {
    sequenceId: testSequence.id,
    order: 1
  });
  console.log('✅ Added job to existing sequence');
  
  // Wait for SSE updates
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Check if we received SSE updates
  const sequenceUpdatesReceived = receivedSequenceUpdates.length > 0;
  const jobUpdatesReceived = receivedJobUpdates.length > 0;
  
  console.log(`📊 SSE Updates received:`);
  console.log(`   Sequence updates: ${receivedSequenceUpdates.length}`);
  console.log(`   Job updates: ${receivedJobUpdates.length}`);
  
  if (sequenceUpdatesReceived && jobUpdatesReceived) {
    console.log('✅ Issue #1 FIXED: SSE updates working for job assignment');
  } else {
    console.log('❌ Issue #1 NOT FIXED: Missing SSE updates');
  }
  
  return sequenceUpdatesReceived && jobUpdatesReceived;
}

async function testIssue2_NewSequenceCreation() {
  console.log('\n🧪 Testing Issue #2: Real-time updates for new sequences...');
  
  // Clear SSE update counters
  receivedSequenceUpdates = [];
  receivedJobUpdates = [];
  
  // Create a new sequence with a different ID
  const newSequence = {
    ...testSequence,
    id: 'fix-test-sequence-2',
    name: 'Fix Test Sequence 2',
    jobs: [testJob.id] // Include the job we created earlier
  };
  
  await axios.post(`${API_BASE}/sequences`, { sequence: newSequence });
  console.log('✅ Created new sequence with jobs');
  
  // Wait for SSE updates
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Check if we received SSE updates
  const sequenceUpdatesReceived = receivedSequenceUpdates.length > 0;
  const jobUpdatesReceived = receivedJobUpdates.length > 0;
  
  console.log(`📊 SSE Updates received:`);
  console.log(`   Sequence updates: ${receivedSequenceUpdates.length}`);
  console.log(`   Job updates: ${receivedJobUpdates.length}`);
  
  if (sequenceUpdatesReceived && jobUpdatesReceived) {
    console.log('✅ Issue #2 FIXED: SSE updates working for sequence creation');
  } else {
    console.log('❌ Issue #2 NOT FIXED: Missing SSE updates');
  }
  
  return sequenceUpdatesReceived && jobUpdatesReceived;
}

async function cleanup() {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    // Remove job from sequences
    try {
      await axios.delete(`${API_BASE}/jobs/${testJob.id}/sequence`);
      console.log('✅ Removed job from sequence');
    } catch (error) {
      console.log('⚠️  Job may not be in sequence');
    }
    
    // Delete sequences
    for (const seqId of ['fix-test-sequence', 'fix-test-sequence-2']) {
      try {
        await axios.delete(`${API_BASE}/sequences/${seqId}`);
        console.log(`✅ Deleted sequence: ${seqId}`);
      } catch (error) {
        console.log(`⚠️  Could not delete sequence: ${seqId}`);
      }
    }
    
    // Delete job
    try {
      await axios.delete(`${API_BASE}/jobs/${testJob.id}`);
      console.log('✅ Deleted test job');
    } catch (error) {
      console.log('⚠️  Could not delete test job');
    }
    
  } catch (error) {
    console.error('❌ Cleanup error:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting Job Sequencing Fix Tests...\n');
  
  try {
    // Setup SSE monitoring
    await setupSSEMonitoring();
    
    // Run tests
    const issue1Fixed = await testIssue1_AddJobToExistingSequence();
    const issue2Fixed = await testIssue2_NewSequenceCreation();
    
    // Summary
    console.log('\n📋 Test Results Summary:');
    console.log(`   Issue #1 (Job assignment SSE): ${issue1Fixed ? '✅ FIXED' : '❌ NOT FIXED'}`);
    console.log(`   Issue #2 (Sequence creation SSE): ${issue2Fixed ? '✅ FIXED' : '❌ NOT FIXED'}`);
    
    if (issue1Fixed && issue2Fixed) {
      console.log('\n🎉 All fixes verified successfully!');
    } else {
      console.log('\n⚠️  Some issues may still need attention.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  } finally {
    closeSSEMonitoring();
    await cleanup();
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--cleanup')) {
  cleanup();
} else {
  runTests();
}

module.exports = {
  runTests,
  cleanup
};
