import { NextRequest, NextResponse } from "next/server";
import {
  loadAllSystemSettings,
  loadSystemSettingsByCategory,
  saveSystemSetting,
  deleteSystemSetting,
  SETTING_CATEGORIES,
} from "@/lib/configPersistence";
import { logger } from "@/lib/jobManager";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get("category");

    if (category) {
      const settings = await loadSystemSettingsByCategory(category);
      return NextResponse.json({ settings, category });
    } else {
      const settings = await loadAllSystemSettings();
      const categories = Object.values(SETTING_CATEGORIES);
      return NextResponse.json({ settings, categories });
    }
  } catch (error) {
    logger.error("Failed to get system settings:", error);
    return NextResponse.json(
      { error: "Failed to retrieve system settings" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { category, key, value, type, description } = body;

    if (!category || !key || value === undefined) {
      return NextResponse.json(
        { error: "Missing required fields: category, key, value" },
        { status: 400 }
      );
    }

    // Validate category
    const validCategories = Object.values(SETTING_CATEGORIES);
    if (
      !validCategories.includes(category as (typeof validCategories)[number])
    ) {
      return NextResponse.json({ error: "Invalid category" }, { status: 400 });
    }

    // Validate type
    const validTypes = ["string", "number", "boolean", "json"];
    if (type && !validTypes.includes(type)) {
      return NextResponse.json(
        { error: "Invalid type. Must be one of: " + validTypes.join(", ") },
        { status: 400 }
      );
    }

    await saveSystemSetting(
      category,
      key,
      value,
      type || "string",
      description
    );

    logger.info(`System setting updated: ${category}.${key} = ${value}`);

    return NextResponse.json({
      success: true,
      message: "Setting saved successfully",
    });
  } catch (error) {
    logger.error("Failed to save system setting:", error);
    return NextResponse.json(
      { error: "Failed to save system setting" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get("category");
    const key = searchParams.get("key");

    if (!category || !key) {
      return NextResponse.json(
        { error: "Missing required parameters: category, key" },
        { status: 400 }
      );
    }

    await deleteSystemSetting(category, key);

    logger.info(`System setting deleted: ${category}.${key}`);

    return NextResponse.json({
      success: true,
      message: "Setting deleted successfully",
    });
  } catch (error) {
    logger.error("Failed to delete system setting:", error);
    return NextResponse.json(
      { error: "Failed to delete system setting" },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { settings } = body;

    if (!Array.isArray(settings)) {
      return NextResponse.json(
        { error: "Settings must be an array" },
        { status: 400 }
      );
    }

    // Batch update settings
    for (const setting of settings) {
      const { category, key, value, type, description } = setting;
      if (category && key && value !== undefined) {
        await saveSystemSetting(
          category,
          key,
          value,
          type || "string",
          description
        );
      }
    }

    logger.info(`Batch updated ${settings.length} system settings`);

    return NextResponse.json({
      success: true,
      message: `Successfully updated ${settings.length} settings`,
    });
  } catch (error) {
    logger.error("Failed to batch update system settings:", error);
    return NextResponse.json(
      { error: "Failed to batch update system settings" },
      { status: 500 }
    );
  }
}
