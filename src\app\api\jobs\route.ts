import { NextRequest, NextResponse } from "next/server";
import { runDataPullingJob, requestJobCancellation } from "@/lib/jobRunner";
import {
  getJobStatus,
  updateJobStatus,
  updateJobEnabled,
} from "@/lib/jobManager";
import { cronScheduler } from "@/lib/cronScheduler";

export async function GET() {
  try {
    const jobs = await getJobStatus();
    return NextResponse.json({ jobs });
  } catch (error) {
    console.error("Error fetching jobs:", error);
    return NextResponse.json(
      { error: "Failed to fetch jobs" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { jobId, action } = await request.json();

    if (action === "run") {
      // Start the job in the background (manual trigger)
      runDataPullingJob(jobId, "manual").catch((error) => {
        console.error(`Job ${jobId} failed:`, error);
      });

      return NextResponse.json({
        message: "Job started successfully",
        jobId,
      });
    }

    if (action === "stop") {
      // Stop the cron job scheduler and disable the job
      cronScheduler.stopJob(jobId);
      await updateJobEnabled(jobId, false);
      return NextResponse.json({
        message: "Job scheduler stopped successfully",
        jobId,
      });
    }

    if (action === "cancel") {
      // Cancel a currently running job
      const wasCancelled = requestJobCancellation(jobId);

      // Always update the job status to "stopped" immediately for UI responsiveness
      // This ensures the UI shows the cancelled state right away
      await updateJobStatus(jobId, "stopped", {
        logs: ["Job cancelled by user"],
      });

      // Ensure SSE broadcast is triggered to update all connected clients
      try {
        const { broadcastJobUpdate } = await import("@/lib/sseManager");
        await broadcastJobUpdate();
      } catch (error) {
        console.error(
          "Failed to broadcast job update after cancellation:",
          error
        );
      }

      if (wasCancelled) {
        // Job was running and has been marked for cancellation
        return NextResponse.json({
          message: "Job cancellation requested successfully",
          jobId,
        });
      } else {
        // Job was not running
        return NextResponse.json({
          message: "Job cancelled successfully",
          jobId,
        });
      }
    }

    if (action === "start") {
      // Start the cron job scheduler and enable the job
      cronScheduler.startJob(jobId);
      await updateJobEnabled(jobId, true);
      return NextResponse.json({
        message: "Job started successfully",
        jobId,
      });
    }

    return NextResponse.json({ error: "Invalid action" }, { status: 400 });
  } catch (error) {
    console.error("Error handling job action:", error);
    return NextResponse.json(
      { error: "Failed to handle job action" },
      { status: 500 }
    );
  }
}
