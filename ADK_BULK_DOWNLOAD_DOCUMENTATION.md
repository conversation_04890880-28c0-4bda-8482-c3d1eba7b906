# ADK RKAKL 2024 Bulk Download Cron Job

This document describes the new automated cron job for downloading ADK RKAKL 2024 files from the Indonesian government SFTP server.

## Overview

The ADK RKAKL 2024 Bulk Download job is designed to automatically download budget documents from all organizational units in the Indonesian government system. This job replaces and enhances the legacy batch file workflow with modern Node.js automation.

## Job Configuration

- **Job ID**: `4`
- **Name**: "ADK RKAKL 2024 Bulk Download"
- **Schedule**: Daily at 1:00 AM (`0 1 * * *`)
- **Type**: SFTP bulk download
- **Status**: Enabled by default

## Data Source Configuration

```typescript
{
  type: "sftp",
  sftp: {
    host: "aksesdata-anggaran.kemenkeu.go.id",
    port: 54321,
    username: "PA_DJPBN",
    password: "Sinergi100Persen",
    remotePath: "adk_rkakl2024"
  },
  options: {
    bulkDownload: true,
    organisationalUnits: [/* 200+ unit codes */],
    skipExisting: true,
    createDirectories: true,
    trackMetadata: true
  }
}
```

## Organizational Units

The job processes **200+ organizational units** with codes like:

- `010012024`, `010022024`, `010032024` (Ministry units)
- `020042024`, `020052024`, `020062024` (Department units)
- And many more...

Each unit code follows the pattern: `{unit}{subunit}{year}`

## Destination Configuration

```typescript
{
  type: "local",
  localPath: "C:/KUMPULAN_ADK/ADK_2024_DIPA",
  options: {
    createDirectory: true,
    preserveStructure: true,
    trackInDatabase: true,
    metadataTable: "monev2024.file_metadata",
    errorLogTable: "log_ftp.error_logs"
  }
}
```

## File Structure

Downloaded files are organized as:

```
C:/KUMPULAN_ADK/ADK_2024_DIPA/
├── 01/001/2024/
│   ├── document1.pdf
│   └── document2.xlsx
├── 01/002/2024/
│   └── document3.pdf
└── ... (200+ organizational folders)
```

## Database Integration

### File Metadata Table (`monev2024.file_metadata`)

- **folder**: Organizational unit path
- **nmfile**: File name
- **tg**: Date downloaded
- **jam**: Time downloaded
- **size**: File size in bytes
- **status**: Processing status ('NEW')

### Error Logging Table (`log_ftp.error_logs`)

- **folder**: Folder where error occurred
- **nmfile**: File name (if applicable)
- **error_message**: Error description
- **stack_trace**: Full error stack trace

## Features

### Smart Download Logic

1. **Skip Existing Files**: Checks local file system before downloading
2. **Metadata Tracking**: Updates database even for existing files
3. **Directory Creation**: Automatically creates folder structure
4. **Error Handling**: Comprehensive error logging and recovery

### Performance Optimizations

- Processes one organizational unit at a time
- Continues processing even if individual units fail
- Configurable retry logic (3 retries, 15-minute delay)
- Detailed progress logging

## API Endpoints

### Manual Trigger

```http
POST /api/jobs/adk-bulk
```

Manually start the bulk download job.

### Job Status

```http
GET /api/jobs/adk-bulk
```

Get current job configuration and status.

## Dashboard Integration

The job includes a dedicated React component (`AdkBulkDownload`) that provides:

- **Real-time Status**: Shows enabled/disabled state
- **Progress Tracking**: Visual progress bar during execution
- **Manual Trigger**: Button to start download immediately
- **Detailed View**: Modal with full job configuration
- **Result Display**: Success/error messages

### Component Usage

```tsx
import { AdkBulkDownload } from "@/components";

export default function Dashboard() {
  return (
    <div className="grid gap-6">
      <AdkBulkDownload className="col-span-1" />
      {/* Other dashboard components */}
    </div>
  );
}
```

## Monitoring and Logs

### Application Logs

- **Winston Logger**: Structured logging with timestamps
- **Log Files**:
  - `logs/combined.log`: All log entries
  - `logs/error.log`: Error-only entries
- **Console Output**: Development environment

### Database Logs

- **Execution History**: Tracked in job execution tables
- **File Metadata**: Complete download history
- **Error Tracking**: Detailed error logs with stack traces

## Error Handling

### Common Error Scenarios

1. **Network Issues**: Connection timeouts, DNS failures
2. **Authentication**: Invalid credentials or expired passwords
3. **File System**: Disk space, permission issues
4. **Database**: Connection failures, constraint violations

### Recovery Mechanisms

- **Automatic Retry**: Up to 3 attempts with exponential backoff
- **Partial Recovery**: Continues with next unit if one fails
- **Manual Recovery**: Dashboard controls for immediate retry
- **Error Notifications**: Comprehensive logging for troubleshooting

## Configuration Management

### Environment Variables

```bash
# Optional overrides (uses defaults if not set)
SFTP_HOST=aksesdata-anggaran.kemenkeu.go.id
SFTP_PORT=54321
SFTP_USERNAME=PA_DJPBN
SFTP_PASSWORD=Sinergi100Persen
LOCAL_DOWNLOAD_PATH=C:/KUMPULAN_ADK/ADK_2024_DIPA
```

### Job Management

- **Enable/Disable**: Toggle via dashboard or API
- **Schedule Changes**: Modify cron expression through job manager
- **Retry Configuration**: Adjustable retry count and delays
- **Path Configuration**: Configurable local and remote paths

## Integration with Legacy System

This job seamlessly integrates with the existing `ftp.js` module:

```javascript
// Uses existing tarikFtp() function
import { tarikFtp } from "../tarikData/sftp/ftp.js";

// Calls within job runner for bulk download
await tarikFtp();
```

## Security Considerations

1. **Credential Management**: Store credentials securely
2. **File Permissions**: Ensure proper local file permissions
3. **Network Security**: Use secure SFTP connections
4. **Access Control**: Restrict dashboard access appropriately

## Troubleshooting

### Common Issues

1. **Connection Failures**

   - Check network connectivity
   - Verify SFTP credentials
   - Confirm server availability

2. **File System Issues**

   - Ensure sufficient disk space
   - Check directory permissions
   - Verify path accessibility

3. **Database Issues**
   - Confirm database connectivity
   - Check table schemas
   - Verify user permissions

### Debug Commands

```bash
# Test SFTP connection
npm run test-sftp

# Check job status
curl http://localhost:3000/api/jobs/adk-bulk

# Manual trigger
curl -X POST http://localhost:3000/api/jobs/adk-bulk
```

## Future Enhancements

1. **Parallel Downloads**: Process multiple units simultaneously
2. **Delta Sync**: Only download changed files
3. **Compression**: Archive older downloads
4. **Notifications**: Email/Slack alerts for failures
5. **Metrics**: Performance and success rate tracking

## Support

For issues or questions:

1. Check application logs in `logs/` directory
2. Review database error logs
3. Use dashboard monitoring tools
4. Contact system administrator

---

**Note**: This job is critical for daily data synchronization. Monitor its execution regularly and ensure proper backup procedures are in place.
