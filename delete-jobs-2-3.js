#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to delete jobs 2 and 3 from the database
 * Run this script to remove the old pre-configured jobs from the database
 */

import { deleteOldJobs, initializeJobManager } from './src/lib/jobManager.js';

async function main() {
  try {
    console.log('🚀 Starting job deletion process...');
    
    // Initialize the job manager and database connection
    console.log('📊 Initializing job manager...');
    await initializeJobManager();
    
    // Delete jobs 2 and 3
    console.log('🗑️  Deleting jobs 2 and 3...');
    await deleteOldJobs();
    
    console.log('✅ Successfully deleted jobs 2 and 3 from the database!');
    console.log('📝 Note: This only affects the database. The pre-configured job definitions in the code have already been removed.');
    
  } catch (error) {
    console.error('❌ Error deleting jobs:', error);
    process.exit(1);
  } finally {
    // Exit the process
    process.exit(0);
  }
}

// Run the script
main();
