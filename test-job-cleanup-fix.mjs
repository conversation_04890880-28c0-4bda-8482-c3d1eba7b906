/**
 * Test script to verify that the job cleanup fix works properly
 * This simulates the scenario where a job fails and then gets retried
 */

import { runDataPullingJob } from "./src/lib/jobRunner.js";
import { getJobDefinition } from "./src/lib/jobManager.js";

async function testJobCleanupFix() {
  console.log("🧪 Testing job cleanup fix...");

  try {
    // First, let's check if job 4 exists
    const jobDef = await getJobDefinition("4");
    if (!jobDef) {
      console.log("❌ Job 4 not found, cannot test");
      return;
    }

    console.log("✅ Job 4 found:", jobDef.name);

    // Try to run the job (this should fail due to SFTP connection issues)
    console.log("🚀 Attempting to run job 4 (expecting it to fail)...");
    const result1 = await runDataPullingJob("4", "manual");
    console.log("📊 First attempt result:", result1);

    // Wait a moment to ensure cleanup completes
    console.log("⏳ Waiting 1 second for cleanup to complete...");
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Try to run the job again immediately (this should not be blocked)
    console.log("🔄 Attempting to run job 4 again (should not be blocked)...");
    const result2 = await runDataPullingJob("4", "manual");
    console.log("📊 Second attempt result:", result2);

    if (result2 === false && !result2.toString().includes("already running")) {
      console.log(
        '✅ SUCCESS: Job cleanup fix is working! Second attempt was not blocked by "already running" error.'
      );
    } else {
      console.log("❌ FAILURE: Job cleanup fix may not be working properly.");
    }
  } catch (error) {
    console.error("❌ Test failed with error:", error.message);
  }
}

// Run the test
testJobCleanupFix()
  .then(() => {
    console.log("🏁 Test completed");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Test crashed:", error);
    process.exit(1);
  });
