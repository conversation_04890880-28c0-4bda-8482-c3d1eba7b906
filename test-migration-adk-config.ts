// Test the updated migration file to ensure ADK job has correct configuration
import { initializeDatabase } from './src/lib/database';
import { loadJobDefinition } from './src/lib/jobPersistence';

async function testMigrationAdkConfig() {
  try {
    console.log('🧪 Testing Updated Migration - ADK Job Configuration...\n');
    
    console.log('Initializing database (this will run the migration)...');
    await initializeDatabase();
    
    console.log('Loading ADK Processing job (Job 6)...');
    const adkJob = await loadJobDefinition('6');
    
    if (!adkJob) {
      console.log('❌ ADK Processing job (Job 6) not found after migration');
      return;
    }
    
    console.log(`✅ Found job: ${adkJob.name}`);
    console.log(`📄 Description: ${adkJob.description}`);
    console.log(`⏰ Schedule: ${adkJob.schedule}`);
    console.log(`🔧 Enabled: ${adkJob.enabled}`);
    
    // Check destination configuration
    console.log('\n🔍 Checking destination configuration...');
    const destination = adkJob.destination;
    
    if (!destination) {
      console.log('❌ No destination configuration found');
      return;
    }
    
    console.log(`✅ Destination type: ${destination.type}`);
    
    // Check main database configuration
    if (destination.database) {
      const db = destination.database;
      console.log(`✅ Main database: ${db.host}:${db.port}/${db.database}`);
      console.log(`   - Type: ${db.type}`);
      console.log(`   - Username: ${db.username}`);
      console.log(`   - Default table: ${db.table}`);
      
      if (db.database === 'dbdipa25') {
        console.log('✅ Correct: ADK data will be stored in dbdipa25 database');
      } else {
        console.log(`❌ Incorrect: Expected dbdipa25, got ${db.database}`);
      }
    } else {
      console.log('❌ No main database configuration found');
    }
    
    // Check file tracking configuration
    console.log('\n🔍 Checking file tracking configuration...');
    if (destination.fileTracking) {
      const ft = destination.fileTracking;
      console.log(`✅ File tracking enabled: ${ft.enabled}`);
      
      if (ft.database) {
        const ftDb = ft.database;
        console.log(`✅ File tracking database: ${ftDb.host}:${ftDb.port}/${ftDb.database}`);
        console.log(`   - Username: ${ftDb.username}`);
        console.log(`   - Table: ${ftDb.table}`);
        
        if (ftDb.database === 'monev2025' && ftDb.table === 'file_metadata') {
          console.log('✅ Correct: File tracking uses monev2025.file_metadata');
        } else {
          console.log(`❌ Incorrect: Expected monev2025.file_metadata, got ${ftDb.database}.${ftDb.table}`);
        }
      } else {
        console.log('❌ No file tracking database configuration found');
      }
    } else {
      console.log('❌ No file tracking configuration found');
    }
    
    // Validate with job handler
    console.log('\n🧪 Validating with ADK job handler...');
    try {
      const { jobHandlerFactory } = await import('./src/lib/jobs');
      const handler = jobHandlerFactory.getHandler('adk_processing');
      const isValid = handler.validateConfig(adkJob);
      
      if (isValid) {
        console.log('✅ Job configuration is valid according to handler');
      } else {
        console.log('❌ Job configuration is invalid according to handler');
      }
    } catch (validationError) {
      console.log(`⚠️  Could not validate with handler: ${validationError}`);
    }
    
    // Summary
    console.log('\n📋 Configuration Summary:');
    console.log('   ✅ ADK data storage: dbdipa25 database (for d_akun, d_item, etc.)');
    console.log('   ✅ File tracking: monev2025.file_metadata table');
    console.log('   ✅ Complete destination configuration with fileTracking');
    console.log('\n🎉 Migration creates properly configured ADK processing job!');
    console.log('💡 Future users will get a working ADK job configuration out of the box.');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error testing migration:', error);
    process.exit(1);
  }
}

testMigrationAdkConfig();
