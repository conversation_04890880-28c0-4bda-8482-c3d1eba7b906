// Simple script to reset job definitions with updated credentials
import mysql from "mysql2/promise";

async function resetJobs() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    port: parseInt(process.env.DB_PORT || "3399"),
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "pulltest",
  });

  try {
    console.log("Deleting existing job definitions...");

    // Delete all existing jobs
    await connection.execute(
      "DELETE FROM job_execution_logs WHERE execution_id IN (SELECT id FROM job_executions)"
    );
    await connection.execute("DELETE FROM job_executions");
    await connection.execute("DELETE FROM job_schedule_history");
    await connection.execute("DELETE FROM job_definitions");

    console.log("Existing jobs deleted successfully");
    console.log(
      "Jobs will be recreated with updated credentials when the app starts next time"
    );
  } catch (error) {
    console.error("Error resetting jobs:", error);
  } finally {
    await connection.end();
  }
}

resetJobs();
