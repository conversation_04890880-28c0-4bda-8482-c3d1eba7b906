"use client";

// ============================================================================
// IMPORTS
// ============================================================================

// React imports
import React, { useState, useEffect } from "react";

// UI library imports
import { Card, CardBody } from "@heroui/react";

// Icon imports
import { Clock } from "lucide-react";

// Animation imports
import { motion } from "framer-motion";

// Utility imports
import {
  getTimezoneDisplayName,
  getUTCOffsetString,
  getUserTimezone,
} from "@/utils/timeUtils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface DigitalClockProps {
  className?: string;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export default function DigitalClock({ className = "" }: DigitalClockProps) {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [currentTime, setCurrentTime] = useState<string>("");
  const [currentDate, setCurrentDate] = useState<string>("");
  const [isLoaded, setIsLoaded] = useState(false);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    const updateTime = () => {
      const now = new Date();

      // Format time (HH:MM:SS) in user's local timezone with 24-hour format
      const timeString = now.toLocaleString("en-US", {
        timeZone: getUserTimezone(),
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false, // Ensure 24-hour format
      });

      // Format date (Day, Month DD) in user's local timezone
      const dateString = now.toLocaleString("en-US", {
        timeZone: getUserTimezone(),
        weekday: "short",
        month: "short",
        day: "2-digit",
      });

      setCurrentTime(timeString);
      setCurrentDate(dateString);
      setIsLoaded(true);
    };

    // Update immediately
    updateTime();

    // Update every second
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, []);

  // ============================================================================
  // RENDER
  // ============================================================================

  // Loading state
  if (!isLoaded) {
    return (
      <Card
        className={`bg-background border-2 border-slate-100 dark:border-slate-600 rounded-2xl ${className}`}
      >
        <CardBody className="px-[var(--spacing-lg)] py-[var(--spacing-md)] h-[60px] flex items-center">
          <div className="flex items-center gap-[var(--spacing-md)] w-full">
            <Clock className="w-4 h-4 text-blue-600 animate-pulse" />
            <div className="flex items-center gap-[var(--spacing-md)]">
              <div className="h-5 w-20 bg-muted rounded-2xl animate-pulse"></div>
              <div className="h-3 w-16 bg-muted rounded-2xl animate-pulse"></div>
            </div>
          </div>
        </CardBody>
      </Card>
    );
  }

  // Main clock display
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card className="bg-background border-2 border-slate-100 dark:border-slate-600 rounded-2xl flex px-[var(--spacing-sm)] h-[60px] transition-all duration-300">
        <CardBody className="flex justify-center">
          <div className="flex items-center gap-[var(--spacing-md)] w-full">
            <motion.div
              animate={{
                rotate: [0, 360],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "linear",
              }}
            >
              <Clock className="w-4 h-4 text-blue-600" />
            </motion.div>

            <div className="flex items-center gap-[var(--spacing-md)]">
              <div className="font-mono text-base font-bold text-foreground tracking-wider">
                {currentTime}
              </div>

              <div className="flex items-center gap-[var(--spacing-sm)]">
                <div className="text-xs text-muted-foreground font-medium">
                  {currentDate}
                </div>

                <div className="text-xs text-muted-foreground bg-muted px-[var(--spacing-sm)] py-0.5 rounded-2xl">
                  {getTimezoneDisplayName()} ({getUTCOffsetString()})
                </div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
}
