"use client";

// ============================================================================
// IMPORTS
// ============================================================================

// React imports
import React, { useState, useEffect } from "react";

// UI library imports
import {
  Input,
  Select,
  SelectItem,
  Button,
  Chip,
  Divider,
} from "@heroui/react";

// Icon imports
import { Clock, Info, RefreshCw, AlertCircle } from "lucide-react";

// Utility imports
import {
  validateCronExpression,
  calculateNextRuns,
  CRON_PRESETS,
} from "@/utils/cronUtils";
import {
  formatDetailedDate,
  formatInServerTimezone,
  getTimezoneDisplayName,
  getUTCOffsetString,
  SERVER_TIMEZONE,
} from "@/utils/timeUtils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface CronExpressionBuilderProps {
  value: string;
  onChange: (cronExpression: string) => void;
  isDisabled?: boolean;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export default function CronExpressionBuilder({
  value,
  onChange,
  isDisabled = false,
}: CronExpressionBuilderProps) {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [customMode, setCustomMode] = useState(false);
  const [selectedPreset, setSelectedPreset] = useState("");
  const [nextRuns, setNextRuns] = useState<Date[]>([]);
  const [validationError, setValidationError] = useState<string | null>(null);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    // Check if current value matches any preset
    const preset = CRON_PRESETS.find((p) => p.value === value);
    if (preset) {
      setSelectedPreset(preset.value);
      setCustomMode(false);
    } else {
      setSelectedPreset("");
      setCustomMode(true);
    }

    // Validate and calculate next run times
    const validation = validateCronExpression(value);
    if (validation.isValid) {
      setValidationError(null);
      setNextRuns(calculateNextRuns(value));
    } else {
      setValidationError(validation.error || "Invalid cron expression");
      setNextRuns([]);
    }
  }, [value]);

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handlePresetChange = (preset: string) => {
    setSelectedPreset(preset);
    setCustomMode(false);
    onChange(preset);
  };

  const handleCustomChange = (customValue: string) => {
    onChange(customValue);
  };

  const toggleCustomMode = () => {
    setCustomMode(!customMode);
    if (!customMode) {
      setSelectedPreset("");
    }
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className="form-section animate-fade-in">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center">
          <Clock className="w-4 h-4 text-blue-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Schedule Configuration
          </h3>
          <p className="text-sm text-gray-600">
            Configure when this job should run automatically
          </p>
        </div>
      </div>

      <div className="form-group">
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant={customMode ? "flat" : "solid"}
            color="primary"
            onPress={() => setCustomMode(false)}
            isDisabled={isDisabled}
          >
            Presets
          </Button>
          <Button
            size="sm"
            variant={customMode ? "solid" : "flat"}
            color="primary"
            onPress={toggleCustomMode}
            isDisabled={isDisabled}
          >
            Custom
          </Button>
        </div>

        {!customMode ? (
          <Select
            label="Schedule Preset"
            placeholder="Select a schedule preset"
            selectedKeys={selectedPreset ? [selectedPreset] : []}
            onSelectionChange={(keys) => {
              const selected = Array.from(keys)[0] as string;
              if (selected) handlePresetChange(selected);
            }}
            isDisabled={isDisabled}
          >
            {CRON_PRESETS.map((preset) => (
              <SelectItem key={preset.value} textValue={preset.label}>
                <div>
                  <div className="font-medium">{preset.label}</div>
                  <div className="text-sm text-gray-500">
                    {preset.description}
                  </div>
                  <div className="text-xs text-gray-400 font-mono">
                    {preset.value}
                  </div>
                </div>
              </SelectItem>
            ))}
          </Select>
        ) : (
          <Input
            label="Custom Cron Expression"
            value={value}
            onChange={(e) => handleCustomChange(e.target.value)}
            placeholder="0 2 * * *"
            description="Format: minute hour day month weekday"
            isDisabled={isDisabled}
          />
        )}

        <Divider />

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Info className="w-4 h-4 text-blue-500" />
            <span className="text-sm font-medium">Current Expression</span>
          </div>
          <Chip
            variant="flat"
            color={validationError ? "danger" : "primary"}
            className="font-mono"
          >
            {value || "No schedule set"}
          </Chip>
          {validationError && (
            <div className="flex items-center gap-2 text-red-600 text-sm">
              <AlertCircle className="w-4 h-4" />
              <span>{validationError}</span>
            </div>
          )}
        </div>

        {nextRuns.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <RefreshCw className="w-4 h-4 text-green-500" />
              <span className="text-sm font-medium">Next Run</span>
              <div className="flex gap-1">
                <span className="text-xs text-gray-500 bg-green-100 px-2 py-1 rounded">
                  Local: {getTimezoneDisplayName()} ({getUTCOffsetString()})
                </span>
                <span className="text-xs text-gray-500 bg-blue-100 px-2 py-1 rounded">
                  Server: {SERVER_TIMEZONE}
                </span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div className="text-sm text-gray-600 bg-green-50 px-3 py-2 rounded">
                <span className="font-medium">Local:</span>{" "}
                {formatDetailedDate(nextRuns[0])}
              </div>
              <div className="text-sm text-gray-500 bg-blue-50 px-3 py-2 rounded">
                <span className="font-medium">Server:</span>{" "}
                {formatInServerTimezone(nextRuns[0])}
              </div>
            </div>
          </div>
        )}

        <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded space-y-1">
          <div>
            <strong>Cron Format:</strong> minute (0-59) hour (0-23) day (1-31)
            month (1-12) weekday (0-7)
          </div>
          <div>
            <strong>Special characters:</strong> * (any) / (step) , (list) -
            (range)
          </div>
          <div className="text-blue-600 font-medium">
            <strong>Timezone Info:</strong> Schedule times are shown in your
            local timezone ({getTimezoneDisplayName()}) for convenience. Jobs
            execute based on server time ({SERVER_TIMEZONE}).
          </div>
        </div>
      </div>
    </div>
  );
}
