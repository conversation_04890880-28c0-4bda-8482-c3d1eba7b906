import React from "react";
import { Card, CardBody, Chip } from "@heroui/react";
import { Server } from "lucide-react";
import { JobSequence } from "@/lib/jobManager";

interface SequenceCardProps {
  sequence: JobSequence;
  index: number;
  onSequenceSelect: (sequence: JobSequence) => void;
}

export const SequenceCard: React.FC<SequenceCardProps> = ({
  sequence,
  index,
  onSequenceSelect,
}) => {
  return (
    <Card
      className="m-2 cursor-pointer hover:bg-gray-50 transition-colors"
      isPressable
      onPress={() => onSequenceSelect(sequence)}
    >
      <CardBody className="p-4">
        <div className="flex items-center gap-2">
          {/* Sequence Number/Index */}
          <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-xs font-semibold text-blue-600">
              {index + 1}
            </span>
          </div>

          {/* Sequence Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-1 mb-1">
              <h4 className="font-semibold text-sm truncate">
                {sequence.name}
              </h4>
              <div className="flex items-center gap-1">
                <Chip
                  size="sm"
                  color={sequence.enabled ? "success" : "default"}
                  variant="flat"
                  className="text-xs px-1 py-0 h-5"
                >
                  {sequence.enabled ? "ON" : "OFF"}
                </Chip>
                <Chip
                  size="sm"
                  color="primary"
                  variant="flat"
                  className="text-xs px-1 py-0 h-5"
                >
                  {sequence.jobs.length} Jobs
                </Chip>
                <Chip
                  size="sm"
                  color={
                    sequence.onFailure === "stop"
                      ? "danger"
                      : sequence.onFailure === "retry"
                      ? "warning"
                      : "success"
                  }
                  variant="flat"
                  className="text-xs px-1 py-0 h-5"
                >
                  {sequence.onFailure.toUpperCase()}
                </Chip>
              </div>
            </div>
            <p className="text-xs text-gray-500 truncate">
              {sequence.description || "No description"}
            </p>
            <div className="flex items-center gap-2 mt-0.5">
              <span className="text-xs text-gray-400">
                Schedule: {sequence.schedule || "Manual only"}
              </span>
            </div>
          </div>

          {/* Sequence Icon */}
          <div className="flex-shrink-0">
            <Server className="w-3.5 h-3.5 text-blue-500" />
          </div>
        </div>
      </CardBody>
    </Card>
  );
};
