"use client";

import { useEffect, useState, useCallback } from "react";
import { Card, CardBody, Button, Chip, useDisclosure } from "@heroui/react";
import {
  Activity,
  CheckCircle,
  XCircle,
  Clock,
  Database,
  Power,
  Wifi,
  WifiOff,
  Settings,
} from "lucide-react";
import axios from "axios";
import { useJobsSSE } from "@/hooks/useJobsSSE";
import { motion } from "framer-motion";
import { JobStatus, SystemStatus } from "@/types/job";

interface JobSequence {
  id: string;
  name: string;
  description: string;
  schedule?: string;
  enabled: boolean;
  onFailure: "stop" | "continue" | "retry";
  maxRetries: number;
  jobs: string[];
}
import { getDisplayStatus } from "@/utils/jobHelpers";
import {
  JobDetailsModal,
  StatsCard,
  EnhancedJobsDisplay,
  SchedulerControls,
  SettingsModal,
  JobAdminModal,
  DigitalClock,
  DarkModeToggle,
} from "@/components";

export default function Dashboard() {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isSettingsOpen,
    onOpen: onSettingsOpen,
    onClose: onSettingsClose,
  } = useDisclosure();
  const {
    isOpen: isAdminOpen,
    onOpen: onAdminOpen,
    onClose: onAdminClose,
  } = useDisclosure();
  const {
    jobs: sseJobs,
    sequences: sseSequences,
    isConnected,
    error: sseError,
    reconnect,
  } = useJobsSSE();
  const [jobs, setJobs] = useState<JobStatus[]>([]);
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    status: "stopped",
    message: "Loading...",
  });
  const [isJobRunning, setIsJobRunning] = useState(false);
  const [selectedJobId, setSelectedJobId] = useState<string | null>(null);
  const [isStartingSystem, setIsStartingSystem] = useState(false);
  const [isStoppingSystem, setIsStoppingSystem] = useState(false);
  const [sequences, setSequences] = useState<JobSequence[]>([]);

  // Get the current job data for the modal (real-time updates)
  const selectedJob = selectedJobId
    ? jobs.find((job) => job.id === selectedJobId)
    : null;

  // Update jobs when SSE data changes (always sync with SSE)
  useEffect(() => {
    console.log("SSE jobs received:", sseJobs.length, sseJobs);
    setJobs(sseJobs);
  }, [sseJobs]);

  // Update sequences when SSE data changes (always sync with SSE)
  useEffect(() => {
    console.log("SSE sequences received:", sseSequences.length, sseSequences);
    setSequences(sseSequences);
  }, [sseSequences]);

  // Sequences are now loaded via SSE, no manual loading needed

  // Fetch system status
  const fetchSystemStatus = async () => {
    try {
      const response = await axios.get("/api/system");
      setSystemStatus(response.data);
    } catch (error) {
      console.error("Error fetching system status:", error);
      setSystemStatus({ status: "stopped", message: "Failed to fetch status" });
    }
  };

  // Fetch jobs data (fallback when SSE is not working)
  const fetchJobs = useCallback(async () => {
    if (isConnected) return; // Don't fetch if SSE is working

    try {
      const response = await axios.get("/api/jobs");
      const jobsData = response.data.jobs.map(
        (job: {
          id: string;
          name: string;
          status: "running" | "completed" | "failed" | "scheduled" | "stopped";
          lastRun: string;
          nextRun: string;
          duration: number;
          logs: string[];
          enabled: boolean;
        }) => ({
          ...job,
          lastRun: new Date(job.lastRun),
          nextRun: new Date(job.nextRun),
        })
      );
      setJobs(jobsData);
    } catch (error) {
      console.error("Error fetching jobs:", error);
      // Fallback to demo data if API fails
      setJobs([
        {
          id: "1",
          name: "Daily Sales Data",
          status: "completed",
          lastRun: new Date(Date.now() - 2 * 60 * 60 * 1000),
          nextRun: new Date(Date.now() + 22 * 60 * 60 * 1000),
          duration: 45,
          logs: [
            "Starting data pull...",
            "Connected to source",
            "Processing records...",
            "Completed successfully",
          ],
          enabled: true,
          lastRunStatus: "completed",
        },
        {
          id: "2",
          name: "Customer Analytics",
          status: "scheduled",
          lastRun: new Date(Date.now() - 26 * 60 * 60 * 1000),
          nextRun: new Date(Date.now() + 2 * 60 * 60 * 1000),
          duration: 32,
          logs: ["Waiting for scheduled time..."],
          enabled: true,
          lastRunStatus: "completed",
        },
        {
          id: "3",
          name: "Inventory Update",
          status: "failed",
          lastRun: new Date(Date.now() - 6 * 60 * 60 * 1000),
          nextRun: new Date(Date.now() + 18 * 60 * 60 * 1000),
          duration: 0,
          logs: [
            "Starting data pull...",
            "Connection failed",
            "Retry attempt 1...",
            "Error: Timeout",
          ],
          enabled: true,
          lastRunStatus: "failed",
        },
      ]);
    }
  }, [isConnected]);

  // Start system services
  const startSystem = async () => {
    setIsStartingSystem(true);
    try {
      await axios.post("/api/system");
      await fetchSystemStatus();
    } catch (error) {
      console.error("Error starting system:", error);
    } finally {
      setIsStartingSystem(false);
    }
  };

  // Stop system services
  const stopSystem = async () => {
    setIsStoppingSystem(true);
    try {
      await axios.delete("/api/system");
      await fetchSystemStatus();
    } catch (error) {
      console.error("Error stopping system:", error);
    } finally {
      setIsStoppingSystem(false);
    }
  };

  // Data is now refreshed automatically via SSE

  useEffect(() => {
    fetchSystemStatus();
    // Initial fetch of jobs if SSE is not connected
    if (!isConnected) {
      fetchJobs();
    }
  }, [isConnected, fetchJobs]);

  const runJob = async (jobId: string) => {
    setIsJobRunning(true);

    try {
      await axios.post("/api/jobs", {
        jobId,
        action: "run",
      });

      // Find the job that was started and auto-open its details modal
      const jobToShow = jobs.find((job) => job.id === jobId);
      if (jobToShow) {
        setSelectedJobId(jobId);
        onOpen(); // Auto-open the details modal to monitor progress
      }

      // SSE will handle the real-time updates
    } catch (error) {
      console.error("Error running job:", error);
    } finally {
      setIsJobRunning(false);
    }
  };

  const stopJob = async (jobId: string) => {
    try {
      await axios.post("/api/jobs", {
        jobId,
        action: "stop",
      });
      // SSE will handle the real-time updates
    } catch (error) {
      console.error("Error stopping job scheduler:", error);
    }
  };

  const cancelJob = async (jobId: string) => {
    try {
      await axios.post("/api/jobs", {
        jobId,
        action: "cancel",
      });
      // SSE will handle the real-time updates
    } catch (error) {
      console.error("Error cancelling job:", error);
    }
  };

  const startJob = async (jobId: string) => {
    try {
      await axios.post("/api/jobs", {
        jobId,
        action: "start",
      });
      // SSE will handle the real-time updates
    } catch (error) {
      console.error("Error starting job scheduler:", error);
    }
  };

  const stopSequence = async (sequenceId: string) => {
    try {
      // Find the sequence to update
      const sequence = sequences.find((seq) => seq.id === sequenceId);
      if (!sequence) {
        console.error("Sequence not found:", sequenceId);
        return;
      }

      // Update the sequence with enabled: false
      const updatedSequence = { ...sequence, enabled: false };

      await axios.put(`/api/admin/sequences/${sequenceId}`, {
        sequence: updatedSequence,
      });

      // SSE will handle the real-time updates
    } catch (error) {
      console.error("Error disabling sequence:", error);
    }
  };

  const startSequence = async (sequenceId: string) => {
    try {
      // Find the sequence to update
      const sequence = sequences.find((seq) => seq.id === sequenceId);
      if (!sequence) {
        console.error("Sequence not found:", sequenceId);
        return;
      }

      // Update the sequence with enabled: true
      const updatedSequence = { ...sequence, enabled: true };

      await axios.put(`/api/admin/sequences/${sequenceId}`, {
        sequence: updatedSequence,
      });

      // SSE will handle the real-time updates
    } catch (error) {
      console.error("Error enabling sequence:", error);
    }
  };

  const viewJobDetails = (job: JobStatus) => {
    setSelectedJobId(job.id);
    onOpen();
  };

  const handleModalClose = () => {
    onClose();
    setSelectedJobId(null);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Simplified Background Pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5"></div>
      </div>

      <div className="relative z-10">
        {/* Unified Sticky Navigation Bar */}
        <div className="sticky top-0 z-50 py-[var(--spacing-lg)] transition-all duration-300">
          <div className="content-container">
            <div className="flex flex-col xl:flex-row gap-[var(--spacing-lg)] xl:items-center">
              {/* Title Card */}
              <Card className="bg-background border-2 border-slate-100 dark:border-slate-600 rounded-2xl flex px-[var(--spacing-sm)] h-[60px] xl:flex-1">
                <CardBody className="flex justify-center">
                  <div className="flex items-center gap-[var(--spacing-md)] w-full">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center flex-shrink-0">
                      <Database className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <h1 className="text-sm font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 dark:from-white dark:via-blue-200 dark:to-indigo-200 bg-clip-text text-transparent">
                        Data Puller Dashboard
                      </h1>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Digital Clock */}
              <DigitalClock />

              {/* Quick Actions Card */}
              <Card className="bg-background border-2 border-slate-100 dark:border-slate-600 rounded-2xl flex px-[var(--spacing-sm)] h-[60px] xl:flex-shrink-0">
                <CardBody className="flex justify-center">
                  <div className="flex items-center gap-[var(--spacing-md)]">
                    <Button
                      variant="flat"
                      size="sm"
                      isIconOnly
                      onPress={onAdminOpen}
                      title="Job Administration (Quick)"
                      className="bg-muted hover:bg-muted/80"
                    >
                      <Database className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="flat"
                      size="sm"
                      isIconOnly
                      onPress={onSettingsOpen}
                      title="Settings"
                      className="bg-muted hover:bg-muted/80"
                    >
                      <Settings className="w-4 h-4" />
                    </Button>

                    <DarkModeToggle />
                  </div>
                </CardBody>
              </Card>

              {/* System Status Panel */}
              <Card className="bg-background border-2 border-slate-100 dark:border-slate-600 rounded-2xl flex px-[var(--spacing-sm)] h-[60px] xl:flex-1 xl:min-w-0">
                <CardBody className="flex justify-center">
                  <div className="flex flex-wrap items-center gap-[var(--spacing-md)] w-full">
                    {/* Connection Status */}
                    <div className="flex items-center gap-[var(--spacing-sm)]">
                      <span className="text-xs font-medium text-muted-foreground">
                        Connection:
                      </span>
                      <motion.div
                        key={isConnected ? "connected" : "disconnected"}
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Chip
                          color={isConnected ? "success" : "warning"}
                          variant="flat"
                          size="sm"
                          startContent={
                            <motion.div
                              animate={{
                                rotate: isConnected ? 0 : 360,
                                scale: isConnected ? 1 : [1, 1.2, 1],
                              }}
                              transition={{
                                duration: isConnected ? 0.3 : 2,
                                repeat: isConnected ? 0 : Infinity,
                              }}
                            >
                              {isConnected ? (
                                <Wifi className="w-3 h-3" />
                              ) : (
                                <WifiOff className="w-3 h-3" />
                              )}
                            </motion.div>
                          }
                        >
                          {isConnected ? "Live" : "Polling"}
                        </Chip>
                      </motion.div>
                    </div>

                    {/* System Status */}
                    <div className="flex items-center gap-[var(--spacing-sm)]">
                      <span className="text-xs font-medium text-muted-foreground">
                        System:
                      </span>
                      <Chip
                        color={
                          systemStatus.status === "running"
                            ? "success"
                            : "danger"
                        }
                        variant="flat"
                        size="sm"
                        startContent={
                          <Power
                            className={`w-3 h-3 ${
                              systemStatus.status === "running"
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          />
                        }
                      >
                        {systemStatus.status === "running"
                          ? "Running"
                          : "Stopped"}
                      </Chip>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center gap-[var(--spacing-sm)] ml-auto">
                      {systemStatus.status === "stopped" && (
                        <Button
                          className="w-[80px]"
                          color="primary"
                          size="sm"
                          isLoading={isStartingSystem}
                          onPress={startSystem}
                        >
                          Start
                        </Button>
                      )}

                      {systemStatus.status === "running" && (
                        <Button
                          className="w-[80px]"
                          color="danger"
                          size="sm"
                          isLoading={isStoppingSystem}
                          onPress={stopSystem}
                        >
                          Stop
                        </Button>
                      )}

                      {sseError && (
                        <Button
                          size="sm"
                          color="warning"
                          variant="flat"
                          onPress={reconnect}
                        >
                          Reconnect
                        </Button>
                      )}
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>
        </div>

        <div className="content-container py-[var(--spacing-lg)]">
          {/* Stats Overview */}
          <div className="mb-[var(--spacing-2xl)]">
            <div className="flex items-center gap-[var(--spacing-sm)] mb-[var(--spacing-xl)]">
              <div className="w-6 h-6 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center">
                <Activity className="w-3 h-3 text-white" />
              </div>
              <h2 className="text-lg font-bold text-foreground">
                System Overview
              </h2>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-[var(--spacing-lg)]">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <StatsCard
                  title="Total Jobs"
                  value={jobs.length}
                  icon={Database}
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <StatsCard
                  title="Running"
                  value={jobs.filter((job) => job.status === "running").length}
                  icon={Activity}
                  iconColor="text-blue-500"
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <StatsCard
                  title="Scheduled"
                  value={
                    jobs.filter(
                      (job) =>
                        getDisplayStatus(job, systemStatus) === "scheduled"
                    ).length
                  }
                  icon={Clock}
                  iconColor="text-yellow-500"
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <StatsCard
                  title="Service Stopped"
                  value={
                    jobs.filter(
                      (job) =>
                        getDisplayStatus(job, systemStatus) ===
                        "service-stopped"
                    ).length
                  }
                  icon={XCircle}
                  iconColor="text-orange-500"
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <StatsCard
                  title="Last Run Success"
                  value={
                    jobs.filter((job) => job.lastRunStatus === "completed")
                      .length
                  }
                  icon={CheckCircle}
                  iconColor="text-green-500"
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <StatsCard
                  title="Last Run Failed"
                  value={
                    jobs.filter((job) => job.lastRunStatus === "failed").length
                  }
                  icon={XCircle}
                  iconColor="text-red-500"
                />
              </motion.div>
            </div>
          </div>

          {/* Jobs Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="mb-[var(--spacing-2xl)]"
          >
            <div className="space-tight">
              <div className="flex justify-between items-center mb-[var(--spacing-xl)]">
                <div className="flex items-center gap-[var(--spacing-sm)]">
                  <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center">
                    <Database className="w-3 h-3 text-white" />
                  </div>
                  <h2 className="text-lg font-semibold text-foreground">
                    Data Pulling Jobs
                  </h2>
                  <Chip color="primary" variant="flat" size="sm">
                    {jobs.length} Total
                  </Chip>
                </div>
              </div>
              <EnhancedJobsDisplay
                jobs={jobs}
                sequences={sequences}
                isJobRunning={isJobRunning}
                systemStatus={systemStatus}
                onRunJob={runJob}
                onCancelJob={cancelJob}
                onViewJobDetails={viewJobDetails}
              />
            </div>
          </motion.div>

          {/* Scheduler Controls */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
            className="mb-[var(--spacing-2xl)]"
          >
            <SchedulerControls
              jobs={jobs}
              sequences={sequences}
              onStopJob={stopJob}
              onStartJob={startJob}
              onStopSequence={stopSequence}
              onStartSequence={startSequence}
            />
          </motion.div>

          {/* Job Details Modal */}
          <JobDetailsModal
            isOpen={isOpen}
            onClose={handleModalClose}
            job={selectedJob || null}
          />

          {/* Settings Modal */}
          <SettingsModal isOpen={isSettingsOpen} onClose={onSettingsClose} />

          {/* Job Admin Modal */}
          <JobAdminModal isOpen={isAdminOpen} onClose={onAdminClose} />
        </div>
      </div>
    </div>
  );
}
