// Script to update job 4 to remove organizational unit dependency
import { saveJobDefinition, loadJobDefinition } from "./src/lib/jobPersistence";
import { initializeDatabase } from "./src/lib/database";

async function updateJob4() {
  try {
    console.log("Initializing database...");
    await initializeDatabase();

    console.log("Loading current job 4 definition...");
    const job4 = await loadJobDefinition("4");

    if (!job4) {
      console.error("Job 4 not found in database");
      process.exit(1);
    }

    console.log("Current job configuration:");
    console.log("- Name:", job4.name);
    console.log("- Description:", job4.description);
    console.log("- Remote Path:", job4.dataSource.sftp?.remotePath);
    console.log(
      "- Organizational Units:",
      (job4.dataSource.options as any)?.organisationalUnits?.length || 0
    );

    // Update the job to remove organizational unit dependency
    job4.name = "ADK RKAKL 2025 Auto-Discovery Download";
    job4.description =
      "Automatically discover and download ADK RKAKL 2025 files from all organizational folders via SFTP using directory browsing";

    // Update remote path to 2025
    if (job4.dataSource.sftp) {
      job4.dataSource.sftp.remotePath = "adk_rkakl2025";
    }

    // Update local path to 2025
    if (job4.destination.localPath) {
      job4.destination.localPath = "C:/KUMPULAN_ADK/ADK_2025_DIPA";
    }

    // Remove organizational units from options since we no longer need them
    if (job4.dataSource.options) {
      delete job4.dataSource.options.organisationalUnits;

      // Add new options for the auto-discovery approach
      job4.dataSource.options = {
        ...job4.dataSource.options,
        autoDiscovery: true,
        browseAllFolders: true,
        skipExisting: true,
        createDirectories: true,
        trackMetadata: true,
      };
    }

    console.log("\nUpdating job 4 with new configuration...");
    await saveJobDefinition(job4);

    console.log("✅ Job 4 updated successfully!");
    console.log("\nNew configuration:");
    console.log("- Name:", job4.name);
    console.log("- Description:", job4.description);
    console.log("- Remote Path:", job4.dataSource.sftp?.remotePath);
    console.log("- Local Path:", job4.destination.localPath);
    console.log("- Auto Discovery:", job4.dataSource.options?.autoDiscovery);
    console.log(
      "- Browse All Folders:",
      job4.dataSource.options?.browseAllFolders
    );

    process.exit(0);
  } catch (error) {
    console.error("Error:", error);
    process.exit(1);
  }
}

updateJob4();
