@echo off
set /a mulainya=(%TIME:~0,2% * 3600) + (%TIME:~3,2% * 60) + %TIME:~6,2%
ECHO Mulai Tarik ADK dan PDF dari FTP DJA... : %time%  & ECHO. >D:\Software\ftp_schedule\php\log\2025\log_ftp_%date:~4,2%_%date:~7,2%_%date:~10,4%.txt

REM sementara di command krn ftp lagi error
php ambil_ftpdja_2025.php >>D:\Software\ftp_schedule\php\log\2025\log_ftp_%date:~4,2%_%date:~7,2%_%date:~10,4%.txt

REM Tarik nilai pagu DIPA , Digital Stamp dan Pejabat Perbendaharaan
ECHO Tarik nilai pagu DIPA , Digital Stamp dan Pejabat Perbendaharaan & ECHO. >>D:\Software\ftp_schedule\php\log\2025\log_ftp_%date:~4,2%_%date:~7,2%_%date:~10,4%.txt
php tarik_pagu_pdf_2025.php >>D:\Software\ftp_schedule\php\log\2025\log_ftp_%date:~4,2%_%date:~7,2%_%date:~10,4%.txt

REM Isi ADK ke database 234->localhost->dbdipa14
ECHO isi ADK ke database server  & ECHO. >>D:\Software\ftp_schedule\php\log\2025\log_ftp_%date:~4,2%_%date:~7,2%_%date:~10,4%.txt
REM WARNING BUAT EXE UNTUK UPLOAD XML PAKAI PHP DISINI
php update_harian_2025.php  >>D:\Software\ftp_schedule\php\log\2025\log_ftp_%date:~4,2%_%date:~7,2%_%date:~10,4%.txt

REM komen dulu sumary pagu
REM ECHO. >>D:\Software\ftp_schedule\php\log\2025\log_ftp_%date:~4,2%_%date:~7,2%_%date:~10,4%.txt
REM ECHO Lanjut Persiapkan tabel pagu monev ... >>D:\Software\ftp_schedule\php\log\2025\log_ftp_%date:~4,2%_%date:~7,2%_%date:~10,4%.txt
REM cd D:\software\monev\2025
REM call refresh_pagu.bat

ECHO. 
set /a selesainya=(%TIME:~0,2% * 3600) + (%TIME:~3,2% * 60) + %TIME:~6,2%
set /a lamanya=%selesainya% - %mulainya%
ECHO Time used in seconds = %lamanya% detik >>D:\Software\ftp_schedule\php\log\2025\log_ftp_%date:~4,2%_%date:~7,2%_%date:~10,4%.txt

pause