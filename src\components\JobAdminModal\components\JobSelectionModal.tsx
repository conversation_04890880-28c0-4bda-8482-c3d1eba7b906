import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardBody,
  Chip,
} from "@heroui/react";
import { Plus, Database, Server } from "lucide-react";
import { JobDefinition } from "@/lib/jobManager";
import { getAvailableJobsForSequence } from "../utils/jobUtils";

interface JobSelectionModalProps {
  isOpen: boolean;
  jobs: JobDefinition[];
  selectedJobsForSequence: Set<string>;
  onClose: () => void;
  onJobSelectionChange: (jobId: string, isSelected: boolean) => void;
  onConfirm: () => void;
}

export const JobSelectionModal: React.FC<JobSelectionModalProps> = ({
  isOpen,
  jobs,
  selectedJobsForSequence,
  onClose,
  onJobSelectionChange,
  onConfirm,
}) => {
  const availableJobs = getAvailableJobsForSequence(jobs);

  return (
    <Modal
      isOpen={isO<PERSON>}
      onClose={onClose}
      size="2xl"
      scrollBehavior="inside"
    >
      <ModalContent>
        <ModalHeader>
          <div className="flex flex-col">
            <h2 className="text-xl font-bold">Add Jobs to Sequence</h2>
            <p className="text-sm text-gray-500 font-normal">
              Select independent jobs to add to this sequence
            </p>
          </div>
        </ModalHeader>
        <ModalBody>
          {availableJobs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No independent jobs available.</p>
              <p className="text-sm">
                All jobs are either already assigned to sequences or none exist.
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              <p className="text-sm text-gray-600 mb-4">
                Select one or more jobs to add to the sequence. Jobs will be
                added in the order they appear here.
              </p>
              {availableJobs.map((job) => (
                <Card
                  key={job.id}
                  className={`cursor-pointer transition-all ${
                    selectedJobsForSequence.has(job.id)
                      ? "border-2 border-blue-500 bg-blue-50"
                      : "border border-gray-200 hover:border-gray-300"
                  }`}
                  isPressable
                  onPress={() =>
                    onJobSelectionChange(
                      job.id,
                      !selectedJobsForSequence.has(job.id)
                    )
                  }
                >
                  <CardBody className="p-3">
                    <div className="flex items-center gap-3">
                      <div
                        className={`w-6 h-6 rounded border-2 flex items-center justify-center ${
                          selectedJobsForSequence.has(job.id)
                            ? "border-blue-500 bg-blue-500"
                            : "border-gray-300"
                        }`}
                      >
                        {selectedJobsForSequence.has(job.id) && (
                          <span className="text-white text-xs">✓</span>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-semibold text-sm">{job.name}</h4>
                          <div className="flex items-center gap-1">
                            <Chip
                              size="sm"
                              color={job.enabled ? "success" : "default"}
                              variant="flat"
                              className="text-xs"
                            >
                              {job.enabled ? "ON" : "OFF"}
                            </Chip>
                            <Chip
                              size="sm"
                              color={
                                job.dataSource.type === "oracle"
                                  ? "primary"
                                  : "secondary"
                              }
                              variant="flat"
                              className="text-xs"
                            >
                              {job.dataSource.type.toUpperCase()}
                            </Chip>
                          </div>
                        </div>
                        <p className="text-xs text-gray-600">
                          {job.description || "No description"}
                        </p>
                        <div className="flex items-center gap-3 text-xs text-gray-500 mt-1">
                          <span className="font-mono">{job.schedule}</span>
                          <span>ID: {job.id}</span>
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        {job.dataSource.type === "oracle" ? (
                          <Database className="w-4 h-4 text-blue-500" />
                        ) : (
                          <Server className="w-4 h-4 text-green-500" />
                        )}
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <div className="flex items-center justify-between w-full">
            <div className="text-sm text-gray-600">
              {selectedJobsForSequence.size > 0 && (
                <span>{selectedJobsForSequence.size} job(s) selected</span>
              )}
            </div>
            <div className="flex gap-2">
              <Button variant="flat" onPress={onClose}>
                Cancel
              </Button>
              <Button
                color="primary"
                onPress={onConfirm}
                isDisabled={selectedJobsForSequence.size === 0}
                startContent={<Plus className="w-4 h-4" />}
              >
                Add{" "}
                {selectedJobsForSequence.size > 0
                  ? `${selectedJobsForSequence.size} `
                  : ""}
                Job{selectedJobsForSequence.size !== 1 ? "s" : ""}
              </Button>
            </div>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
