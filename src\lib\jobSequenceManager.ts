import { JobSequenceExecution, SequenceExecutionState } from "./jobManager";
import {
  loadJobSequence,
  saveJobSequenceExecution,
} from "./sequencePersistence";
import { getJobDefinition } from "./jobManager";
import { runDataPullingJob } from "./jobRunner";
import { logger } from "./jobManager";

/**
 * JobSequenceManager handles the execution and state management of job sequences.
 * It ensures jobs in a sequence run in order and handles failure scenarios.
 */
class JobSequenceManager {
  private static instance: JobSequenceManager;
  private activeSequences: Map<string, SequenceExecutionState> = new Map();

  private constructor() {}

  public static getInstance(): JobSequenceManager {
    if (!JobSequenceManager.instance) {
      JobSequenceManager.instance = new JobSequenceManager();
    }
    return JobSequenceManager.instance;
  }

  /**
   * Execute a job sequence from the beginning
   */
  async executeSequence(
    sequenceId: string,
    triggerType: "manual" | "automatic" = "automatic"
  ): Promise<string> {
    logger.info(`Starting sequence execution: ${sequenceId}`, {
      sequenceId,
      triggerType,
    });

    // Check if sequence is already running
    if (this.activeSequences.has(sequenceId)) {
      const activeSequence = this.activeSequences.get(sequenceId);
      const error = `Sequence ${sequenceId} is already running`;
      logger.warn(error, {
        sequenceId,
        activeSequenceExecutionId: activeSequence?.execution.id,
        activeSequenceStartTime: activeSequence?.execution.startTime,
        activeSequenceCurrentJob: activeSequence?.execution.currentJobId,
        totalActiveSequences: this.activeSequences.size,
      });
      throw new Error(error);
    }

    // Load sequence definition
    const sequence = await loadJobSequence(sequenceId);
    if (!sequence) {
      const error = `Sequence not found: ${sequenceId}`;
      logger.error(error);
      throw new Error(error);
    }

    if (!sequence.enabled) {
      const error = `Sequence ${sequenceId} is disabled`;
      logger.warn(error);
      throw new Error(error);
    }

    if (sequence.jobs.length === 0) {
      const error = `Sequence ${sequenceId} has no jobs`;
      logger.warn(error);
      throw new Error(error);
    }

    // Create sequence execution record
    const executionId = `seq-${sequenceId}-${Date.now()}`;
    const execution: JobSequenceExecution = {
      id: executionId,
      sequenceId,
      status: "running",
      currentJobId: sequence.jobs[0],
      currentJobOrder: 1,
      startTime: new Date(),
      triggerType,
    };

    // Save execution to database
    await saveJobSequenceExecution(execution);

    // Create execution state for tracking
    const executionState: SequenceExecutionState = {
      execution,
      sequence,
      currentJobIndex: 0,
      retryCount: 0,
    };

    // Track active sequence
    this.activeSequences.set(sequenceId, executionState);

    try {
      // Start the first job
      await this.executeNextJob(sequenceId);
      return executionId;
    } catch (error) {
      // Clean up on immediate failure
      this.activeSequences.delete(sequenceId);

      // Update execution status
      execution.status = "failed";
      execution.endTime = new Date();
      execution.duration = Math.round(
        (execution.endTime.getTime() - execution.startTime.getTime()) / 1000
      );
      execution.errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      await saveJobSequenceExecution(execution);

      throw error;
    }
  }

  /**
   * Handle job completion and continue sequence execution
   */
  async onJobCompleted(jobId: string, success: boolean): Promise<void> {
    logger.debug(`Job completed: ${jobId}, success: ${success}`);

    // Find which sequence this job belongs to
    const sequenceId = await this.findSequenceForJob(jobId);
    if (!sequenceId) {
      // Job is not part of a sequence, nothing to do
      return;
    }

    const executionState = this.activeSequences.get(sequenceId);
    if (!executionState) {
      // Sequence is not currently active
      logger.warn(
        `Job ${jobId} completed but sequence ${sequenceId} is not active`
      );
      return;
    }

    const { execution: _execution, sequence: _sequence } = executionState;

    if (success) {
      await this.handleJobSuccess(sequenceId, executionState);
    } else {
      await this.handleJobFailure(sequenceId, executionState);
    }
  }

  /**
   * Handle successful job completion
   */
  private async handleJobSuccess(
    sequenceId: string,
    executionState: SequenceExecutionState
  ): Promise<void> {
    const { execution, sequence } = executionState;

    logger.info(
      `Job ${execution.currentJobId} in sequence ${sequenceId} completed successfully`
    );

    // Reset retry count on success
    executionState.retryCount = 0;

    // Check if this was the last job in the sequence
    if (executionState.currentJobIndex >= sequence.jobs.length - 1) {
      // Sequence completed successfully
      await this.completeSequence(sequenceId, executionState, "completed");
      return;
    }

    // Move to next job
    executionState.currentJobIndex++;
    execution.currentJobId = sequence.jobs[executionState.currentJobIndex];
    execution.currentJobOrder = executionState.currentJobIndex + 1;

    // Update execution in database
    await saveJobSequenceExecution(execution);

    // Execute next job
    try {
      await this.executeNextJob(sequenceId);
    } catch (error) {
      logger.error(
        `Failed to start next job in sequence ${sequenceId}:`,
        error
      );
      await this.completeSequence(
        sequenceId,
        executionState,
        "failed",
        error instanceof Error ? error.message : "Failed to start next job"
      );
    }
  }

  /**
   * Handle failed job completion
   */
  private async handleJobFailure(
    sequenceId: string,
    executionState: SequenceExecutionState
  ): Promise<void> {
    const { execution, sequence } = executionState;

    logger.warn(
      `Job ${execution.currentJobId} in sequence ${sequenceId} failed`
    );

    switch (sequence.onFailure) {
      case "stop":
        await this.completeSequence(
          sequenceId,
          executionState,
          "failed",
          `Job ${execution.currentJobId} failed, stopping sequence`
        );
        break;

      case "continue":
        logger.info(`Continuing sequence ${sequenceId} despite job failure`);
        // Move to next job (same logic as success)
        await this.handleJobSuccess(sequenceId, executionState);
        break;

      case "retry":
        await this.handleJobRetry(sequenceId, executionState);
        break;

      default:
        logger.error(`Unknown failure action: ${sequence.onFailure}`);
        await this.completeSequence(
          sequenceId,
          executionState,
          "failed",
          `Unknown failure action: ${sequence.onFailure}`
        );
    }
  }

  /**
   * Handle job retry logic
   */
  private async handleJobRetry(
    sequenceId: string,
    executionState: SequenceExecutionState
  ): Promise<void> {
    const { execution, sequence } = executionState;

    if (executionState.retryCount >= sequence.maxRetries) {
      logger.warn(
        `Max retries (${sequence.maxRetries}) reached for job ${execution.currentJobId} in sequence ${sequenceId}`
      );
      await this.completeSequence(
        sequenceId,
        executionState,
        "failed",
        `Job ${execution.currentJobId} failed after ${sequence.maxRetries} retries`
      );
      return;
    }

    executionState.retryCount++;
    logger.info(
      `Retrying job ${execution.currentJobId} in sequence ${sequenceId} (attempt ${executionState.retryCount}/${sequence.maxRetries})`
    );

    // Update execution in database
    await saveJobSequenceExecution(execution);

    // Retry the current job
    try {
      await this.executeNextJob(sequenceId);
    } catch (error) {
      logger.error(`Failed to retry job in sequence ${sequenceId}:`, error);
      await this.completeSequence(
        sequenceId,
        executionState,
        "failed",
        error instanceof Error ? error.message : "Failed to retry job"
      );
    }
  }

  /**
   * Execute the next job in the sequence
   */
  private async executeNextJob(sequenceId: string): Promise<void> {
    const executionState = this.activeSequences.get(sequenceId);
    if (!executionState) {
      throw new Error(`Sequence ${sequenceId} is not active`);
    }

    const { execution, sequence } = executionState;
    const jobId = execution.currentJobId;

    if (!jobId) {
      throw new Error(`No current job for sequence ${sequenceId}`);
    }

    logger.info(
      `Executing job ${jobId} in sequence ${sequenceId} (${execution.currentJobOrder}/${sequence.jobs.length})`
    );

    // Verify job exists and is enabled
    const jobDef = await getJobDefinition(jobId);
    if (!jobDef) {
      throw new Error(`Job ${jobId} not found`);
    }

    if (!jobDef.enabled) {
      throw new Error(`Job ${jobId} is disabled`);
    }

    // Execute the job
    // Note: The job runner will call onJobCompleted when the job finishes
    await runDataPullingJob(jobId, execution.triggerType);
  }

  /**
   * Complete sequence execution
   */
  private async completeSequence(
    sequenceId: string,
    executionState: SequenceExecutionState,
    status: "completed" | "failed" | "stopped",
    errorMessage?: string
  ): Promise<void> {
    const { execution } = executionState;

    execution.status = status;
    execution.endTime = new Date();
    execution.duration = Math.round(
      (execution.endTime.getTime() - execution.startTime.getTime()) / 1000
    );

    if (errorMessage) {
      execution.errorMessage = errorMessage;
    }

    // Save final execution state
    await saveJobSequenceExecution(execution);

    // Remove from active sequences
    this.activeSequences.delete(sequenceId);

    logger.info(`Sequence ${sequenceId} ${status}`, {
      sequenceId,
      executionId: execution.id,
      duration: execution.duration,
      status,
      errorMessage,
    });
  }

  /**
   * Find which sequence a job belongs to (if any)
   */
  private async findSequenceForJob(jobId: string): Promise<string | null> {
    try {
      const jobDef = await getJobDefinition(jobId);
      return jobDef?.sequenceConfig?.sequenceId || null;
    } catch (error) {
      logger.error(`Failed to find sequence for job ${jobId}:`, error);
      return null;
    }
  }

  /**
   * Stop a running sequence
   */
  async stopSequence(sequenceId: string): Promise<void> {
    const executionState = this.activeSequences.get(sequenceId);
    if (!executionState) {
      throw new Error(`Sequence ${sequenceId} is not running`);
    }

    logger.info(`Stopping sequence ${sequenceId}`);
    await this.completeSequence(
      sequenceId,
      executionState,
      "stopped",
      "Manually stopped"
    );
  }

  /**
   * Get current sequence status
   */
  getSequenceStatus(sequenceId: string): SequenceExecutionState | null {
    return this.activeSequences.get(sequenceId) || null;
  }

  /**
   * Get all active sequences
   */
  getActiveSequences(): Map<string, SequenceExecutionState> {
    return new Map(this.activeSequences);
  }

  /**
   * Check if a sequence is currently running
   */
  isSequenceRunning(sequenceId: string): boolean {
    return this.activeSequences.has(sequenceId);
  }

  /**
   * Clear all active sequences (used when system stops)
   */
  clearAllActiveSequences(): void {
    logger.info(
      `Clearing ${this.activeSequences.size} active sequences due to system stop`
    );
    this.activeSequences.clear();
  }

  /**
   * Get debug information about active sequences
   */
  getDebugInfo(): {
    activeSequenceCount: number;
    activeSequences: Array<{
      sequenceId: string;
      executionId: string;
      startTime: Date;
      currentJobId?: string;
      currentJobOrder?: number;
    }>;
  } {
    const activeSequences = Array.from(this.activeSequences.entries()).map(
      ([sequenceId, state]) => ({
        sequenceId,
        executionId: state.execution.id,
        startTime: state.execution.startTime,
        currentJobId: state.execution.currentJobId,
        currentJobOrder: state.execution.currentJobOrder,
      })
    );

    return {
      activeSequenceCount: this.activeSequences.size,
      activeSequences,
    };
  }
}

export default JobSequenceManager;
