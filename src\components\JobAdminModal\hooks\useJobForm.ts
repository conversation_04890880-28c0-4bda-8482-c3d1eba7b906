import { JobDefinition } from "@/lib/jobManager";
import { updateNestedField as updateNestedFieldUtil } from "../utils/formUtils";

export const useJobForm = (
  editedJob: JobDefinition | null,
  setEditedJob: (job: JobDefinition | null) => void
) => {
  const updateJobField = (
    field: keyof JobDefinition,
    value: string | boolean | number
  ) => {
    if (!editedJob) return;
    setEditedJob({
      ...editedJob,
      [field]: value,
    });
  };

  const updateNestedField = (path: string, value: unknown) => {
    if (!editedJob) return;
    const updatedJob = updateNestedFieldUtil(
      editedJob as unknown as Record<string, unknown>,
      path,
      value
    ) as unknown as JobDefinition;
    setEditedJob(updatedJob);
  };

  return {
    updateJobField,
    updateNestedField,
  };
};
