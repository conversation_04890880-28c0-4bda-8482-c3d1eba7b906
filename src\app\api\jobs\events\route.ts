import { NextRequest } from "next/server";
import { getJobStatus } from "@/lib/jobManager";
import { addConnection, removeConnection } from "@/lib/sseManager";

export async function GET(request: NextRequest) {
  // Set up Server-Sent Events
  const encoder = new TextEncoder();

  const customReadable = new ReadableStream({
    start(controller) {
      const writer = {
        write: async (chunk: string) => {
          controller.enqueue(encoder.encode(chunk));
        },
      };

      // Add this connection to our set
      addConnection(writer as WritableStreamDefaultWriter);

      // Send initial data
      Promise.all([
        getJobStatus(),
        import("@/lib/sequencePersistence").then(({ loadJobSequences }) =>
          loadJobSequences()
        ),
      ])
        .then(([jobs, sequences]) => {
          // Send initial job data
          const jobData = JSON.stringify({ type: "jobUpdate", jobs });
          writer.write(`data: ${jobData}\n\n`);

          // Send initial sequence data
          const sequenceData = JSON.stringify({
            type: "sequenceUpdate",
            sequences,
          });
          writer.write(`data: ${sequenceData}\n\n`);
        })
        .catch(console.error);

      // Send periodic heartbeat
      const heartbeat = setInterval(() => {
        writer
          .write(
            `data: ${JSON.stringify({
              type: "heartbeat",
              timestamp: Date.now(),
            })}\n\n`
          )
          .catch(() => {
            // Connection is dead, clean up
            clearInterval(heartbeat);
            removeConnection(writer as WritableStreamDefaultWriter);
          });
      }, 30000); // Every 30 seconds

      // Clean up when connection closes
      request.signal.addEventListener("abort", () => {
        clearInterval(heartbeat);
        removeConnection(writer as WritableStreamDefaultWriter);
        controller.close();
      });
    },
  });

  return new Response(customReadable, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "Cache-Control",
    },
  });
}
