"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  Input,
  Textarea,
  Switch,
  Card,
  CardBody,
  CardHeader,
  Chip,
} from "@heroui/react";
import { Edit, Save, X, Database, Server } from "lucide-react";
import { JobDefinition } from "@/lib/jobManager";
import axios from "axios";

interface JobAdminModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function JobAdminModal({ isOpen, onClose }: JobAdminModalProps) {
  const [jobs, setJobs] = useState<JobDefinition[]>([]);
  const [selectedJob, setSelectedJob] = useState<JobDefinition | null>(null);
  const [editedJob, setEditedJob] = useState<JobDefinition | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch jobs when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchJobs();
    }
  }, [isOpen]);

  const fetchJobs = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await axios.get("/api/admin/jobs");
      setJobs(response.data.jobs);
    } catch (error) {
      console.error("Error fetching jobs:", error);
      setError("Failed to fetch jobs");
    } finally {
      setIsLoading(false);
    }
  };

  const handleJobSelect = (job: JobDefinition) => {
    setSelectedJob(job);
    setEditedJob({ ...job });
    setIsEditing(false);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedJob(selectedJob ? { ...selectedJob } : null);
  };

  const handleSave = async () => {
    if (!editedJob) return;

    setIsSaving(true);
    try {
      await axios.put(`/api/admin/jobs/${editedJob.id}`, editedJob);

      // Update local state
      setJobs(jobs.map((job) => (job.id === editedJob.id ? editedJob : job)));
      setSelectedJob(editedJob);
      setIsEditing(false);

      alert("Job updated successfully!");
    } catch (error) {
      console.error("Error saving job:", error);
      alert("Failed to save job. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const updateJobField = (
    field: keyof JobDefinition,
    value: string | boolean | number
  ) => {
    if (!editedJob) return;
    setEditedJob({
      ...editedJob,
      [field]: value,
    });
  };

  const updateNestedField = (path: string, value: string | number) => {
    if (!editedJob) return;

    const pathParts = path.split(".");
    const newJob = { ...editedJob };
    let current = newJob as Record<string, unknown>;

    for (let i = 0; i < pathParts.length - 1; i++) {
      if (!current[pathParts[i]]) {
        current[pathParts[i]] = {};
      }
      current = current[pathParts[i]] as Record<string, unknown>;
    }

    current[pathParts[pathParts.length - 1]] = value;
    setEditedJob(newJob);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="5xl"
      scrollBehavior="inside"
      backdrop="blur"
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            <span>Job Administration</span>
          </div>
          <p className="text-sm text-gray-500 font-normal">
            Manage job definitions and configurations
          </p>
        </ModalHeader>

        <ModalBody className="gap-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Jobs List */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">Jobs</h3>
                </CardHeader>
                <CardBody className="gap-2">
                  {isLoading ? (
                    <div className="text-center py-4">Loading jobs...</div>
                  ) : (
                    jobs.map((job) => (
                      <div
                        key={job.id}
                        className={`p-3 rounded-lg cursor-pointer transition-colors ${
                          selectedJob?.id === job.id
                            ? "bg-primary-100 border border-primary-300"
                            : "bg-gray-50 hover:bg-gray-100"
                        }`}
                        onClick={() => handleJobSelect(job)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-sm">{job.name}</p>
                            <p className="text-xs text-gray-500">{job.id}</p>
                          </div>
                          <div className="flex flex-col items-end gap-1">
                            <Chip
                              size="sm"
                              color={job.enabled ? "success" : "default"}
                              variant="flat"
                            >
                              {job.enabled ? "Enabled" : "Disabled"}
                            </Chip>
                            <Chip
                              size="sm"
                              color={
                                job.dataSource.type === "oracle" ||
                                job.dataSource.type === "mysql"
                                  ? "primary"
                                  : "secondary"
                              }
                              variant="flat"
                              startContent={
                                job.dataSource.type === "oracle" ||
                                job.dataSource.type === "mysql" ? (
                                  <Database className="w-3 h-3" />
                                ) : (
                                  <Server className="w-3 h-3" />
                                )
                              }
                            >
                              {job.dataSource.type.toUpperCase()}
                            </Chip>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </CardBody>
              </Card>
            </div>

            {/* Job Details */}
            <div className="lg:col-span-2">
              {selectedJob && editedJob ? (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between w-full">
                      <h3 className="text-lg font-semibold">Job Details</h3>
                      <div className="flex gap-2">
                        {!isEditing ? (
                          <Button
                            color="primary"
                            size="sm"
                            startContent={<Edit className="w-4 h-4" />}
                            onPress={handleEdit}
                          >
                            Edit
                          </Button>
                        ) : (
                          <>
                            <Button
                              color="success"
                              size="sm"
                              startContent={<Save className="w-4 h-4" />}
                              onPress={handleSave}
                              isLoading={isSaving}
                            >
                              Save
                            </Button>
                            <Button
                              color="danger"
                              variant="flat"
                              size="sm"
                              startContent={<X className="w-4 h-4" />}
                              onPress={handleCancel}
                            >
                              Cancel
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardBody className="space-y-4">
                    {/* Basic Info */}
                    <div className="space-y-3">
                      <h4 className="text-md font-semibold">
                        Basic Information
                      </h4>

                      <Input
                        label="Job Name"
                        value={editedJob.name}
                        onChange={(e) => updateJobField("name", e.target.value)}
                        isDisabled={!isEditing}
                      />

                      <Textarea
                        label="Description"
                        value={editedJob.description}
                        onChange={(e) =>
                          updateJobField("description", e.target.value)
                        }
                        isDisabled={!isEditing}
                      />

                      <Input
                        label="Schedule (Cron)"
                        value={editedJob.schedule}
                        onChange={(e) =>
                          updateJobField("schedule", e.target.value)
                        }
                        isDisabled={!isEditing}
                        placeholder="0 2 * * *"
                      />

                      <Switch
                        isSelected={editedJob.enabled}
                        onValueChange={(value) =>
                          updateJobField("enabled", value)
                        }
                        isDisabled={!isEditing}
                      >
                        Job Enabled
                      </Switch>
                    </div>

                    {/* Data Source Type */}
                    <div className="space-y-3">
                      <h4 className="text-md font-semibold">Data Source</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium">Type</label>
                          <Chip
                            color={
                              selectedJob.dataSource.type === "oracle" ||
                              selectedJob.dataSource.type === "mysql"
                                ? "primary"
                                : "secondary"
                            }
                            variant="flat"
                            startContent={
                              selectedJob.dataSource.type === "oracle" ||
                              selectedJob.dataSource.type === "mysql" ? (
                                <Database className="w-3 h-3" />
                              ) : (
                                <Server className="w-3 h-3" />
                              )
                            }
                          >
                            {selectedJob.dataSource.type.toUpperCase()}
                          </Chip>
                        </div>
                      </div>
                    </div>

                    {/* Oracle Configuration */}
                    {selectedJob.dataSource.type === "oracle" &&
                      selectedJob.dataSource.oracle && (
                        <div className="space-y-3">
                          <h4 className="text-md font-semibold">
                            Oracle Configuration
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <Input
                              label="Host"
                              value={editedJob.dataSource.oracle?.host || ""}
                              onChange={(e) =>
                                updateNestedField(
                                  "dataSource.oracle.host",
                                  e.target.value
                                )
                              }
                              isDisabled={!isEditing}
                            />
                            <Input
                              label="Port"
                              type="number"
                              value={
                                editedJob.dataSource.oracle?.port?.toString() ||
                                ""
                              }
                              onChange={(e) =>
                                updateNestedField(
                                  "dataSource.oracle.port",
                                  parseInt(e.target.value)
                                )
                              }
                              isDisabled={!isEditing}
                            />
                            <Input
                              label="Service Name"
                              value={
                                editedJob.dataSource.oracle?.serviceName || ""
                              }
                              onChange={(e) =>
                                updateNestedField(
                                  "dataSource.oracle.serviceName",
                                  e.target.value
                                )
                              }
                              isDisabled={!isEditing}
                            />
                            <Input
                              label="Username"
                              value={
                                editedJob.dataSource.oracle?.username || ""
                              }
                              onChange={(e) =>
                                updateNestedField(
                                  "dataSource.oracle.username",
                                  e.target.value
                                )
                              }
                              isDisabled={!isEditing}
                            />
                          </div>
                          <Textarea
                            label="SQL Query"
                            value={editedJob.dataSource.oracle?.query || ""}
                            onChange={(e) =>
                              updateNestedField(
                                "dataSource.oracle.query",
                                e.target.value
                              )
                            }
                            isDisabled={!isEditing}
                            minRows={3}
                          />
                        </div>
                      )}

                    {/* MySQL Configuration */}
                    {selectedJob.dataSource.type === "mysql" &&
                      selectedJob.dataSource.mysql && (
                        <div className="space-y-3">
                          <h4 className="text-md font-semibold">
                            MySQL Configuration
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <Input
                              label="Host"
                              value={editedJob.dataSource.mysql?.host || ""}
                              onChange={(e) =>
                                updateNestedField(
                                  "dataSource.mysql.host",
                                  e.target.value
                                )
                              }
                              isDisabled={!isEditing}
                            />
                            <Input
                              label="Port"
                              type="number"
                              value={
                                editedJob.dataSource.mysql?.port?.toString() ||
                                ""
                              }
                              onChange={(e) =>
                                updateNestedField(
                                  "dataSource.mysql.port",
                                  parseInt(e.target.value) || 3306
                                )
                              }
                              isDisabled={!isEditing}
                            />
                          </div>
                          <Input
                            label="Database Name"
                            value={editedJob.dataSource.mysql?.database || ""}
                            onChange={(e) =>
                              updateNestedField(
                                "dataSource.mysql.database",
                                e.target.value
                              )
                            }
                            isDisabled={!isEditing}
                          />
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <Input
                              label="Username"
                              value={editedJob.dataSource.mysql?.username || ""}
                              onChange={(e) =>
                                updateNestedField(
                                  "dataSource.mysql.username",
                                  e.target.value
                                )
                              }
                              isDisabled={!isEditing}
                            />
                            <Input
                              label="Password"
                              type="password"
                              value={editedJob.dataSource.mysql?.password || ""}
                              onChange={(e) =>
                                updateNestedField(
                                  "dataSource.mysql.password",
                                  e.target.value
                                )
                              }
                              isDisabled={!isEditing}
                            />
                          </div>
                          <Textarea
                            label="SQL Query"
                            value={editedJob.dataSource.mysql?.query || ""}
                            onChange={(e) =>
                              updateNestedField(
                                "dataSource.mysql.query",
                                e.target.value
                              )
                            }
                            isDisabled={!isEditing}
                            minRows={3}
                          />
                        </div>
                      )}

                    {/* SFTP Configuration */}
                    {selectedJob.dataSource.type === "sftp" &&
                      selectedJob.dataSource.sftp && (
                        <div className="space-y-3">
                          <h4 className="text-md font-semibold">
                            SFTP Configuration
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <Input
                              label="Host"
                              value={editedJob.dataSource.sftp?.host || ""}
                              onChange={(e) =>
                                updateNestedField(
                                  "dataSource.sftp.host",
                                  e.target.value
                                )
                              }
                              isDisabled={!isEditing}
                            />
                            <Input
                              label="Port"
                              type="number"
                              value={
                                editedJob.dataSource.sftp?.port?.toString() ||
                                ""
                              }
                              onChange={(e) =>
                                updateNestedField(
                                  "dataSource.sftp.port",
                                  parseInt(e.target.value)
                                )
                              }
                              isDisabled={!isEditing}
                            />
                            <Input
                              label="Username"
                              value={editedJob.dataSource.sftp?.username || ""}
                              onChange={(e) =>
                                updateNestedField(
                                  "dataSource.sftp.username",
                                  e.target.value
                                )
                              }
                              isDisabled={!isEditing}
                            />
                            <Input
                              label="Remote Path"
                              value={
                                editedJob.dataSource.sftp?.remotePath || ""
                              }
                              onChange={(e) =>
                                updateNestedField(
                                  "dataSource.sftp.remotePath",
                                  e.target.value
                                )
                              }
                              isDisabled={!isEditing}
                            />
                          </div>
                        </div>
                      )}

                    {/* Destination Configuration */}
                    <div className="space-y-3">
                      <h4 className="text-md font-semibold">Destination</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium">Type</label>
                          <p className="text-sm text-gray-600">
                            {selectedJob.destination.type}
                          </p>
                        </div>
                        {selectedJob.destination.localPath && (
                          <Input
                            label="Local Path"
                            value={editedJob.destination.localPath || ""}
                            onChange={(e) =>
                              updateNestedField(
                                "destination.localPath",
                                e.target.value
                              )
                            }
                            isDisabled={!isEditing}
                          />
                        )}
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ) : (
                <Card>
                  <CardBody>
                    <div className="text-center py-8 text-gray-500">
                      Select a job to view and edit its details
                    </div>
                  </CardBody>
                </Card>
              )}
            </div>
          </div>
        </ModalBody>

        <ModalFooter>
          <Button color="danger" variant="flat" onPress={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
