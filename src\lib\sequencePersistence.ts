import { executeQuery, executeUpdate } from "./database";
import { JobSequence, JobSequenceExecution } from "./jobManager";
import { logger } from "./jobManager";

// Types for database operations
interface DbJobSequence {
  id: string;
  name: string;
  description: string | null;
  schedule_cron: string | null;
  enabled: boolean;
  on_failure: "stop" | "continue" | "retry";
  max_retries: number;
  created_at: Date;
  updated_at: Date;
}

interface DbJobSequenceExecution {
  id: string;
  sequence_id: string;
  status: "running" | "completed" | "failed" | "stopped";
  current_job_id: string | null;
  current_job_order: number | null;
  start_time: Date;
  end_time: Date | null;
  duration_seconds: number | null;
  trigger_type: "manual" | "automatic";
  error_message: string | null;
  created_at: Date;
  updated_at: Date;
}

// Convert database row to JobSequence
function dbRowToJobSequence(
  row: DbJobSequence,
  jobs: string[] = []
): JobSequence {
  return {
    id: row.id,
    name: row.name,
    description: row.description || "",
    schedule: row.schedule_cron || undefined,
    enabled: row.enabled,
    onFailure: row.on_failure,
    maxRetries: row.max_retries,
    jobs,
    createdAt: row.created_at,
    updatedAt: row.updated_at,
  };
}

// Convert database row to JobSequenceExecution
function dbRowToJobSequenceExecution(
  row: DbJobSequenceExecution
): JobSequenceExecution {
  return {
    id: row.id,
    sequenceId: row.sequence_id,
    status: row.status,
    currentJobId: row.current_job_id || undefined,
    currentJobOrder: row.current_job_order || undefined,
    startTime: row.start_time,
    endTime: row.end_time || undefined,
    duration: row.duration_seconds || undefined,
    triggerType: row.trigger_type,
    errorMessage: row.error_message || undefined,
  };
}

// Job Sequences CRUD operations
export async function saveJobSequence(sequence: JobSequence): Promise<void> {
  try {
    const query = `
      INSERT INTO job_sequences (
        id, name, description, schedule_cron, enabled, 
        on_failure, max_retries
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        description = VALUES(description),
        schedule_cron = VALUES(schedule_cron),
        enabled = VALUES(enabled),
        on_failure = VALUES(on_failure),
        max_retries = VALUES(max_retries),
        updated_at = CURRENT_TIMESTAMP
    `;

    const params = [
      sequence.id,
      sequence.name,
      sequence.description,
      sequence.schedule || null,
      sequence.enabled,
      sequence.onFailure,
      sequence.maxRetries,
    ];

    await executeUpdate(query, params);
    logger.info(`Job sequence saved: ${sequence.id} - ${sequence.name}`);
  } catch (error) {
    logger.error(`Failed to save job sequence ${sequence.id}:`, error);
    throw error;
  }
}

export async function loadJobSequence(
  sequenceId: string
): Promise<JobSequence | null> {
  try {
    const query = "SELECT * FROM job_sequences WHERE id = ?";
    const rows = await executeQuery<DbJobSequence>(query, [sequenceId]);

    if (rows.length === 0) {
      return null;
    }

    const sequenceRow = rows[0];

    // Load jobs in this sequence
    const jobs = await getSequenceJobs(sequenceId);

    return dbRowToJobSequence(sequenceRow, jobs);
  } catch (error) {
    logger.error(`Failed to load job sequence ${sequenceId}:`, error);
    throw error;
  }
}

export async function loadJobSequences(): Promise<JobSequence[]> {
  try {
    const query = "SELECT * FROM job_sequences ORDER BY name";
    const rows = await executeQuery<DbJobSequence>(query);

    const sequences: JobSequence[] = [];

    for (const row of rows) {
      const jobs = await getSequenceJobs(row.id);
      sequences.push(dbRowToJobSequence(row, jobs));
    }

    return sequences;
  } catch (error) {
    logger.error("Failed to load job sequences:", error);
    throw error;
  }
}

export async function deleteJobSequence(sequenceId: string): Promise<void> {
  try {
    // First, remove sequence assignment from jobs
    await executeUpdate(
      "UPDATE job_definitions SET sequence_id = NULL, sequence_order = NULL WHERE sequence_id = ?",
      [sequenceId]
    );

    // Then delete the sequence
    const query = "DELETE FROM job_sequences WHERE id = ?";
    await executeUpdate(query, [sequenceId]);

    logger.info(`Job sequence deleted: ${sequenceId}`);
  } catch (error) {
    logger.error(`Failed to delete job sequence ${sequenceId}:`, error);
    throw error;
  }
}

// Get jobs in a sequence, ordered by sequence_order
export async function getSequenceJobs(sequenceId: string): Promise<string[]> {
  try {
    const query = `
      SELECT id FROM job_definitions 
      WHERE sequence_id = ? 
      ORDER BY sequence_order ASC
    `;
    const rows = await executeQuery<{ id: string }>(query, [sequenceId]);
    return rows.map((row) => row.id);
  } catch (error) {
    logger.error(`Failed to get sequence jobs for ${sequenceId}:`, error);
    throw error;
  }
}

// Update job sequence assignment
export async function updateJobSequenceAssignment(
  jobId: string,
  sequenceId: string | null,
  order: number | null
): Promise<void> {
  try {
    const query = `
      UPDATE job_definitions 
      SET sequence_id = ?, sequence_order = ? 
      WHERE id = ?
    `;
    await executeUpdate(query, [sequenceId, order, jobId]);

    logger.info(
      `Job ${jobId} sequence assignment updated: ${sequenceId}:${order}`
    );
  } catch (error) {
    logger.error(
      `Failed to update job sequence assignment for ${jobId}:`,
      error
    );
    throw error;
  }
}

// Job Sequence Executions CRUD operations
export async function saveJobSequenceExecution(
  execution: JobSequenceExecution
): Promise<void> {
  try {
    const query = `
      INSERT INTO job_sequence_executions (
        id, sequence_id, status, current_job_id, current_job_order,
        start_time, end_time, duration_seconds, trigger_type, error_message
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        current_job_id = VALUES(current_job_id),
        current_job_order = VALUES(current_job_order),
        end_time = VALUES(end_time),
        duration_seconds = VALUES(duration_seconds),
        error_message = VALUES(error_message),
        updated_at = CURRENT_TIMESTAMP
    `;

    const params = [
      execution.id,
      execution.sequenceId,
      execution.status,
      execution.currentJobId || null,
      execution.currentJobOrder || null,
      execution.startTime,
      execution.endTime || null,
      execution.duration || null,
      execution.triggerType,
      execution.errorMessage || null,
    ];

    await executeUpdate(query, params);
    logger.debug(`Job sequence execution saved: ${execution.id}`);
  } catch (error) {
    logger.error(
      `Failed to save job sequence execution ${execution.id}:`,
      error
    );
    throw error;
  }
}

export async function loadJobSequenceExecution(
  executionId: string
): Promise<JobSequenceExecution | null> {
  try {
    const query = "SELECT * FROM job_sequence_executions WHERE id = ?";
    const rows = await executeQuery<DbJobSequenceExecution>(query, [
      executionId,
    ]);

    if (rows.length === 0) {
      return null;
    }

    return dbRowToJobSequenceExecution(rows[0]);
  } catch (error) {
    logger.error(
      `Failed to load job sequence execution ${executionId}:`,
      error
    );
    throw error;
  }
}

export async function getLatestSequenceExecution(
  sequenceId: string
): Promise<JobSequenceExecution | null> {
  try {
    const query = `
      SELECT * FROM job_sequence_executions 
      WHERE sequence_id = ? 
      ORDER BY start_time DESC 
      LIMIT 1
    `;
    const rows = await executeQuery<DbJobSequenceExecution>(query, [
      sequenceId,
    ]);

    if (rows.length === 0) {
      return null;
    }

    return dbRowToJobSequenceExecution(rows[0]);
  } catch (error) {
    logger.error(
      `Failed to get latest sequence execution for ${sequenceId}:`,
      error
    );
    throw error;
  }
}

export async function loadSequenceExecutions(
  sequenceId?: string,
  limit: number = 50
): Promise<JobSequenceExecution[]> {
  try {
    let query = `
      SELECT * FROM job_sequence_executions 
    `;
    const params: unknown[] = [];

    if (sequenceId) {
      query += " WHERE sequence_id = ?";
      params.push(sequenceId);
    }

    // Validate limit to prevent SQL injection
    const safeLimit = Math.max(1, Math.min(1000, Math.floor(limit)));
    query += ` ORDER BY start_time DESC LIMIT ${safeLimit}`;

    const rows = await executeQuery<DbJobSequenceExecution>(query, params);
    return rows.map(dbRowToJobSequenceExecution);
  } catch (error) {
    logger.error("Failed to load sequence executions:", error);
    throw error;
  }
}
