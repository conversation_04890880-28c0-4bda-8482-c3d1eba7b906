// Debug script to check ADK processing job issue
import {
  loadJobDefinitions,
  loadJobDefinition,
} from "./src/lib/jobPersistence";
import { initializeDatabase } from "./src/lib/database";
import { jobHandlerFactory } from "./src/lib/jobs";

async function debugAdkJobs() {
  try {
    console.log("🔍 Debugging ADK Processing Job Issue...\n");

    console.log("Initializing database...");
    await initializeDatabase();

    console.log("Loading job definitions...");
    const jobs = await loadJobDefinitions();

    console.log(`📋 Found ${jobs.length} total jobs\n`);

    // Find ADK processing jobs
    const adkJobs = jobs.filter(
      (job) => job.dataSource.type === "adk_processing"
    );
    console.log(`📊 Found ${adkJobs.length} ADK processing jobs:`);

    for (const job of adkJobs) {
      console.log(`  - ${job.id}: ${job.name} (enabled: ${job.enabled})`);
    }
    console.log();

    // Check the specific failing job
    const failingJobId = "job-1753091554181";
    console.log(`🔍 Checking specific failing job: ${failingJobId}`);
    const failingJob = await loadJobDefinition(failingJobId);

    if (!failingJob) {
      console.log(`❌ Job ${failingJobId} not found in database`);

      // Show all jobs to help identify the correct one
      console.log("\n📋 All jobs in database:");
      jobs.forEach((job) => {
        console.log(
          `  - ${job.id}: ${job.name} (type: ${job.dataSource.type}, enabled: ${job.enabled})`
        );
      });

      return;
    }

    console.log(`📄 Job Details:`);
    console.log(`  Name: ${failingJob.name}`);
    console.log(`  Type: ${failingJob.dataSource.type}`);
    console.log(`  Enabled: ${failingJob.enabled}`);
    console.log();

    // Check job handler registration
    console.log("🔧 Checking job handler registration...");
    const supportedTypes = jobHandlerFactory.getSupportedJobTypes();
    console.log(`Supported job types: ${supportedTypes.join(", ")}`);

    const isAdkSupported =
      jobHandlerFactory.isJobTypeSupported("adk_processing");
    console.log(`ADK processing supported: ${isAdkSupported}`);
    console.log();

    // Try to get the handler and validate
    if (isAdkSupported) {
      console.log("🧪 Testing job handler validation...");
      try {
        const handler = jobHandlerFactory.getHandler("adk_processing");
        console.log(`Handler found: ${handler.constructor.name}`);
        console.log(`Handler jobType: ${handler.jobType}`);

        // Test validation
        const isValid = handler.validateConfig(failingJob);
        console.log(`Configuration valid: ${isValid}`);

        if (!isValid) {
          console.log(
            "\n❌ Configuration validation failed. Checking details..."
          );

          // Check basic validation
          const basicValid = failingJob.dataSource.type === handler.jobType;
          console.log(
            `  Basic type check: ${basicValid} (${failingJob.dataSource.type} === ${handler.jobType})`
          );

          // Check ADK config exists
          const adkConfig = failingJob.dataSource.adk_processing;
          console.log(`  ADK config exists: ${!!adkConfig}`);

          if (adkConfig) {
            console.log(`  ADK config details:`);
            console.log(
              `    sourceDirectory: ${adkConfig.sourceDirectory || "MISSING"}`
            );
            console.log(
              `    extractionPath: ${adkConfig.extractionPath || "MISSING"}`
            );
            console.log(
              `    rarToolPath: ${adkConfig.rarToolPath || "MISSING"}`
            );
            console.log(
              `    fileListDatabase: ${!!adkConfig.fileListDatabase}`
            );

            if (adkConfig.fileListDatabase) {
              const db = adkConfig.fileListDatabase;
              console.log(`      host: ${db.host || "MISSING"}`);
              console.log(`      port: ${db.port || "MISSING"}`);
              console.log(`      database: ${db.database || "MISSING"}`);
              console.log(`      username: ${db.username || "MISSING"}`);
              console.log(
                `      password: ${db.password ? "[SET]" : "MISSING"}`
              );
              console.log(`      table: ${db.table || "MISSING"}`);
            }

            // Check file paths exist
            const fs = await import("fs");
            if (adkConfig.sourceDirectory) {
              const sourceExists = fs.existsSync(adkConfig.sourceDirectory);
              console.log(`    sourceDirectory exists: ${sourceExists}`);
            }
            if (adkConfig.rarToolPath) {
              const rarExists = fs.existsSync(adkConfig.rarToolPath);
              console.log(`    rarToolPath exists: ${rarExists}`);
            }
          }

          // Check destination config
          console.log(`  Destination config:`);
          console.log(`    type: ${failingJob.destination?.type || "MISSING"}`);
          console.log(
            `    fileTracking: ${!!failingJob.destination?.fileTracking}`
          );

          if (failingJob.destination?.fileTracking) {
            const ft = failingJob.destination.fileTracking;
            console.log(`      enabled: ${ft.enabled}`);
            console.log(`      database: ${!!ft.database}`);

            if (ft.database) {
              const db = ft.database;
              console.log(`        host: ${db.host || "MISSING"}`);
              console.log(`        port: ${db.port || "MISSING"}`);
              console.log(`        database: ${db.database || "MISSING"}`);
              console.log(`        username: ${db.username || "MISSING"}`);
              console.log(
                `        password: ${db.password ? "[SET]" : "MISSING"}`
              );
              console.log(`        table: ${db.table || "MISSING"}`);
            }
          }
        }
      } catch (error) {
        console.log(
          `❌ Error getting/testing handler: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
        console.log(
          `Stack trace: ${
            error instanceof Error ? error.stack : "No stack trace available"
          }`
        );
      }
    }

    // Show full job configuration for debugging
    console.log("\n📋 Full Job Configuration:");
    console.log(JSON.stringify(failingJob, null, 2));

    process.exit(0);
  } catch (error) {
    console.error("❌ Error during debugging:", error);
    process.exit(1);
  }
}

debugAdkJobs();
