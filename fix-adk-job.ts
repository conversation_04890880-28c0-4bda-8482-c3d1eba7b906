// Fix ADK processing job configuration
import { loadJobDefinition, saveJobDefinition } from './src/lib/jobPersistence';
import { initializeDatabase } from './src/lib/database';

async function fixAdkJob() {
  try {
    console.log('🔧 Fixing ADK Processing Job Configuration...\n');
    
    console.log('Initializing database...');
    await initializeDatabase();
    
    const jobId = 'job-1753091554181';
    console.log(`Loading job: ${jobId}`);
    const job = await loadJobDefinition(jobId);
    
    if (!job) {
      console.log(`❌ Job ${jobId} not found`);
      return;
    }
    
    console.log(`📄 Current job: ${job.name}`);
    console.log(`Current destination config:`, JSON.stringify(job.destination, null, 2));
    
    // Fix the destination configuration to include fileTracking
    const updatedJob = {
      ...job,
      destination: {
        ...job.destination,
        fileTracking: {
          enabled: true,
          database: {
            host: "localhost",
            port: 3306,
            username: "root", 
            password: "",
            database: "monev2025",
            table: "file_metadata"
          }
        }
      }
    };
    
    console.log(`\n🔧 Updated destination config:`, JSON.stringify(updatedJob.destination, null, 2));
    
    // Save the updated job
    console.log(`\n💾 Saving updated job configuration...`);
    await saveJobDefinition(updatedJob);
    
    console.log(`✅ Job configuration updated successfully!`);
    
    // Verify the fix by loading and validating again
    console.log(`\n🧪 Verifying the fix...`);
    const { jobHandlerFactory } = await import('./src/lib/jobs');
    const handler = jobHandlerFactory.getHandler('adk_processing');
    const isValid = handler.validateConfig(updatedJob);
    
    console.log(`Configuration now valid: ${isValid}`);
    
    if (!isValid) {
      console.log(`❌ Configuration still invalid. Additional debugging needed.`);
    } else {
      console.log(`✅ Configuration is now valid! The job should work.`);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error fixing job:', error);
    process.exit(1);
  }
}

fixAdkJob();
