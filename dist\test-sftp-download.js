"use strict";
/**
 * Test script to download a specific PDF file from SFTP server
 * This script tests the SFTP connection and download// Function to test SFTP connection without downloading
async function testConnection(): Promise<boolean> {
  const sftp = new SftpClient(); single file
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.sftpConfig = void 0;
exports.downloadSingleFile = downloadSingleFile;
exports.testConnection = testConnection;
const fs = require("fs");
const path = require("path");
const ssh2_sftp_client_1 = require("ssh2-sftp-client");
const crypto_1 = require("crypto");
// SFTP Configuration
const sftpConfig = {
    host: "aksesdata-anggaran.kemenkeu.go.id",
    port: 54321,
    username: "PA_DJPBN",
    password: "Sinergi100Persen",
};
exports.sftpConfig = sftpConfig;
// File to download
const remoteFilePath = "/adk_rkakl2025/01/001/2025/2025-01-004.01-450774-06-001-2-00.pdf";
const localDirectory = "C:/data/tarif-pdf-downloads/test";
const fileName = path.basename(remoteFilePath);
const localFilePath = path.join(localDirectory, fileName);
async function downloadSingleFile() {
    const sftp = new ssh2_sftp_client_1.default();
    const startTime = Date.now();
    try {
        console.log("🔗 Connecting to SFTP server...");
        console.log(`   Host: ${sftpConfig.host}:${sftpConfig.port}`);
        console.log(`   User: ${sftpConfig.username}`);
        await sftp.connect(sftpConfig);
        console.log("✅ Connected to SFTP server successfully");
        // Check if remote file exists
        console.log("📁 Checking if remote file exists...");
        console.log(`   Remote path: ${remoteFilePath}`);
        const fileExists = await sftp.exists(remoteFilePath);
        if (!fileExists) {
            throw new Error(`Remote file does not exist: ${remoteFilePath}`);
        }
        const fileStats = await sftp.stat(remoteFilePath);
        console.log(`✅ Remote file found - Size: ${fileStats.size} bytes`);
        // Create local directory if it doesn't exist
        console.log("📂 Creating local directory...");
        console.log(`   Local directory: ${localDirectory}`);
        if (!fs.existsSync(localDirectory)) {
            fs.mkdirSync(localDirectory, { recursive: true });
            console.log("✅ Local directory created");
        }
        else {
            console.log("✅ Local directory already exists");
        }
        // Download the file
        console.log("⬇️  Starting file download...");
        console.log(`   Downloading to: ${localFilePath}`);
        await sftp.fastGet(remoteFilePath, localFilePath);
        const endTime = Date.now();
        const downloadTime = endTime - startTime;
        // Verify local file
        const localStats = fs.statSync(localFilePath);
        console.log(`✅ File downloaded successfully`);
        console.log(`   Local file size: ${localStats.size} bytes`);
        console.log(`   Download time: ${downloadTime}ms`);
        // Calculate file hash for integrity verification
        console.log("🔐 Calculating file hash...");
        const fileBuffer = fs.readFileSync(localFilePath);
        const hash = crypto_1.default.createHash("md5").update(fileBuffer).digest("hex");
        console.log(`✅ File hash (MD5): ${hash}`);
        // Verify file sizes match
        if (localStats.size !== fileStats.size) {
            throw new Error(`File size mismatch: remote=${fileStats.size}, local=${localStats.size}`);
        }
        console.log("🎉 File download completed successfully!");
        return {
            success: true,
            filePath: localFilePath,
            fileSize: localStats.size,
            downloadTime,
            hash,
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error("❌ Download failed:", errorMessage);
        return {
            success: false,
            error: errorMessage,
        };
    }
    finally {
        // Always close the SFTP connection
        try {
            await sftp.end();
            console.log("🔌 SFTP connection closed");
        }
        catch (closeError) {
            console.warn("⚠️  Warning: Failed to close SFTP connection:", closeError);
        }
    }
}
// Function to test SFTP connection without downloading
async function testConnection() {
    const sftp = new ssh2_sftp_client_1.default();
    try {
        console.log("🧪 Testing SFTP connection...");
        await sftp.connect(sftpConfig);
        console.log("✅ Connection test successful");
        // List directory to verify permissions
        const parentDir = path.dirname(remoteFilePath);
        console.log(`📁 Listing directory: ${parentDir}`);
        const list = await sftp.list(parentDir);
        console.log(`✅ Directory listing successful - Found ${list.length} items`);
        // Look for our specific file
        const targetFile = list.find((item) => item.name === fileName);
        if (targetFile) {
            console.log(`✅ Target file found: ${targetFile.name} (${targetFile.size} bytes)`);
        }
        else {
            console.log(`⚠️  Target file not found in directory listing`);
        }
        return true;
    }
    catch (error) {
        console.error("❌ Connection test failed:", error);
        return false;
    }
    finally {
        try {
            await sftp.end();
        }
        catch (closeError) {
            console.warn("⚠️  Warning: Failed to close SFTP connection:", closeError);
        }
    }
}
// Main execution function
async function main() {
    console.log("=".repeat(60));
    console.log("🚀 SFTP Single File Download Test");
    console.log("=".repeat(60));
    // First test the connection
    const connectionOk = await testConnection();
    if (!connectionOk) {
        console.log("❌ Connection test failed. Aborting download.");
        process.exit(1);
    }
    console.log("\n" + "=".repeat(60));
    console.log("📥 Starting File Download");
    console.log("=".repeat(60));
    // Download the file
    const result = await downloadSingleFile();
    console.log("\n" + "=".repeat(60));
    console.log("📊 Download Summary");
    console.log("=".repeat(60));
    if (result.success) {
        console.log("✅ Status: SUCCESS");
        console.log(`📁 File: ${result.filePath}`);
        console.log(`📏 Size: ${result.fileSize} bytes`);
        console.log(`⏱️  Time: ${result.downloadTime}ms`);
        console.log(`🔐 Hash: ${result.hash}`);
    }
    else {
        console.log("❌ Status: FAILED");
        console.log(`💥 Error: ${result.error}`);
        process.exit(1);
    }
}
// Run the test if this file is executed directly
if (require.main === module) {
    main().catch((error) => {
        console.error("💥 Unexpected error:", error);
        process.exit(1);
    });
}
