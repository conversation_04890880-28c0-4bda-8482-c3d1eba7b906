import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardBody,
  Tabs,
  Tab,
  Accordion,
  AccordionItem,
} from "@heroui/react";
import {
  HelpCircle,
  Database,
  FileText,
  Settings,
  CheckCircle,
  AlertTriangle,
  Info,
} from "lucide-react";

interface AdkHelpSystemProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AdkHelpSystem: React.FC<AdkHelpSystemProps> = ({
  isOpen,
  onClose,
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="5xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center">
              <HelpCircle className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold">ADK Processing Help</h3>
              <p className="text-sm text-gray-600">
                Complete guide to configuring ADK multi-table processing
              </p>
            </div>
          </div>
        </ModalHeader>
        <ModalBody>
          <Tabs
            aria-label="ADK Help Topics"
            color="primary"
            variant="underlined"
            classNames={{
              tabList:
                "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-primary",
              tab: "max-w-fit px-0 h-12",
              tabContent: "group-data-[selected=true]:text-primary",
            }}
          >
            <Tab
              key="overview"
              title={
                <div className="flex items-center space-x-2">
                  <Info className="w-4 h-4" />
                  <span>Overview</span>
                </div>
              }
            >
              <div className="space-y-6 py-4">
                <Card className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200">
                  <CardBody className="p-6">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">
                      🎯 What is ADK Processing?
                    </h4>
                    <p className="text-gray-700 mb-4">
                      ADK (Aplikasi Data Keuangan) Processing is an automated
                      system that extracts and processes financial data from
                      compressed archive files (.s25, .s2504, .s2507) and
                      distributes the data across multiple database tables.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-white p-4 rounded-lg border border-blue-200">
                        <FileText className="w-6 h-6 text-blue-600 mb-2" />
                        <h5 className="font-semibold text-gray-800">
                          Multi-File Processing
                        </h5>
                        <p className="text-sm text-gray-600">
                          Each archive contains ~27 XML files with different
                          data types
                        </p>
                      </div>
                      <div className="bg-white p-4 rounded-lg border border-green-200">
                        <Database className="w-6 h-6 text-green-600 mb-2" />
                        <h5 className="font-semibold text-gray-800">
                          14 Target Tables
                        </h5>
                        <p className="text-sm text-gray-600">
                          Automatic routing to d_akun, d_item, d_kpa, and 11
                          other tables
                        </p>
                      </div>
                      <div className="bg-white p-4 rounded-lg border border-purple-200">
                        <Settings className="w-6 h-6 text-purple-600 mb-2" />
                        <h5 className="font-semibold text-gray-800">
                          Smart Processing
                        </h5>
                        <p className="text-sm text-gray-600">
                          Dynamic schema adaptation and error handling
                        </p>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="border border-gray-200">
                    <CardBody className="p-4">
                      <h5 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        Key Benefits
                      </h5>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• Automated extraction and processing</li>
                        <li>• Multi-table data distribution</li>
                        <li>• Error-resilient processing</li>
                        <li>• Automatic cleanup and status tracking</li>
                        <li>• High-performance batch processing</li>
                      </ul>
                    </CardBody>
                  </Card>

                  <Card className="border border-gray-200">
                    <CardBody className="p-4">
                      <h5 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                        <AlertTriangle className="w-5 h-5 text-yellow-600" />
                        Prerequisites
                      </h5>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• RAR extraction tool (Rar.exe)</li>
                        <li>• Database with 14 ADK target tables</li>
                        <li>• File tracking database (file_metadata)</li>
                        <li>• Windows file system access</li>
                        <li>• Sufficient disk space for extraction</li>
                      </ul>
                    </CardBody>
                  </Card>
                </div>
              </div>
            </Tab>

            <Tab
              key="configuration"
              title={
                <div className="flex items-center space-x-2">
                  <Settings className="w-4 h-4" />
                  <span>Configuration</span>
                </div>
              }
            >
              <div className="space-y-4 py-4">
                <Accordion variant="splitted">
                  <AccordionItem
                    key="paths"
                    aria-label="File Paths Configuration"
                    title="📁 File Paths Configuration"
                    subtitle="Required paths for ADK processing"
                  >
                    <div className="space-y-4">
                      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <h6 className="font-semibold text-blue-800 mb-2">
                          Source Directory
                        </h6>
                        <p className="text-sm text-blue-700 mb-2">
                          The base directory containing your ADK archive files
                          (.s25, .s2504, .s2507).
                        </p>
                        <div className="bg-white p-2 rounded border border-blue-200 font-mono text-sm">
                          Example: C:\KUMPULAN_ADK\ADK_2025_DIPA
                        </div>
                      </div>

                      <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                        <h6 className="font-semibold text-purple-800 mb-2">
                          Extraction Path
                        </h6>
                        <p className="text-sm text-purple-700 mb-2">
                          Temporary directory where XML files will be extracted.
                          This directory is cleaned automatically.
                        </p>
                        <div className="bg-white p-2 rounded border border-purple-200 font-mono text-sm">
                          Example: C:\KUMPULAN_ADK\XML
                        </div>
                      </div>

                      <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                        <h6 className="font-semibold text-green-800 mb-2">
                          RAR Tool Path
                        </h6>
                        <p className="text-sm text-green-700 mb-2">
                          Path to the RAR extraction executable. Must point to
                          Rar.exe file.
                        </p>
                        <div className="bg-white p-2 rounded border border-green-200 font-mono text-sm">
                          Example: C:\KUMPULAN_ADK\TOOLS\Rar.exe
                        </div>
                      </div>
                    </div>
                  </AccordionItem>

                  <AccordionItem
                    key="filtering"
                    aria-label="File Filtering Options"
                    title="🔍 File Filtering Options"
                    subtitle="Control which files are processed"
                  >
                    <div className="space-y-4">
                      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                        <h6 className="font-semibold text-yellow-800 mb-2">
                          File Name Prefixes
                        </h6>
                        <p className="text-sm text-yellow-700 mb-2">
                          Only process files that start with these prefixes. Use
                          comma-separated values.
                        </p>
                        <div className="bg-white p-2 rounded border border-yellow-200 font-mono text-sm">
                          Example: d, D (processes files starting with
                          &apos;d&apos; or &apos;D&apos;)
                        </div>
                      </div>

                      <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                        <h6 className="font-semibold text-red-800 mb-2">
                          Exclude Extensions
                        </h6>
                        <p className="text-sm text-red-700 mb-2">
                          Skip files with these extensions. Use comma-separated
                          values without dots.
                        </p>
                        <div className="bg-white p-2 rounded border border-red-200 font-mono text-sm">
                          Example: pdf, txt (skips .pdf and .txt files)
                        </div>
                      </div>
                    </div>
                  </AccordionItem>

                  <AccordionItem
                    key="processing"
                    aria-label="Processing Options"
                    title="⚙️ Processing Options"
                    subtitle="Advanced processing behavior"
                  >
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <h6 className="font-semibold text-green-800 mb-2">
                            ✅ Continue on Error
                          </h6>
                          <p className="text-sm text-green-700">
                            <strong>Recommended:</strong> Process remaining
                            files even if some fail. Ensures maximum data
                            extraction.
                          </p>
                        </div>

                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <h6 className="font-semibold text-blue-800 mb-2">
                            🗑️ Clean Extraction Directory
                          </h6>
                          <p className="text-sm text-blue-700">
                            <strong>Recommended:</strong> Remove old XML files
                            before processing to prevent conflicts.
                          </p>
                        </div>
                      </div>

                      <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                        <h6 className="font-semibold text-purple-800 mb-2">
                          📦 Batch Size
                        </h6>
                        <p className="text-sm text-purple-700 mb-2">
                          Number of files to process in each batch. Leave empty
                          for no batching.
                        </p>
                        <div className="bg-white p-2 rounded border border-purple-200 text-sm">
                          <strong>Recommendation:</strong> Use 10-50 for
                          balanced performance and memory usage.
                        </div>
                      </div>
                    </div>
                  </AccordionItem>
                </Accordion>
              </div>
            </Tab>

            <Tab
              key="tables"
              title={
                <div className="flex items-center space-x-2">
                  <Database className="w-4 h-4" />
                  <span>Target Tables</span>
                </div>
              }
            >
              <div className="space-y-4 py-4">
                <Card className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200">
                  <CardBody className="p-4">
                    <h4 className="text-lg font-semibold text-gray-800 mb-2">
                      📊 ADK Target Tables (14)
                    </h4>
                    <p className="text-sm text-gray-600 mb-4">
                      Data is automatically routed to these tables based on XML
                      filename patterns.
                    </p>
                  </CardBody>
                </Card>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    {
                      name: "d_akun",
                      desc: "Account master data",
                      records: "~1,000",
                      pattern: "d_akun.xml",
                    },
                    {
                      name: "d_cttakun",
                      desc: "Account notes and descriptions",
                      records: "~800",
                      pattern: "d_cttakun.xml",
                    },
                    {
                      name: "d_item",
                      desc: "Item master data (largest)",
                      records: "~20,000",
                      pattern: "d_item.xml",
                    },
                    {
                      name: "d_kmpnen",
                      desc: "Component data",
                      records: "~1,500",
                      pattern: "d_kmpnen.xml",
                    },
                    {
                      name: "d_kpa",
                      desc: "Key Performance Area data",
                      records: "~200",
                      pattern: "d_kpa.xml",
                    },
                    {
                      name: "d_kpjm",
                      desc: "KPJM data",
                      records: "~300",
                      pattern: "d_kpjm.xml",
                    },
                    {
                      name: "d_output",
                      desc: "Output data",
                      records: "~500",
                      pattern: "d_output.xml",
                    },
                    {
                      name: "d_pdpt",
                      desc: "Revenue (Pendapatan) data",
                      records: "~200",
                      pattern: "d_pdpt.xml",
                    },
                    {
                      name: "d_pgj",
                      desc: "PGJ data (schema issues)",
                      records: "~100",
                      pattern: "d_pgj.xml",
                    },
                    {
                      name: "d_polri",
                      desc: "Police data",
                      records: "~50",
                      pattern: "d_polri.xml",
                    },
                    {
                      name: "d_skmpnen",
                      desc: "Sub-component data",
                      records: "~2,000",
                      pattern: "d_skmpnen.xml",
                    },
                    {
                      name: "d_soutput",
                      desc: "Sub-output data",
                      records: "~800",
                      pattern: "d_soutput.xml",
                    },
                    {
                      name: "d_trktrm",
                      desc: "Transfer data",
                      records: "~400",
                      pattern: "d_trktrm.xml",
                    },
                    {
                      name: "d_valas",
                      desc: "Foreign exchange data",
                      records: "~100",
                      pattern: "d_valas.xml",
                    },
                  ].map((table) => (
                    <Card
                      key={table.name}
                      className="border border-gray-200 hover:border-blue-300 transition-colors"
                    >
                      <CardBody className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <code className="text-sm font-mono text-blue-700 bg-blue-100 px-2 py-1 rounded">
                            {table.name}
                          </code>
                          <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">
                            {table.records}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">
                          {table.desc}
                        </p>
                        <div className="text-xs text-gray-500">
                          <strong>XML Pattern:</strong> {table.pattern}
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              </div>
            </Tab>

            <Tab
              key="troubleshooting"
              title={
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="w-4 h-4" />
                  <span>Troubleshooting</span>
                </div>
              }
            >
              <div className="space-y-4 py-4">
                <Accordion variant="splitted">
                  <AccordionItem
                    key="common-issues"
                    aria-label="Common Issues"
                    title="🚨 Common Issues & Solutions"
                    subtitle="Frequently encountered problems"
                  >
                    <div className="space-y-4">
                      <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                        <h6 className="font-semibold text-red-800 mb-2">
                          ❌ &quot;RAR tool not found&quot;
                        </h6>
                        <p className="text-sm text-red-700 mb-2">
                          The system cannot locate the RAR extraction tool.
                        </p>
                        <div className="bg-white p-2 rounded border border-red-200 text-sm">
                          <strong>Solution:</strong> Verify the RAR tool path
                          points to a valid Rar.exe file.
                        </div>
                      </div>

                      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                        <h6 className="font-semibold text-yellow-800 mb-2">
                          ⚠️ &quot;Database connection failed&quot;
                        </h6>
                        <p className="text-sm text-yellow-700 mb-2">
                          Cannot connect to the target database.
                        </p>
                        <div className="bg-white p-2 rounded border border-yellow-200 text-sm">
                          <strong>Solution:</strong> Check database credentials
                          and network connectivity.
                        </div>
                      </div>

                      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <h6 className="font-semibold text-blue-800 mb-2">
                          ℹ️ &quot;Some files skipped&quot;
                        </h6>
                        <p className="text-sm text-blue-700 mb-2">
                          Files don&apos;t match the configured filters.
                        </p>
                        <div className="bg-white p-2 rounded border border-blue-200 text-sm">
                          <strong>Solution:</strong> Review file prefix filters
                          and exclude extensions.
                        </div>
                      </div>
                    </div>
                  </AccordionItem>

                  <AccordionItem
                    key="performance"
                    aria-label="Performance Tips"
                    title="🚀 Performance Optimization"
                    subtitle="Tips for better processing speed"
                  >
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <h6 className="font-semibold text-green-800 mb-2">
                            ✅ Best Practices
                          </h6>
                          <ul className="text-sm text-green-700 space-y-1">
                            <li>• Use SSD storage for extraction path</li>
                            <li>• Set batch size to 10-50 files</li>
                            <li>• Enable &quot;Continue on Error&quot;</li>
                            <li>• Clean extraction directory regularly</li>
                          </ul>
                        </div>

                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <h6 className="font-semibold text-blue-800 mb-2">
                            📊 Expected Performance
                          </h6>
                          <ul className="text-sm text-blue-700 space-y-1">
                            <li>• ~0.5 seconds per file</li>
                            <li>• ~67K records per batch</li>
                            <li>• ~173 files typical batch</li>
                            <li>• 99%+ success rate</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </AccordionItem>
                </Accordion>
              </div>
            </Tab>
          </Tabs>
        </ModalBody>
        <ModalFooter>
          <Button color="primary" onPress={onClose}>
            Got it, thanks!
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

// Quick help tooltip component
export const AdkQuickHelp: React.FC<{ topic?: string }> = () => {
  const [showHelp, setShowHelp] = useState(false);

  return (
    <>
      <Button
        isIconOnly
        size="sm"
        variant="light"
        onPress={() => setShowHelp(true)}
        className="text-gray-400 hover:text-blue-600"
      >
        <HelpCircle className="w-4 h-4" />
      </Button>
      <AdkHelpSystem isOpen={showHelp} onClose={() => setShowHelp(false)} />
    </>
  );
};
