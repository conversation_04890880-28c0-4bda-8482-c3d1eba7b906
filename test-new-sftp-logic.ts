// Test the new SFTP directory browsing logic
import { initializeDatabase } from "./src/lib/database";
import { loadJobDefinition } from "./src/lib/jobPersistence";
import Client from "ssh2-sftp-client";

// Import the browseFolderRecursively function (we'll need to make it exportable)
// For now, let's recreate the logic here for testing

async function browseFolderRecursively(
  sftp: Client,
  folderPath: string,
  pattern: string
): Promise<Array<{
  name: string;
  size: number;
  lastModified: Date;
  remotePath: string;
}>> {
  const allFiles: Array<{
    name: string;
    size: number;
    lastModified: Date;
    remotePath: string;
  }> = [];

  try {
    // List contents of the current folder
    const folderContents = await sftp.list(folderPath);

    // Process files in current folder
    const files = folderContents.filter((item) => {
      if (item.type !== "-") return false; // Only regular files
      
      if (pattern === "*") return true;
      
      // Simple pattern matching
      const regex = new RegExp(pattern.replace(/\*/g, ".*"));
      return regex.test(item.name);
    });

    // Add files from current folder
    files.forEach((file) => {
      allFiles.push({
        name: file.name,
        size: file.size,
        lastModified: new Date(file.modifyTime),
        remotePath: `${folderPath}/${file.name}`,
      });
    });

    // Process subdirectories recursively
    const subdirectories = folderContents.filter((item) => item.type === "d");
    
    for (const subdir of subdirectories) {
      try {
        const subdirPath = `${folderPath}/${subdir.name}`;
        const subdirFiles = await browseFolderRecursively(
          sftp,
          subdirPath,
          pattern
        );
        
        allFiles.push(...subdirFiles);
      } catch (subdirError) {
        console.log(`Warning: Could not access subdirectory ${subdir.name}: ${subdirError instanceof Error ? subdirError.message : 'Unknown error'}`);
      }
    }
  } catch (error) {
    console.log(`Warning: Could not access folder ${folderPath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return allFiles;
}

async function testNewSftpLogic() {
  try {
    console.log("🔧 Initializing database...");
    await initializeDatabase();

    console.log("📋 Loading job 4 definition...");
    const job4 = await loadJobDefinition("4");

    if (!job4) {
      console.error("❌ Job 4 not found in database");
      process.exit(1);
    }

    console.log("✅ Job 4 loaded successfully:");
    console.log(`- Name: ${job4.name}`);
    console.log(`- Remote Path: ${job4.dataSource.sftp?.remotePath}`);
    console.log(`- Local Path: ${job4.destination.localPath}`);

    // Test the SFTP connection and directory browsing
    const sftp = new Client();
    
    const sftpConfig = {
      host: job4.dataSource.sftp?.host || "aksesdata-anggaran.kemenkeu.go.id",
      port: job4.dataSource.sftp?.port || 54321,
      username: job4.dataSource.sftp?.username || "PA_DJPBN",
      password: job4.dataSource.sftp?.password || "Sinergi100Persen",
    };

    console.log("\n🔗 Connecting to SFTP server...");
    await sftp.connect(sftpConfig);
    console.log("✅ Connected successfully!");

    const remotePath = job4.dataSource.sftp?.remotePath || "adk_rkakl2025";
    console.log(`\n📁 Browsing directory: ${remotePath}`);

    // List directories in the remote directory (looking for 2-digit folders)
    const rootList = await sftp.list(remotePath);

    // Filter for 2-digit directories only (exclude 3-digit directories)
    const twoDigitFolders = rootList.filter((item) => {
      return item.type === "d" && /^[0-9]{2}$/.test(item.name);
    });

    console.log(`🎯 Found ${twoDigitFolders.length} 2-digit organizational folders`);

    // Test processing the first 2 folders to avoid overwhelming output
    const foldersToTest = twoDigitFolders.slice(0, 2);
    let totalFiles = 0;

    for (const folder of foldersToTest) {
      console.log(`\n📂 Processing 2-digit folder: ${folder.name}`);
      
      // Browse 3-digit subfolders within this 2-digit folder
      const twoDigitPath = `${remotePath}/${folder.name}`;
      const subfolderList = await sftp.list(twoDigitPath);
      
      // Filter for 3-digit directories
      const threeDigitFolders = subfolderList.filter((item) => {
        return item.type === "d" && /^[0-9]{3}$/.test(item.name);
      });

      console.log(`  Found ${threeDigitFolders.length} 3-digit subfolders`);

      // Test the first 3-digit subfolder
      if (threeDigitFolders.length > 0) {
        const firstSubfolder = threeDigitFolders[0];
        const subfolderPath = `${twoDigitPath}/${firstSubfolder.name}`;
        
        // Look for year folders within the 3-digit folder
        const yearFolderList = await sftp.list(subfolderPath);
        const yearFolders = yearFolderList.filter((item) => {
          return item.type === "d" && /^20[0-9]{2}$/.test(item.name);
        });

        console.log(`  Found ${yearFolders.length} year folders in ${folder.name}/${firstSubfolder.name}`);

        if (yearFolders.length > 0) {
          const firstYearFolder = yearFolders[0];
          const yearFolderPath = `${subfolderPath}/${firstYearFolder.name}`;
          
          console.log(`  📄 Checking files in ${folder.name}/${firstSubfolder.name}/${firstYearFolder.name}...`);
          
          const yearFolderFiles = await browseFolderRecursively(
            sftp,
            yearFolderPath,
            "*"
          );

          console.log(`  ✅ Found ${yearFolderFiles.length} files`);
          totalFiles += yearFolderFiles.length;

          // Show a few sample files
          if (yearFolderFiles.length > 0) {
            console.log(`  Sample files:`);
            yearFolderFiles.slice(0, 3).forEach(file => {
              console.log(`    - ${file.name} (${file.size} bytes)`);
            });
          }
        }
      }
    }

    console.log(`\n🎉 Test completed! Found ${totalFiles} files in ${foldersToTest.length} test folders`);
    console.log(`📊 Estimated total files across all ${twoDigitFolders.length} folders: ${Math.round(totalFiles * twoDigitFolders.length / foldersToTest.length)}`);

    await sftp.end();
    console.log("🔌 SFTP connection closed");

  } catch (error) {
    console.error("❌ Test failed:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

testNewSftpLogic();
