import { NextRequest, NextResponse } from "next/server";
import { saveJobDefinition, loadJobDefinition } from "@/lib/jobPersistence";
import { JobDefinition } from "@/lib/jobManager";
import { logger } from "@/lib/jobManager";

// GET /api/admin/jobs/[id] - Get specific job definition
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: jobId } = await params;
    const job = await loadJobDefinition(jobId);

    if (!job) {
      return NextResponse.json(
        { error: `Job definition not found: ${jobId}` },
        { status: 404 }
      );
    }

    return NextResponse.json({ job });
  } catch (error) {
    logger.error("Error fetching job definition:", error);
    return NextResponse.json(
      { error: "Failed to fetch job definition" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/jobs/[id] - Update specific job definition
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: jobId } = await params;
    const updatedJob: JobDefinition = await request.json();

    // Validate that the job ID in the URL matches the job ID in the body
    if (updatedJob.id !== jobId) {
      return NextResponse.json(
        { error: "Job ID in URL does not match job ID in request body" },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!updatedJob.name || !updatedJob.schedule) {
      return NextResponse.json(
        { error: "Missing required fields: name, schedule" },
        { status: 400 }
      );
    }

    // Validate job ID exists
    const existingJob = await loadJobDefinition(jobId);
    if (!existingJob) {
      return NextResponse.json(
        { error: `Job definition not found: ${jobId}` },
        { status: 404 }
      );
    }

    // Save the updated job definition
    await saveJobDefinition(updatedJob);

    logger.info(
      `Job definition updated via admin API: ${updatedJob.id} - ${updatedJob.name}`
    );

    // Update the cron scheduler if the schedule changed
    try {
      const { cronScheduler } = await import("@/lib/cronScheduler");
      if (existingJob.schedule !== updatedJob.schedule) {
        logger.info(
          `Rescheduling job ${updatedJob.id} from "${existingJob.schedule}" to "${updatedJob.schedule}"`
        );
        cronScheduler.rescheduleJob(
          updatedJob.id,
          updatedJob.schedule,
          updatedJob.name
        );
      }
    } catch (error) {
      logger.error("Could not update cron scheduler", { error });
    }

    // Broadcast the update to all connected clients
    try {
      const { broadcastJobUpdate } = await import("@/lib/sseManager");
      await broadcastJobUpdate();
    } catch (error) {
      logger.debug("Could not broadcast job update", { error });
    }

    return NextResponse.json({
      message: "Job definition updated successfully",
      jobId: updatedJob.id,
    });
  } catch (error) {
    logger.error("Error updating job definition:", error);
    return NextResponse.json(
      { error: "Failed to update job definition" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/jobs/[id] - Delete specific job definition
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: jobId } = await params;

    // Validate job ID exists
    const existingJob = await loadJobDefinition(jobId);
    if (!existingJob) {
      return NextResponse.json(
        { error: `Job definition not found: ${jobId}` },
        { status: 404 }
      );
    }

    // Import deleteJobDefinition function
    const { deleteJobDefinition } = await import("@/lib/jobPersistence");

    // Delete the job definition
    await deleteJobDefinition(jobId);

    logger.info(
      `Job definition deleted via admin API: ${jobId} - ${existingJob.name}`
    );

    // Broadcast the update to all connected clients
    try {
      const { broadcastJobUpdate } = await import("@/lib/sseManager");
      await broadcastJobUpdate();
    } catch (error) {
      logger.debug("Could not broadcast job update", { error });
    }

    return NextResponse.json({
      message: "Job definition deleted successfully",
      jobId,
    });
  } catch (error) {
    logger.error("Error deleting job definition:", error);
    return NextResponse.json(
      { error: "Failed to delete job definition" },
      { status: 500 }
    );
  }
}
