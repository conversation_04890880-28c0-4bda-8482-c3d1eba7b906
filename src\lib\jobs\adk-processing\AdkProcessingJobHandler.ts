import { BaseJobHandler } from "../base/BaseJobHandler";
import type { <PERSON><PERSON><PERSON><PERSON>, JobExecutionContext } from "../types";
import type { JobDefinition } from "../../jobManager";
import { logger } from "../../jobManager";
import {
  queryFilesByStatus,
  updateFileStatus,
  FileTrackingDbConfig,
} from "../../fileTrackingDatabase";
import fs from "fs";
import path from "path";
import { exec } from "child_process";
import xml2js from "xml2js";
// Import global process tracking functions
import {
  registerRunningProcess,
  unregisterRunningProcess,
} from "../../jobRunner";
import type { Connection } from "mysql2/promise";

/**
 * Interface for XML file to table mapping configuration
 */
interface XmlTableMapping {
  filePattern: RegExp;
  tableName: string;
  xmlDataPath: string; // Path to data in XML (e.g., 'VFPData.c_item')
  description: string;
}

/**
 * Job handler for ADK processing operations
 */
export class AdkProcessingJobHandler extends BaseJobHandler {
  public readonly jobType = "adk_processing";

  // Multi-table mapping configuration for XML files
  private readonly xmlTableMappings: XmlTableMapping[] = [
    {
      filePattern: /^d_akun.*\.xml$/i,
      tableName: "d_akun",
      xmlDataPath: "VFPData.c_akun",
      description: "Account data",
    },
    {
      filePattern: /^d_cttakun.*\.xml$/i,
      tableName: "d_cttakun",
      xmlDataPath: "VFPData.c_cttakun",
      description: "Account notes data",
    },
    {
      filePattern: /^d_item.*\.xml$/i,
      tableName: "d_item",
      xmlDataPath: "VFPData.c_item",
      description: "Item data",
    },
    {
      filePattern: /^d_kmpnen.*\.xml$/i,
      tableName: "d_kmpnen",
      xmlDataPath: "VFPData.c_kmpnen",
      description: "Component data",
    },
    {
      filePattern: /^d_kpa.*\.xml$/i,
      tableName: "d_kpa",
      xmlDataPath: "VFPData.c_kpa",
      description: "KPA data",
    },
    {
      filePattern: /^d_kpjm.*\.xml$/i,
      tableName: "d_kpjm",
      xmlDataPath: "VFPData.c_kpjm",
      description: "KPJM data",
    },
    {
      filePattern: /^d_output.*\.xml$/i,
      tableName: "d_output",
      xmlDataPath: "VFPData.c_output",
      description: "Output data",
    },
    {
      filePattern: /^d_pdpt.*\.xml$/i,
      tableName: "d_pdpt",
      xmlDataPath: "VFPData.c_pdpt",
      description: "Revenue data",
    },
    {
      filePattern: /^d_pgj.*\.xml$/i,
      tableName: "d_pgj",
      xmlDataPath: "VFPData.c_pgj",
      description: "PGJ data",
    },
    {
      filePattern: /^d_polri.*\.xml$/i,
      tableName: "d_polri",
      xmlDataPath: "VFPData.c_polri",
      description: "Police data",
    },
    {
      filePattern: /^d_skmpnen.*\.xml$/i,
      tableName: "d_skmpnen",
      xmlDataPath: "VFPData.c_skmpnen",
      description: "Sub-component data",
    },
    {
      filePattern: /^d_soutput.*\.xml$/i,
      tableName: "d_soutput",
      xmlDataPath: "VFPData.c_soutput",
      description: "Sub-output data",
    },
    {
      filePattern: /^d_trktrm.*\.xml$/i,
      tableName: "d_trktrm",
      xmlDataPath: "VFPData.c_trktrm",
      description: "Transfer data",
    },
    {
      filePattern: /^d_valas.*\.xml$/i,
      tableName: "d_valas",
      xmlDataPath: "VFPData.c_valas",
      description: "Foreign exchange data",
    },
    // Note: Additional XML files found in archives (d_giat, d_ln, d_ngiat, d_nprogram,
    // d_nsasaran, d_satker, d_tni, m_*) are not mapped as corresponding tables don't exist.
    // These files will be logged but skipped during processing.
  ];

  /**
   * Execute ADK processing job
   */
  public async execute(context: JobExecutionContext): Promise<JobResult> {
    const { jobDefinition } = context;
    const config = jobDefinition.dataSource.adk_processing;

    if (!config) {
      throw new Error("ADK processing configuration is missing");
    }

    // Validate configuration
    this.validateAdkProcessingConfig(config);

    const startTime = await this.logOperationStart(
      context,
      "ADK processing",
      `Processing files from ${config.sourceDirectory}`
    );

    try {
      await context.addLog(
        `Configuration validated - Source: ${config.sourceDirectory}, Extraction: ${config.extractionPath}`
      );

      // Pre-processing cleanup: Clear extraction folder completely
      await this.completeExtractionCleanup(context, config.extractionPath);

      let recordsProcessed = 0;

      // Get file tracking configuration from job destination
      const fileTrackingConfig =
        jobDefinition.destination?.fileTracking?.database;
      if (!fileTrackingConfig) {
        throw new Error(
          "File tracking configuration is required for ADK processing. Please configure destination.fileTracking.database in the job definition."
        );
      }

      await context.addLog(
        "Querying files with status 'NEW' from file_metadata table..."
      );

      // Query files with status 'NEW' from file_metadata table
      // Note: Only the latest version of each file gets 'NEW' status.
      // Older versions are marked as 'SUPERSEDED' by the SFTP job handler.
      const filesToProcess = await queryFilesByStatus(
        fileTrackingConfig,
        "NEW"
      );
      await context.addLog(
        `Found ${filesToProcess.length} files to process (latest versions only)`
      );

      // Process each file individually following the new workflow
      await this.executeWithProgress(
        context,
        filesToProcess,
        async (fileRecord, _index) => {
          // Check for cancellation before processing each file
          await context.checkCancellation();

          try {
            const processedCount = await this.processAdkFile(
              context,
              config,
              fileRecord,
              fileTrackingConfig
            );
            recordsProcessed += processedCount;
          } catch (fileError) {
            const errorMessage =
              fileError instanceof Error
                ? fileError.message
                : "Unknown file processing error";
            await context.addLog(
              `Error processing file ${fileRecord.nmfile}: ${errorMessage}`
            );

            // Update file status to ERROR
            try {
              await updateFileStatus(
                fileTrackingConfig,
                fileRecord.id,
                "ERROR",
                context.jobId,
                context.executionId
              );
              await context.addLog(`File ${fileRecord.nmfile} marked as ERROR`);
            } catch (statusError) {
              const statusErrorMessage =
                statusError instanceof Error
                  ? statusError.message
                  : String(statusError);
              await context.addLog(
                `Failed to update file status: ${statusErrorMessage}`
              );
            }

            if (!config.processingOptions?.continueOnError) {
              throw fileError;
            }
          }
        },
        "ADK file processing",
        1 // Progress update for each file
      );

      await context.addLog(
        `ADK processing completed. Total records processed: ${recordsProcessed}`
      );

      await this.logOperationComplete(
        context,
        "ADK processing",
        startTime,
        `${recordsProcessed} records processed from ${filesToProcess.length} files`
      );

      // Return summary of processing results
      return this.createJobResult(recordsProcessed, {
        type: "adk_processing",
        recordsProcessed,
        filesProcessed: filesToProcess.length,
        message: "ADK processing completed successfully",
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown ADK processing error";
      await context.addLog(`ADK processing failed: ${errorMessage}`);

      logger.error(`ADK processing failed for job ${context.jobId}`, {
        jobId: context.jobId,
        error: errorMessage,
        sourceDirectory: config.sourceDirectory,
        stack: error instanceof Error ? error.stack : undefined,
      });

      throw error;
    } finally {
      // Clean up any running processes
      this.cleanupRunningProcesses(context.jobId);
    }
  }

  /**
   * Process a single ADK file with multi-table data distribution
   */
  private async processAdkFile(
    context: JobExecutionContext,
    config: NonNullable<JobDefinition["dataSource"]["adk_processing"]>,
    fileRecord: {
      id: number;
      folder: string;
      nmfile: string;
      tg: string;
      jam: string;
      size: number;
      status: string;
    },
    fileTrackingConfig: FileTrackingDbConfig
  ): Promise<number> {
    const nmfile = fileRecord.nmfile;
    const folder = fileRecord.folder;

    // Parse folder to get dept and unit (folder format: "34/152")
    const folderParts = folder.split("/");
    const folder1 = folderParts[0]; // dept (e.g., "34")
    const folder2 = folderParts[1]; // unit (e.g., "152")

    await context.addLog(`Processing file: ${nmfile} (ID: ${fileRecord.id})`);

    // Log file processing details for debugging
    await context.addLog(
      `File details - Dept: ${folder1}, Unit: ${folder2}, Archive: ${nmfile}`
    );

    // Ensure extraction directory exists first
    if (!fs.existsSync(config.extractionPath)) {
      await fs.promises.mkdir(config.extractionPath, { recursive: true });
      await context.addLog(
        `Created extraction directory: ${config.extractionPath}`
      );
    }

    // Extract XML files from compressed archive
    const archivePath = path.join(
      config.sourceDirectory,
      folder1,
      folder2,
      "2025", // Add the year component to match the directory structure
      nmfile
    );

    await context.addLog(`Looking for archive at: ${archivePath}`);

    // Check if archive file exists before extraction
    if (!fs.existsSync(archivePath)) {
      throw new Error(`Archive file not found: ${archivePath}`);
    }

    // Log archive file size for debugging
    const archiveStats = await fs.promises.stat(archivePath);
    await context.addLog(
      `Archive file size: ${Math.round(archiveStats.size / 1024)}KB`
    );

    // Check for cancellation before extraction
    await context.checkCancellation();

    // Extract the archive
    await this.extractArchive(context, config, archivePath, nmfile);

    // Check for cancellation after extraction
    await context.checkCancellation();

    // NEW: Scan extraction folder for ALL XML files
    const extractedXmlFiles = await this.scanForXmlFiles(
      context,
      config.extractionPath
    );

    if (extractedXmlFiles.length === 0) {
      await context.addLog(
        `Warning: No XML files found in extraction directory after extracting ${nmfile}`
      );
      return 0;
    }

    await context.addLog(
      `Found ${
        extractedXmlFiles.length
      } XML files to process: ${extractedXmlFiles.join(", ")}`
    );

    // NEW: Process each XML file and distribute to appropriate tables
    let totalProcessedCount = 0;
    for (const xmlFile of extractedXmlFiles) {
      try {
        await context.checkCancellation();

        const xmlFilePath = path.join(config.extractionPath, xmlFile);
        const processedCount = await this.processXmlFileWithTableMapping(
          context,
          xmlFilePath,
          xmlFile
        );
        totalProcessedCount += processedCount;

        await context.addLog(
          `Processed ${processedCount} records from ${xmlFile}`
        );
      } catch (xmlError) {
        const errorMessage =
          xmlError instanceof Error ? xmlError.message : String(xmlError);
        await context.addLog(
          `Error processing XML file ${xmlFile}: ${errorMessage}`
        );

        if (!config.processingOptions?.continueOnError) {
          throw xmlError;
        }
      }
    }

    // Post-processing cleanup: Clear ALL files from extraction folder
    await this.completeExtractionCleanup(context, config.extractionPath);

    // Update file status to PROCESSED
    await updateFileStatus(
      fileTrackingConfig,
      fileRecord.id,
      "PROCESSED",
      context.jobId,
      context.executionId
    );
    await context.addLog(`File ${nmfile} marked as PROCESSED`);

    return totalProcessedCount;
  }

  /**
   * Complete cleanup of extraction folder - removes ALL files
   */
  private async completeExtractionCleanup(
    context: JobExecutionContext,
    extractionPath: string
  ): Promise<void> {
    try {
      // Verify extraction path exists before cleanup
      if (fs.existsSync(extractionPath)) {
        const files = await fs.promises.readdir(extractionPath);

        if (files.length > 0) {
          await context.addLog(
            `Cleaning up ${files.length} files from extraction directory`
          );

          // Remove all files in the extraction directory
          for (const file of files) {
            const filePath = path.join(extractionPath, file);
            const stats = await fs.promises.stat(filePath);

            if (stats.isFile()) {
              await fs.promises.unlink(filePath);
            } else if (stats.isDirectory()) {
              // Remove directory recursively if needed
              await fs.promises.rmdir(filePath, { recursive: true });
            }
          }

          await context.addLog("Extraction directory cleaned up completely");
        } else {
          await context.addLog("Extraction directory is already empty");
        }
      } else {
        await context.addLog(
          `Warning: Extraction directory ${extractionPath} does not exist, skipping cleanup`
        );
      }
    } catch (cleanupError) {
      const errorMessage =
        cleanupError instanceof Error
          ? cleanupError.message
          : String(cleanupError);
      await context.addLog(
        `Warning: Could not clean up extraction directory: ${errorMessage}`
      );
    }
  }

  /**
   * Scan extraction folder for all XML files
   */
  private async scanForXmlFiles(
    context: JobExecutionContext,
    extractionPath: string
  ): Promise<string[]> {
    try {
      if (!fs.existsSync(extractionPath)) {
        await context.addLog(
          `Warning: Extraction directory ${extractionPath} does not exist`
        );
        return [];
      }

      const files = await fs.promises.readdir(extractionPath);
      const xmlFiles = files.filter(
        (file) =>
          file.toLowerCase().endsWith(".xml") &&
          fs.statSync(path.join(extractionPath, file)).isFile()
      );

      await context.addLog(
        `Scanned extraction directory: found ${xmlFiles.length} XML files out of ${files.length} total files`
      );

      return xmlFiles;
    } catch (scanError) {
      const errorMessage =
        scanError instanceof Error ? scanError.message : String(scanError);
      await context.addLog(
        `Error scanning extraction directory: ${errorMessage}`
      );
      return [];
    }
  }

  /**
   * Extract archive file
   */
  private async extractArchive(
    context: JobExecutionContext,
    config: NonNullable<JobDefinition["dataSource"]["adk_processing"]>,
    archivePath: string,
    nmfile: string
  ): Promise<void> {
    // Add -o+ to overwrite files without prompting and -y to assume "yes" to all queries
    const extractCommand = `"${config.rarToolPath}" e -o+ -y "${archivePath}" "${config.extractionPath}"`;

    await context.addLog(`Extracting: ${nmfile}`);
    await context.addLog(`Extraction command: ${extractCommand}`);

    // Check if RAR tool exists
    if (!fs.existsSync(config.rarToolPath)) {
      throw new Error(`RAR tool not found at: ${config.rarToolPath}`);
    }

    try {
      await context.checkCancellation();

      await context.addLog(`Starting extraction process...`);

      // Add timeout and better error handling for extraction
      const extractionTimeout = 120000; // 120 seconds timeout

      const extractionPromise = this.execPromise(extractCommand, context.jobId);

      // Create a timeout promise
      const timeoutPromise = new Promise<string>((_, reject) =>
        setTimeout(
          () =>
            reject(
              new Error(`Extraction timed out after ${extractionTimeout}ms`)
            ),
          extractionTimeout
        )
      );

      // Race the extraction against the timeout
      const result = await Promise.race([extractionPromise, timeoutPromise]);

      await context.addLog(`Extraction completed for ${nmfile}`);

      // Check for cancellation after extraction
      await context.checkCancellation();

      // Log extraction output if available
      if (result && result.trim()) {
        await context.addLog(`Extraction output: ${result.trim()}`);
      }
    } catch (extractError) {
      const errorMessage =
        extractError instanceof Error
          ? extractError.message
          : String(extractError);

      // Log detailed error information
      await context.addLog(`Extraction failed for ${nmfile}: ${errorMessage}`);
      await context.addLog(`Failed command: ${extractCommand}`);

      throw new Error(`Failed to extract ${nmfile}: ${errorMessage}`);
    }
  }

  /**
   * Process XML file with table mapping and store data in appropriate database table
   */
  private async processXmlFileWithTableMapping(
    context: JobExecutionContext,
    xmlFilePath: string,
    xmlFileName: string
  ): Promise<number> {
    // Check for cancellation before processing XML file
    await context.checkCancellation();

    // Find the appropriate table mapping for this XML file
    const mapping = this.xmlTableMappings.find((m) =>
      m.filePattern.test(xmlFileName)
    );

    if (!mapping) {
      await context.addLog(
        `Warning: No table mapping found for XML file: ${xmlFileName}, skipping...`
      );
      return 0;
    }

    await context.addLog(
      `Processing ${xmlFileName} -> ${mapping.tableName} (${mapping.description})`
    );

    // Parse XML file
    const xmlData = await fs.promises.readFile(xmlFilePath, "utf8");
    const parser = new xml2js.Parser({ explicitArray: false });
    const parsedResult = await parser.parseStringPromise(xmlData);

    // Extract data using the configured path
    const pathParts = mapping.xmlDataPath.split(".");
    let items = parsedResult;
    for (const part of pathParts) {
      items = items?.[part];
    }

    if (!items) {
      await context.addLog(
        `Warning: No data found at path '${mapping.xmlDataPath}' in XML file: ${xmlFileName}`
      );
      return 0;
    }

    // Ensure items is an array
    if (!Array.isArray(items)) {
      items = [items];
    }

    if (items.length === 0) {
      await context.addLog(
        `Warning: Empty data array found in XML file: ${xmlFileName}`
      );
      return 0;
    }

    await context.checkCancellation();

    // Connect to target database for storing processed data
    const mysql = await import("mysql2/promise");
    let targetConnection: Connection | null = null;

    try {
      // Use the destination database configuration
      const destConfig = context.jobDefinition.destination?.database;
      if (!destConfig) {
        throw new Error("Destination database configuration is missing");
      }

      targetConnection = await mysql.createConnection({
        host: destConfig.host,
        port: destConfig.port,
        database: destConfig.database,
        user: destConfig.username,
        password: destConfig.password,
      });

      await context.addLog(
        `Connected to target database: ${destConfig.database}.${mapping.tableName}`
      );

      // Process and store data in the appropriate table
      let processedCount = 0;

      for (const item of items) {
        await context.checkCancellation();

        // Use dynamic insertion based on the item's properties
        const insertResult = await this.insertItemDynamically(
          targetConnection,
          mapping.tableName,
          item
        );

        if (insertResult) {
          processedCount++;
        }
      }

      await context.addLog(
        `Successfully processed ${processedCount} records from ${xmlFileName} into ${mapping.tableName}`
      );
      return processedCount;
    } finally {
      // Close target database connection
      if (targetConnection) {
        await targetConnection.end();
      }
    }
  }

  /**
   * Dynamically insert item into database table based on item properties
   * Uses INSERT IGNORE to prevent duplicate record errors
   */
  private async insertItemDynamically(
    connection: Connection,
    tableName: string,
    item: Record<string, unknown>
  ): Promise<boolean> {
    try {
      // Get all property names from the item (no metadata fields for now)
      const itemKeys = Object.keys(item);

      // Use only the XML data properties (no metadata columns)
      const allKeys = itemKeys;
      const allValues = itemKeys.map((key) => item[key]);

      // Create dynamic SQL with INSERT IGNORE to prevent duplicate record errors
      const placeholders = allKeys.map(() => "?").join(", ");
      const columnNames = allKeys.join(", ");

      const insertSql = `
        INSERT IGNORE INTO ${tableName} (${columnNames})
        VALUES (${placeholders})
      `;

      await connection.execute(insertSql, allValues);
      return true;
    } catch (insertError) {
      // Log the error but don't throw - let the caller decide how to handle
      const errorMessage =
        insertError instanceof Error
          ? insertError.message
          : String(insertError);
      console.error(`Failed to insert item into ${tableName}: ${errorMessage}`);
      return false;
    }
  }

  /**
   * Execute shell commands with enhanced error handling and cancellation support
   */
  private execPromise(command: string, jobId: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const childProcess = exec(
        command,
        { timeout: 120000 },
        (err, stdout, stderr) => {
          // Remove from global tracking map when done
          unregisterRunningProcess(jobId);

          if (err) {
            // Create detailed error information
            const errorInfo = {
              command: command,
              exitCode: err.code,
              signal: err.signal,
              stderr: stderr,
              stdout: stdout,
              error: err.message,
            };

            const detailedError = new Error(
              `Command failed: ${command}\nExit code: ${err.code}\nSignal: ${err.signal}\nStderr: ${stderr}\nStdout: ${stdout}`
            );

            // Attach additional info to error for debugging
            Object.assign(detailedError, { details: errorInfo });

            reject(detailedError);
          } else {
            resolve(stdout);
          }
        }
      );

      // Register the process with global tracking for cancellation
      registerRunningProcess(jobId, childProcess);
    });
  }

  /**
   * Clean up any running processes for the given job ID
   */
  private cleanupRunningProcesses(jobId: string): void {
    try {
      unregisterRunningProcess(jobId);
    } catch (error) {
      // Log error but don't throw - this is cleanup code
      logger.warn(`Failed to cleanup running processes for job ${jobId}`, {
        jobId,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Validate ADK processing job configuration
   */
  public validateConfig(jobDef: JobDefinition): boolean {
    if (!super.validateConfig(jobDef)) {
      return false;
    }

    const config = jobDef.dataSource.adk_processing;
    if (!config) {
      return false;
    }

    try {
      this.validateAdkProcessingConfig(config);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get required permissions for ADK processing operations
   */
  public getRequiredPermissions(): string[] {
    return [
      "filesystem:read",
      "filesystem:write",
      "process:execute",
      "database:read",
      "database:write",
    ];
  }

  /**
   * Validate ADK processing configuration fields
   */
  private validateAdkProcessingConfig(
    config: NonNullable<JobDefinition["dataSource"]["adk_processing"]>
  ): void {
    this.validateRequiredFields(
      config,
      ["sourceDirectory", "extractionPath", "rarToolPath", "fileListDatabase"],
      "ADK processing configuration"
    );

    // Validate file list database configuration
    this.validateRequiredFields(
      config.fileListDatabase,
      ["host", "port", "database", "username", "password", "table"],
      "File list database configuration"
    );

    // Additional validations
    if (
      typeof config.fileListDatabase.port !== "number" ||
      config.fileListDatabase.port <= 0 ||
      config.fileListDatabase.port > 65535
    ) {
      throw new Error(
        "File list database port must be a valid number between 1 and 65535"
      );
    }

    if (!config.sourceDirectory.trim()) {
      throw new Error("Source directory cannot be empty");
    }

    if (!config.extractionPath.trim()) {
      throw new Error("Extraction path cannot be empty");
    }

    if (!config.rarToolPath.trim()) {
      throw new Error("RAR tool path cannot be empty");
    }

    // Check if source directory exists
    if (!fs.existsSync(config.sourceDirectory)) {
      throw new Error(
        `Source directory does not exist: ${config.sourceDirectory}`
      );
    }

    // Check if RAR tool exists
    if (!fs.existsSync(config.rarToolPath)) {
      throw new Error(`RAR tool not found at: ${config.rarToolPath}`);
    }
  }
}
