// Test the fixed ADK processing job
import { runDataPullingJob } from './src/lib/jobRunner';
import { initializeDatabase } from './src/lib/database';

async function testFixedAdkJob() {
  try {
    console.log('🧪 Testing Fixed ADK Processing Job...\n');
    
    console.log('Initializing database...');
    await initializeDatabase();
    
    const jobId = '6'; // ADK Processing job
    console.log(`🚀 Running job: ${jobId} (ADK Processing)`);
    console.log('⏱️  This may take a while as it processes archive files...\n');
    
    const success = await runDataPullingJob(jobId, 'manual');
    
    if (success) {
      console.log('\n✅ Job executed successfully!');
      console.log('🎉 The file tracking configuration fix worked!');
    } else {
      console.log('\n❌ Job execution failed');
      console.log('💡 Check the logs above for specific error details');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Error testing job:', error);
    console.log('\n💡 This might be expected if there are other configuration issues');
    console.log('   The important thing is that we should no longer see the');
    console.log('   "File tracking configuration is required" error');
    process.exit(1);
  }
}

testFixedAdkJob();
