const SftpClient = require("ssh2-sftp-client");

async function testNestedStructure() {
  const sftp = new SftpClient();

  const config = {
    host: "aksesdata-anggaran.kemenkeu.go.id",
    port: 54321,
    username: "<PERSON>_DJPBN",
    password: "Sinergi100Persen",
  };

  try {
    console.log("🔗 Connecting to SFTP server...");
    await sftp.connect(config);
    console.log("✅ Connected successfully!");

    const rootPath = "adk_rkakl2025";
    console.log(`\n📁 Browsing root directory: ${rootPath}`);

    // List root directory
    const rootList = await sftp.list(rootPath);
    console.log(`Found ${rootList.length} items in root directory`);

    // Filter for 2-digit directories
    const twoDigitFolders = rootList.filter((item) => {
      return item.type === "d" && /^[0-9]{2}$/.test(item.name);
    });

    console.log(
      `\n🎯 Found ${twoDigitFolders.length} 2-digit organizational folders:`
    );
    twoDigitFolders.forEach((folder) => {
      console.log(`  - ${folder.name}/`);
    });

    // Test the first few 2-digit folders
    const foldersToTest = twoDigitFolders.slice(0, 3);

    for (const folder of foldersToTest) {
      console.log(`\n📂 Exploring 2-digit folder: ${folder.name}`);

      try {
        const twoDigitPath = `${rootPath}/${folder.name}`;
        const subfolderList = await sftp.list(twoDigitPath);

        // Filter for 3-digit directories
        const threeDigitFolders = subfolderList.filter((item) => {
          return item.type === "d" && /^[0-9]{3}$/.test(item.name);
        });

        console.log(`  Found ${threeDigitFolders.length} 3-digit subfolders:`);
        threeDigitFolders.slice(0, 5).forEach((subfolder) => {
          console.log(`    - ${subfolder.name}/`);
        });

        if (threeDigitFolders.length > 5) {
          console.log(`    ... and ${threeDigitFolders.length - 5} more`);
        }

        // Test the first 3-digit subfolder for files
        if (threeDigitFolders.length > 0) {
          const firstSubfolder = threeDigitFolders[0];
          const subfolderPath = `${twoDigitPath}/${firstSubfolder.name}`;

          console.log(
            `\n📄 Checking files in ${folder.name}/${firstSubfolder.name}:`
          );

          try {
            const fileList = await sftp.list(subfolderPath);
            const files = fileList.filter((item) => item.type === "-");
            const subdirs = fileList.filter((item) => item.type === "d");

            console.log(
              `    Found ${files.length} files and ${subdirs.length} subdirectories`
            );

            if (files.length > 0) {
              files.slice(0, 5).forEach((file) => {
                console.log(`      📄 ${file.name} (${file.size} bytes)`);
              });
              if (files.length > 5) {
                console.log(`      ... and ${files.length - 5} more files`);
              }
            }

            if (subdirs.length > 0) {
              console.log(`    Subdirectories:`);
              subdirs.slice(0, 5).forEach((subdir) => {
                console.log(`      📁 ${subdir.name}/`);
              });

              // Check if subdirectories are year folders
              const yearFolders = subdirs.filter((subdir) =>
                /^20[0-9]{2}$/.test(subdir.name)
              );
              if (yearFolders.length > 0) {
                console.log(
                  `    Year folders found: ${yearFolders
                    .map((f) => f.name)
                    .join(", ")}`
                );

                // Check the first year folder for files
                const yearPath = `${subfolderPath}/${yearFolders[0].name}`;
                try {
                  const yearList = await sftp.list(yearPath);
                  const yearFiles = yearList.filter(
                    (item) => item.type === "-"
                  );
                  console.log(
                    `      └─ ${yearFolders[0].name}/ contains ${yearFiles.length} files`
                  );

                  if (yearFiles.length > 0) {
                    yearFiles.slice(0, 3).forEach((file) => {
                      console.log(
                        `         📄 ${file.name} (${file.size} bytes)`
                      );
                    });
                  }
                } catch (yearError) {
                  console.log(
                    `      └─ Could not access ${yearFolders[0].name}/: ${yearError.message}`
                  );
                }
              } else {
                // Check the first subdirectory for files (non-year folder)
                const deeperPath = `${subfolderPath}/${subdirs[0].name}`;
                try {
                  const deeperList = await sftp.list(deeperPath);
                  const deeperFiles = deeperList.filter(
                    (item) => item.type === "-"
                  );
                  console.log(
                    `      └─ ${subdirs[0].name}/ contains ${deeperFiles.length} files`
                  );

                  if (deeperFiles.length > 0) {
                    deeperFiles.slice(0, 3).forEach((file) => {
                      console.log(
                        `         📄 ${file.name} (${file.size} bytes)`
                      );
                    });
                  }
                } catch (deeperError) {
                  console.log(
                    `      └─ Could not access ${subdirs[0].name}/: ${deeperError.message}`
                  );
                }
              }
            }
          } catch (fileError) {
            console.log(`    ❌ Could not list files: ${fileError.message}`);
          }
        }
      } catch (folderError) {
        console.log(
          `  ❌ Could not access folder ${folder.name}: ${folderError.message}`
        );
      }
    }
  } catch (error) {
    console.error("❌ SFTP Error:", error.message);
  } finally {
    try {
      await sftp.end();
      console.log("\n🔌 SFTP connection closed");
    } catch (closeError) {
      console.error("Error closing connection:", closeError.message);
    }
  }
}

// Run the test
testNestedStructure().catch(console.error);
