"use client";

// ============================================================================
// IMPORTS
// ============================================================================

// React imports
import React, { useState, useEffect, useRef } from "react";

// UI library imports
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
  Input,
  Textarea,
  Switch,
  Tabs,
  Tab,
} from "@heroui/react";

// Icon imports
import {
  Save,
  Database,
  Plus,
  Trash2,
  Copy,
  RefreshCw,
  Maximize,
  Minimize,
  Server,
} from "lucide-react";

// Type imports
import { JobDefinition, JobSequence } from "@/lib/jobManager";
import { JobAdminModalEnhancedProps } from "./utils/types";

// Custom hook imports
import { useModalState } from "./hooks/useModalState";
import { useJobManagement } from "./hooks/useJobManagement";
import { useSequenceManagement } from "./hooks/useSequenceManagement";
import { useJobForm } from "./hooks/useJobForm";

// Local component imports
import { JobsList } from "./components/JobsList";
import { SequencesList } from "./components/SequencesList";
import { SequenceCard } from "./components/SequenceCard";
import { DataSourceForm } from "./components/forms/DataSourceForm";
import { DestinationForm } from "./components/forms/DestinationForm";
import { RetryConfigForm } from "./components/forms/RetryConfigForm";
import { SequenceForm } from "./components/forms/SequenceForm";
import { JobSelectionModal } from "./components/JobSelectionModal";

// Utility imports
import { getJobStats } from "./utils/jobUtils";

// External component imports
import CronExpressionBuilder from "../CronExpressionBuilder";
import { DeleteJobConfirmModal } from "../DeleteJobConfirmModal";
import { DeleteSequenceConfirmModal } from "../DeleteSequenceConfirmModal";
import { SequenceExecutionConfirmModal } from "../SequenceExecutionConfirmModal";

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export default function JobAdminModalEnhanced({
  isOpen,
  onClose,
}: JobAdminModalEnhancedProps) {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  // Modal state management
  const modalState = useModalState(isOpen);

  // Job management
  const jobManagement = useJobManagement();

  // Sequence management
  const sequenceManagement = useSequenceManagement();

  // Form handling
  const jobForm = useJobForm(
    jobManagement.editedJob,
    jobManagement.setEditedJob
  );

  // Local UI state
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [isSequenceDeleteConfirmOpen, setIsSequenceDeleteConfirmOpen] =
    useState(false);
  const [, setShowVisualBuilder] = useState(false);
  const [, setShowSchemaBrowser] = useState(false);

  // Job selection state for sequences
  const [isJobSelectionModalOpen, setIsJobSelectionModalOpen] = useState(false);
  const [selectedJobsForSequence, setSelectedJobsForSequence] = useState<
    Set<string>
  >(new Set());

  // Session tracking
  const hasFetchedRef = useRef(false);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  // Fetch data when modal opens
  useEffect(() => {
    if (isOpen && !hasFetchedRef.current) {
      hasFetchedRef.current = true;
      jobManagement.fetchJobs();
      jobManagement.clearSelection();
      modalState.resetModalState();
    } else if (!isOpen) {
      hasFetchedRef.current = false;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  // ============================================================================
  // JOB MANAGEMENT HANDLERS
  // ============================================================================

  const handleCreateNewJob = () => {
    jobManagement.handleCreateNew();
    modalState.switchToCreateJob();
  };

  const handleJobSelect = (job: JobDefinition) => {
    jobManagement.handleJobSelect(job);
    modalState.switchToEditJob();
  };

  const handleSave = async () => {
    const isCreate = modalState.viewMode === "create";
    const success = await jobManagement.handleSave(isCreate);
    if (success) {
      modalState.switchToList();
    }
  };

  const handleCancel = () => {
    modalState.switchToList();
    jobManagement.clearSelection();
  };

  const handleDelete = () => {
    setIsDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = async () => {
    const success = await jobManagement.handleDelete();
    if (success) {
      modalState.switchToList();
    }
    setIsDeleteConfirmOpen(false);
  };

  const handleDuplicate = () => {
    jobManagement.handleDuplicate();
    modalState.switchToCreateJob();
  };

  // ============================================================================
  // SEQUENCE MANAGEMENT HANDLERS
  // ============================================================================

  const handleSequencesView = () => {
    modalState.switchToSequences();
    sequenceManagement.fetchSequences();
  };

  const handleCreateNewSequence = () => {
    sequenceManagement.handleCreateNewSequence();
    modalState.switchToCreateSequence();
  };

  const handleSequenceSelect = (sequence: JobSequence) => {
    sequenceManagement.handleSequenceSelect(sequence);
    modalState.switchToEditSequence();
  };

  const handleSequenceSave = async () => {
    const isCreate = modalState.viewMode === "create-sequence";
    const success = await sequenceManagement.handleSequenceSave(
      isCreate,
      jobManagement.fetchJobs
    );
    if (success) {
      modalState.switchToSequences();
    }
  };

  const handleSequenceCancel = () => {
    modalState.switchToSequences();
    sequenceManagement.clearSequenceSelection();
  };

  const handleSequenceDelete = () => {
    setIsSequenceDeleteConfirmOpen(true);
  };

  const handleSequenceDeleteConfirm = async () => {
    const success = await sequenceManagement.handleSequenceDelete();
    if (success) {
      modalState.switchToSequences();
    }
    setIsSequenceDeleteConfirmOpen(false);
  };

  const handleSequenceExecution = (sequenceId: string) => {
    sequenceManagement.handleSequenceExecution(sequenceId, jobManagement.jobs);
  };

  // ============================================================================
  // JOB SELECTION HANDLERS (for sequences)
  // ============================================================================

  const handleAddJobsToSequence = () => {
    setSelectedJobsForSequence(new Set());
    setIsJobSelectionModalOpen(true);
  };

  const handleJobSelectionChange = (jobId: string, isSelected: boolean) => {
    const newSelection = new Set(selectedJobsForSequence);
    if (isSelected) {
      newSelection.add(jobId);
    } else {
      newSelection.delete(jobId);
    }
    setSelectedJobsForSequence(newSelection);
  };

  const handleConfirmJobSelection = () => {
    if (
      !sequenceManagement.editedSequence ||
      selectedJobsForSequence.size === 0
    )
      return;

    const selectedJobIds = Array.from(selectedJobsForSequence);
    const updatedJobs = [
      ...sequenceManagement.editedSequence.jobs,
      ...selectedJobIds,
    ];

    sequenceManagement.setEditedSequence({
      ...sequenceManagement.editedSequence,
      jobs: updatedJobs,
    });

    sequenceManagement.setError(null);
    setIsJobSelectionModalOpen(false);
    setSelectedJobsForSequence(new Set());
  };

  const handleCancelJobSelection = () => {
    setIsJobSelectionModalOpen(false);
    setSelectedJobsForSequence(new Set());
  };

  // ============================================================================
  // HELPER FUNCTIONS
  // ============================================================================

  // Update job field helper
  const updateJobField = (
    field: keyof JobDefinition,
    value: string | boolean | number
  ) => {
    if (!jobManagement.editedJob) return;
    jobManagement.setEditedJob({
      ...jobManagement.editedJob,
      [field]: value,
    });
  };

  // ============================================================================
  // RENDER FUNCTIONS
  // ============================================================================

  const renderJobForm = () => {
    if (!jobManagement.editedJob) return null;

    return (
      <div className="w-full animate-fade-in modal-form-container">
        <div className="space-compact">
          {modalState.activeTab === "basic" && (
            <div
              className="form-section"
              role="tabpanel"
              aria-labelledby="basic-tab"
            >
              <div className="form-group">
                {/* Compact two-column layout for ID and Name */}
                <fieldset className="form-row" aria-label="Job identification">
                  <Input
                    label="Job ID"
                    value={jobManagement.editedJob.id}
                    onChange={(e) => updateJobField("id", e.target.value)}
                    isDisabled={modalState.viewMode === "edit"}
                    description={
                      modalState.viewMode === "edit"
                        ? "Cannot be changed"
                        : "Unique identifier"
                    }
                    isRequired
                    classNames={{
                      input: "text-sm",
                      label: "text-sm font-medium text-gray-700",
                      description: "text-xs text-gray-500",
                    }}
                  />
                  <Input
                    label="Job Name"
                    value={jobManagement.editedJob.name}
                    onChange={(e) => updateJobField("name", e.target.value)}
                    placeholder="Enter descriptive name"
                    isRequired
                    classNames={{
                      input: "text-sm",
                      label: "text-sm font-medium text-gray-700",
                    }}
                  />
                </fieldset>

                <Textarea
                  label="Description"
                  value={jobManagement.editedJob.description}
                  onChange={(e) =>
                    updateJobField("description", e.target.value)
                  }
                  placeholder="Describe what this job does"
                  minRows={2}
                  classNames={{
                    input: "text-sm",
                    label: "text-sm font-medium text-gray-700",
                  }}
                />

                {/* Compact enable switch */}
                <fieldset
                  className="flex items-center justify-between p-4 bg-gray-50/50 rounded-lg border border-gray-200"
                  aria-label="Job status configuration"
                >
                  <legend className="sr-only">Job Status Settings</legend>
                  <div>
                    <span className="text-sm font-medium text-gray-900">
                      Job Status
                    </span>
                    <p
                      className="text-xs text-gray-600 mt-1"
                      id="job-status-description"
                    >
                      {jobManagement.editedJob.enabled
                        ? "Job will run according to schedule"
                        : "Job is disabled and will not run"}
                    </p>
                  </div>
                  <Switch
                    isSelected={jobManagement.editedJob.enabled}
                    onValueChange={(enabled) =>
                      updateJobField("enabled", enabled)
                    }
                    color={
                      jobManagement.editedJob.enabled ? "success" : "default"
                    }
                    aria-label="Enable or disable job"
                    aria-describedby="job-status-description"
                    classNames={{
                      wrapper: "group-data-[selected=true]:bg-green-500",
                    }}
                  />
                </fieldset>
              </div>
            </div>
          )}

          {modalState.activeTab === "schedule" && (
            <CronExpressionBuilder
              value={jobManagement.editedJob.schedule}
              onChange={(cronExpression) =>
                updateJobField("schedule", cronExpression)
              }
            />
          )}

          {modalState.activeTab === "datasource" && (
            <DataSourceForm
              editedJob={jobManagement.editedJob}
              updateNestedField={jobForm.updateNestedField}
              setShowVisualBuilder={setShowVisualBuilder}
              setShowSchemaBrowser={setShowSchemaBrowser}
            />
          )}

          {modalState.activeTab === "destination" && (
            <DestinationForm
              editedJob={jobManagement.editedJob}
              updateNestedField={jobForm.updateNestedField}
            />
          )}

          {modalState.activeTab === "retry" && (
            <RetryConfigForm
              editedJob={jobManagement.editedJob}
              updateNestedField={jobForm.updateNestedField}
            />
          )}
        </div>
      </div>
    );
  };

  // ============================================================================
  // MAIN RENDER
  // ============================================================================

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size={modalState.isFullscreen ? "full" : "5xl"}
        scrollBehavior="inside"
        backdrop="blur"
        isDismissable={modalState.viewMode === "list"}
        classNames={{
          base: modalState.isFullscreen
            ? "h-screen w-screen animate-scale-in"
            : "h-[95vh] sm:h-[80vh] w-full sm:w-[64rem] max-w-[95vw] sm:max-w-[90vw] animate-scale-in",
          wrapper: "items-center justify-center flex p-2 sm:p-0",
          body: modalState.isFullscreen
            ? "h-[calc(100vh-160px)] overflow-hidden px-2 sm:px-3 py-2 sm:py-4"
            : "h-[calc(95vh-160px)] sm:h-[calc(80vh-160px)] overflow-hidden px-2 sm:px-3 py-2 sm:py-4",
          header:
            "px-2 sm:px-3 py-3 sm:py-4 border-b border-gray-200/50 flex-shrink-0",
          footer:
            "px-2 sm:px-3 py-3 sm:py-4 border-t border-gray-200/50 bg-gray-50/30 flex-shrink-0",
        }}
        hideCloseButton
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <ModalContent
          className={modalState.isFullscreen ? "" : "h-full flex flex-col"}
        >
          {/* Modal Header */}
          <ModalHeader className="flex flex-col gap-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-blue-100 text-blue-600">
                  <Database className="w-5 h-5" />
                </div>
                <div>
                  <h2
                    id="modal-title"
                    className="text-lg font-semibold text-gray-900"
                  >
                    {modalState.viewMode === "list"
                      ? "Job Administration"
                      : modalState.viewMode === "sequences"
                      ? "Sequence Management"
                      : modalState.viewMode === "create"
                      ? "Create New Job"
                      : modalState.viewMode === "create-sequence"
                      ? "Create New Sequence"
                      : modalState.viewMode === "edit-sequence"
                      ? "Edit Sequence"
                      : "Edit Job"}
                  </h2>
                  <p id="modal-description" className="text-sm text-gray-500">
                    {modalState.viewMode === "list"
                      ? "Manage and configure data extraction jobs"
                      : modalState.viewMode === "sequences"
                      ? "Create and manage job sequences"
                      : modalState.viewMode === "create"
                      ? "Configure a new data extraction job"
                      : modalState.viewMode === "create-sequence"
                      ? "Create a new job sequence"
                      : modalState.viewMode === "edit-sequence"
                      ? "Modify sequence configuration"
                      : "Modify job configuration"}
                  </p>
                </div>
              </div>

              {/* Header Action Buttons */}
              <div className="flex items-center gap-1 sm:gap-2">
                {modalState.viewMode === "list" && (
                  <>
                    <Button
                      color="primary"
                      size="sm"
                      startContent={<Plus className="w-3 h-3 sm:w-4 sm:h-4" />}
                      onPress={handleCreateNewJob}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-sm text-xs sm:text-sm px-2 sm:px-3"
                      title="Create a new data extraction job"
                    >
                      <span className="hidden sm:inline">Create Job</span>
                      <span className="sm:hidden">Create</span>
                    </Button>
                    <Button
                      variant="flat"
                      size="sm"
                      startContent={
                        <RefreshCw className="w-3 h-3 sm:w-4 sm:h-4" />
                      }
                      onPress={jobManagement.fetchJobs}
                      isLoading={jobManagement.isLoading}
                      className="border border-gray-300 hover:bg-gray-50 text-gray-700 text-xs sm:text-sm px-2 sm:px-3"
                      title="Refresh the jobs list"
                    >
                      <span className="hidden sm:inline">Refresh</span>
                    </Button>
                    <Button
                      variant="flat"
                      size="sm"
                      startContent={
                        <Server className="w-3 h-3 sm:w-4 sm:h-4" />
                      }
                      onPress={handleSequencesView}
                      className="border border-gray-300 hover:bg-gray-50 text-gray-700 text-xs sm:text-sm px-2 sm:px-3"
                      title="Manage job sequences"
                    >
                      <span className="hidden sm:inline">Sequences</span>
                      <span className="sm:hidden">Seq</span>
                    </Button>
                  </>
                )}
                {modalState.viewMode === "sequences" && (
                  <>
                    <Button
                      color="primary"
                      size="sm"
                      startContent={<Plus className="w-4 h-4" />}
                      onPress={handleCreateNewSequence}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-sm"
                    >
                      Create Sequence
                    </Button>
                    <Button
                      variant="flat"
                      size="sm"
                      onPress={() => modalState.switchToList()}
                      className="border border-gray-300 hover:bg-gray-50 text-gray-700"
                    >
                      Back to Jobs
                    </Button>
                    <Button
                      variant="flat"
                      size="sm"
                      startContent={<RefreshCw className="w-4 h-4" />}
                      onPress={sequenceManagement.fetchSequences}
                      isLoading={sequenceManagement.isLoading}
                      className="border border-gray-300 hover:bg-gray-50 text-gray-700"
                    >
                      Refresh
                    </Button>
                  </>
                )}

                {/* Fullscreen Toggle */}
                <Button
                  variant="flat"
                  size="sm"
                  isIconOnly
                  startContent={
                    modalState.isFullscreen ? (
                      <Minimize className="w-3 h-3 sm:w-4 sm:h-4" />
                    ) : (
                      <Maximize className="w-3 h-3 sm:w-4 sm:h-4" />
                    )
                  }
                  onPress={() =>
                    modalState.setIsFullscreen(!modalState.isFullscreen)
                  }
                  title={
                    modalState.isFullscreen
                      ? "Exit Fullscreen"
                      : "Enter Fullscreen"
                  }
                  className="border border-gray-300 hover:bg-gray-50 text-gray-600 min-w-8 sm:min-w-10"
                />

                {/* Close Button */}
                <Button
                  variant="flat"
                  size="sm"
                  isIconOnly
                  onPress={onClose}
                  title="Close"
                  className="border border-gray-300 hover:bg-gray-50 text-gray-600 hover:text-gray-800 min-w-8 sm:min-w-10 text-sm sm:text-base"
                >
                  ×
                </Button>
              </div>
            </div>
          </ModalHeader>

          {/* Modal Body Content */}
          <ModalBody
            className={`modal-content-container ${
              modalState.isFullscreen ? "" : "flex-1 min-h-0 flex flex-col"
            }`}
          >
            {/* Navigation Tabs - Fixed at top */}
            {((modalState.viewMode !== "list" &&
              modalState.viewMode !== "sequences" &&
              jobManagement.editedJob) ||
              modalState.viewMode === "create-sequence" ||
              modalState.viewMode === "edit-sequence") && (
              <div className="flex-shrink-0 mb-4">
                <Tabs
                  selectedKey={modalState.activeTab}
                  onSelectionChange={(key) =>
                    modalState.setActiveTab(key as string)
                  }
                  className="w-full"
                  aria-label="Job configuration sections"
                  classNames={{
                    tabList: "w-full",
                    tab: "flex-1 max-w-none",
                  }}
                >
                  {modalState.viewMode === "create-sequence" ||
                  modalState.viewMode === "edit-sequence" ? (
                    <>
                      <Tab key="basic" title="Info" />
                      <Tab key="jobs" title="Jobs" />
                      <Tab key="schedule" title="Schedule" />
                    </>
                  ) : (
                    <>
                      <Tab key="basic" title="Info" />
                      <Tab key="schedule" title="Schedule" />
                      <Tab key="datasource" title="Source" />
                      <Tab key="destination" title="Target" />
                      <Tab key="retry" title="Retry" />
                    </>
                  )}
                </Tabs>
              </div>
            )}

            {/* Scrollable Content Area */}
            <div className="flex-1 min-h-0 overflow-y-auto space-y-6">
              {/* Error Display */}
              {(jobManagement.error || sequenceManagement.error) && (
                <div className="status-error animate-slide-up rounded-lg px-4 py-3 border border-red-200 bg-red-50">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-red-500 flex-shrink-0"></div>
                    <span className="font-medium">Error</span>
                  </div>
                  <p className="mt-1 text-sm">
                    {jobManagement.error || sequenceManagement.error}
                  </p>
                </div>
              )}

              {/* Jobs List View */}
              {modalState.viewMode === "list" && (
                <div className="space-y-3 animate-fade-in w-full">
                  {jobManagement.isLoading ? (
                    <div className="space-y-2">
                      {/* Loading skeleton */}
                      {[...Array(3)].map((_, i) => (
                        <div key={i} className="card-modern p-2 animate-pulse">
                          <div className="flex items-center gap-2">
                            <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
                            <div className="flex-1 space-y-1">
                              <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                              <div className="h-2 bg-gray-200 rounded w-3/4"></div>
                            </div>
                            <div className="w-12 h-4 bg-gray-200 rounded"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <>
                      <JobsList
                        jobs={jobManagement.jobs}
                        isLoading={jobManagement.isLoading}
                        independentJobsExpanded={
                          modalState.independentJobsExpanded
                        }
                        setIndependentJobsExpanded={
                          modalState.setIndependentJobsExpanded
                        }
                        onJobSelect={handleJobSelect}
                      />
                      <SequencesList
                        jobs={jobManagement.jobs}
                        sequences={sequenceManagement.sequences}
                        isLoading={sequenceManagement.isLoading}
                        sequenceJobsExpanded={modalState.sequenceJobsExpanded}
                        setSequenceJobsExpanded={
                          modalState.setSequenceJobsExpanded
                        }
                        onJobSelect={handleJobSelect}
                        onSequenceExecution={handleSequenceExecution}
                      />
                    </>
                  )}
                </div>
              )}

              {/* Sequences Management View */}
              {modalState.viewMode === "sequences" && (
                <div className="space-y-2 w-full">
                  {sequenceManagement.isLoading ? (
                    <div className="text-center py-6">Loading sequences...</div>
                  ) : sequenceManagement.sequences.length === 0 ? (
                    <div className="text-center py-6 text-gray-500">
                      No sequences found. Create your first sequence to get
                      started.
                    </div>
                  ) : (
                    <div className="grid gap-1">
                      {sequenceManagement.sequences.map((sequence, index) => (
                        <SequenceCard
                          key={sequence.id}
                          sequence={sequence}
                          index={index}
                          onSequenceSelect={handleSequenceSelect}
                        />
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Sequence Form View */}
              {(modalState.viewMode === "create-sequence" ||
                modalState.viewMode === "edit-sequence") &&
                sequenceManagement.editedSequence && (
                  <div className="modal-form-container">
                    <SequenceForm
                      editedSequence={sequenceManagement.editedSequence}
                      setEditedSequence={sequenceManagement.setEditedSequence}
                      viewMode={modalState.viewMode}
                      activeTab={modalState.activeTab}
                      jobs={jobManagement.jobs}
                      onAddJobsToSequence={handleAddJobsToSequence}
                    />
                  </div>
                )}

              {/* Job Form View */}
              {(modalState.viewMode === "create" ||
                modalState.viewMode === "edit") &&
                renderJobForm()}
            </div>
          </ModalBody>

          {/* Modal Footer */}
          <ModalFooter>
            {/* List View Footer */}
            {modalState.viewMode === "list" ? (
              <div className="flex items-center justify-between w-full">
                {/* Job Statistics */}
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                    <span className="text-sm font-medium text-gray-700">
                      Job Statistics
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm">
                    <div className="status-indicator bg-gray-100 text-gray-700">
                      Total: {getJobStats(jobManagement.jobs).total}
                    </div>
                    <div className="status-indicator status-success">
                      Active: {getJobStats(jobManagement.jobs).active}
                    </div>
                    <div className="status-indicator bg-gray-100 text-gray-600">
                      Disabled: {getJobStats(jobManagement.jobs).disabled}
                    </div>
                    <div className="status-indicator bg-blue-100 text-blue-700">
                      Oracle: {getJobStats(jobManagement.jobs).oracle}
                    </div>
                    <div className="status-indicator bg-orange-100 text-orange-700">
                      SFTP: {getJobStats(jobManagement.jobs).sftp}
                    </div>
                  </div>
                </div>
                <Button
                  color="primary"
                  onPress={onClose}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-sm"
                >
                  Close
                </Button>
              </div>
            ) : /* Sequences View Footer */
            modalState.viewMode === "sequences" ? (
              <div className="flex items-center justify-between w-full">
                {/* Sequence Statistics */}
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                    <span className="text-sm font-medium text-gray-700">
                      Sequence Statistics
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm">
                    <div className="status-indicator bg-gray-100 text-gray-700">
                      Total: {sequenceManagement.sequences.length}
                    </div>
                    <div className="status-indicator status-success">
                      Enabled:{" "}
                      {
                        sequenceManagement.sequences.filter((s) => s.enabled)
                          .length
                      }
                    </div>
                    <div className="status-indicator bg-gray-100 text-gray-600">
                      Disabled:{" "}
                      {
                        sequenceManagement.sequences.filter((s) => !s.enabled)
                          .length
                      }
                    </div>
                  </div>
                </div>
                <Button
                  color="primary"
                  onPress={onClose}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-sm"
                >
                  Close
                </Button>
              </div>
            ) : /* Sequence Form Footer */
            modalState.viewMode === "create-sequence" ||
              modalState.viewMode === "edit-sequence" ? (
              <div className="flex gap-2 w-full justify-between">
                <div className="flex gap-2">
                  {modalState.viewMode === "edit-sequence" &&
                    sequenceManagement.selectedSequence && (
                      <Button
                        color="danger"
                        variant="flat"
                        size="sm"
                        startContent={<Trash2 className="w-4 h-4" />}
                        onPress={handleSequenceDelete}
                        isLoading={sequenceManagement.isDeletingSequence}
                      >
                        Delete Sequence
                      </Button>
                    )}
                </div>
                <div className="flex gap-3">
                  <Button
                    variant="flat"
                    onPress={handleSequenceCancel}
                    className="border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium"
                  >
                    Cancel
                  </Button>
                  <Button
                    color="primary"
                    startContent={<Save className="w-4 h-4" />}
                    onPress={handleSequenceSave}
                    isLoading={sequenceManagement.isSaving}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-sm"
                  >
                    {modalState.viewMode === "create-sequence"
                      ? "Create Sequence"
                      : "Save Changes"}
                  </Button>
                </div>
              </div>
            ) : (
              /* Job Form Footer */
              <div className="flex gap-2 w-full justify-between">
                <div className="flex gap-2">
                  {modalState.viewMode === "edit" &&
                    jobManagement.selectedJob && (
                      <>
                        <Button
                          color="warning"
                          variant="flat"
                          size="sm"
                          startContent={<Copy className="w-4 h-4" />}
                          onPress={handleDuplicate}
                        >
                          Duplicate
                        </Button>
                        <Button
                          color="danger"
                          variant="flat"
                          size="sm"
                          startContent={<Trash2 className="w-4 h-4" />}
                          onPress={handleDelete}
                          isLoading={jobManagement.isDeleting}
                        >
                          Delete
                        </Button>
                      </>
                    )}
                </div>
                <div className="flex gap-3">
                  <Button
                    variant="flat"
                    onPress={handleCancel}
                    className="border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium"
                  >
                    Cancel
                  </Button>
                  <Button
                    color="primary"
                    startContent={<Save className="w-4 h-4" />}
                    onPress={handleSave}
                    isLoading={jobManagement.isSaving}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-sm"
                  >
                    {modalState.viewMode === "create"
                      ? "Create Job"
                      : "Save Changes"}
                  </Button>
                </div>
              </div>
            )}
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* ============================================================================ */}
      {/* ADDITIONAL MODALS */}
      {/* ============================================================================ */}

      {/* Job Selection Modal for Sequences */}
      <JobSelectionModal
        isOpen={isJobSelectionModalOpen}
        jobs={jobManagement.jobs}
        selectedJobsForSequence={selectedJobsForSequence}
        onClose={handleCancelJobSelection}
        onJobSelectionChange={handleJobSelectionChange}
        onConfirm={handleConfirmJobSelection}
      />

      {/* Job Delete Confirmation Modal */}
      <DeleteJobConfirmModal
        isOpen={isDeleteConfirmOpen}
        onClose={() => setIsDeleteConfirmOpen(false)}
        onConfirm={handleDeleteConfirm}
        job={jobManagement.selectedJob}
        isLoading={jobManagement.isDeleting}
      />

      {/* Sequence Delete Confirmation Modal */}
      <DeleteSequenceConfirmModal
        isOpen={isSequenceDeleteConfirmOpen}
        onClose={() => setIsSequenceDeleteConfirmOpen(false)}
        onConfirm={handleSequenceDeleteConfirm}
        sequence={sequenceManagement.selectedSequence}
        isLoading={sequenceManagement.isDeletingSequence}
      />

      {/* Sequence Execution Confirmation Modal */}
      <SequenceExecutionConfirmModal
        isOpen={sequenceManagement.isSequenceConfirmModalOpen}
        onClose={() => sequenceManagement.setIsSequenceConfirmModalOpen(false)}
        onConfirm={sequenceManagement.executeSequence}
        sequenceName={
          sequenceManagement.selectedSequenceForExecution?.name || ""
        }
        sequenceDescription={
          sequenceManagement.selectedSequenceForExecution?.description
        }
        jobCount={
          sequenceManagement.selectedSequenceForExecution?.jobCount || 0
        }
        isLoading={sequenceManagement.isLoading}
      />
    </>
  );
}
