"use client";

// ============================================================================
// IMPORTS
// ============================================================================

// React imports
import React, { useState } from "react";

// UI library imports
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
} from "@heroui/react";

// Icon imports
import { Trash2, AlertTriangle, Maximize, Minimize } from "lucide-react";

// Type imports
import { JobDefinition } from "@/lib/jobManager";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface DeleteJobConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  job: JobDefinition | null;
  isLoading?: boolean;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const DeleteJobConfirmModal = ({
  isOpen,
  onClose,
  onConfirm,
  job,
  isLoading = false,
}: DeleteJobConfirmModalProps) => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [isFullscreen, setIsFullscreen] = useState(false);

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  // ============================================================================
  // EARLY RETURN
  // ============================================================================

  if (!job) return null;

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size={isFullscreen ? "full" : "md"}
      backdrop="blur"
      isDismissable={!isLoading}
      classNames={{
        base: isFullscreen ? "max-h-screen" : "",
        body: isFullscreen
          ? "min-h-[calc(100vh-200px)] max-h-[calc(100vh-200px)] overflow-y-auto"
          : "",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Trash2 className="w-5 h-5 text-danger" />
              <span>Confirm Job Deletion</span>
            </div>
            <Button
              variant="flat"
              size="sm"
              isIconOnly
              startContent={
                isFullscreen ? (
                  <Minimize className="w-4 h-4" />
                ) : (
                  <Maximize className="w-4 h-4" />
                )
              }
              onPress={() => setIsFullscreen(!isFullscreen)}
              title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
            />
          </div>
        </ModalHeader>

        <ModalBody>
          <div className="space-y-4">
            <div className="flex items-start gap-3 p-3 bg-danger-50 border border-danger-200 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-danger-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-danger-800 font-medium text-sm">
                  Permanent Deletion
                </p>
                <p className="text-danger-700 text-sm mt-1">
                  This action cannot be undone. All job data, execution history,
                  and logs will be permanently deleted.
                </p>
              </div>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Job Name:
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {job.name}
              </p>
            </div>

            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Are you sure you want to delete this job? This will remove:
              </p>
              <ul className="text-sm text-gray-600 dark:text-gray-400 mt-2 ml-4 space-y-1">
                <li>• Job configuration and settings</li>
                <li>• All execution history</li>
                <li>• All associated logs</li>
                <li>• Schedule information</li>
              </ul>
            </div>
          </div>
        </ModalBody>

        <ModalFooter>
          <Button variant="flat" onPress={onClose} isDisabled={isLoading}>
            Cancel
          </Button>
          <Button
            color="danger"
            onPress={handleConfirm}
            startContent={<Trash2 className="w-4 h-4" />}
            isLoading={isLoading}
          >
            {isLoading ? "Deleting..." : "Delete Job"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
