const { executeOracleQuery } = require('../src/tarikData/db/Oracle');
const { saveToMySQL } = require('../src/lib/jobRunner');

// Mock data simulating Oracle DATE/TIMESTAMP extraction
const mockOracleData = {
  rows: [
    {
      ID: 1,
      TGL_DOKUMEN: new Date('2025-01-14T00:00:00.000Z'),
      NAMA_DOKUMEN: 'Test Document 1',
      CREATED_AT: new Date('2025-01-14T10:30:00.000Z')
    },
    {
      ID: 2,
      TGL_DOKUMEN: '2025-01-15 08:45:30', // String date from Oracle
      NAMA_DOKUMEN: 'Test Document 2',
      CREATED_AT: new Date('2025-01-15T08:45:30.000Z')
    },
    {
      ID: 3,
      TGL_DOKUMEN: 'Tue Jan 16 2025 00:00:00 GMT+0700 (Indochina Time)', // Problematic JS Date string
      NAMA_DOKUMEN: 'Test Document 3',
      CREATED_AT: new Date('2025-01-16T00:00:00.000Z')
    }
  ]
};

describe('Oracle to MySQL DateTime Conversion', () => {
  test('should handle Date objects correctly', () => {
    const testDate = new Date('2025-01-14T10:30:00.000Z');
    const mysqlFormat = testDate.toISOString().slice(0, 19).replace('T', ' ');
    
    expect(mysqlFormat).toBe('2025-01-14 10:30:00');
  });
  
  test('should convert JavaScript Date string to MySQL format', () => {
    const jsDateString = 'Tue Jan 14 2025 00:00:00 GMT+0700 (Indochina Time)';
    const dateObj = new Date(jsDateString);
    const mysqlFormat = dateObj.toISOString().slice(0, 19).replace('T', ' ');
    
    expect(mysqlFormat).toBe('2025-01-13 17:00:00'); // Adjusted for timezone
  });
  
  test('should handle Oracle date string format', () => {
    const oracleDateString = '2025-01-15 08:45:30';
    const dateObj = new Date(oracleDateString);
    const mysqlFormat = dateObj.toISOString().slice(0, 19).replace('T', ' ');
    
    expect(mysqlFormat).toBe('2025-01-15 08:45:30');
  });
  
  test('should detect date columns for MySQL schema creation', () => {
    const sampleData = {
      TGL_DOKUMEN: new Date(),
      CREATED_AT: new Date(),
      UPDATED_AT: new Date(),
      NAMA_DOKUMEN: 'Test',
      ID: 1
    };
    
    // Test date field detection
    Object.keys(sampleData).forEach(key => {
      const value = sampleData[key];
      const isDateField = value instanceof Date;
      
      if (['TGL_DOKUMEN', 'CREATED_AT', 'UPDATED_AT'].includes(key)) {
        expect(isDateField).toBe(true);
      }
    });
  });
  
  test('should handle mixed date formats in Oracle data processing', () => {
    // Simulate Oracle data processing
    const processedRows = mockOracleData.rows.map(row => {
      const processedRow = {};
      
      Object.keys(row).forEach(key => {
        const value = row[key];
        
        // Handle DATE and TIMESTAMP columns
        if (value instanceof Date) {
          processedRow[key] = value;
        }
        // Handle date strings that might come from Oracle
        else if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value)) {
          const dateObj = new Date(value);
          if (!isNaN(dateObj.getTime())) {
            processedRow[key] = dateObj;
          } else {
            processedRow[key] = value;
          }
        }
        // Handle JavaScript date strings
        else if (typeof value === 'string' && /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun)/.test(value)) {
          try {
            const dateObj = new Date(value);
            if (!isNaN(dateObj.getTime())) {
              processedRow[key] = dateObj;
            } else {
              processedRow[key] = value;
            }
          } catch (error) {
            processedRow[key] = value;
          }
        }
        else {
          processedRow[key] = value;
        }
      });
      
      return processedRow;
    });
    
    // Verify all TGL_DOKUMEN values are Date objects
    processedRows.forEach(row => {
      expect(row.TGL_DOKUMEN instanceof Date).toBe(true);
    });
  });
  
  test('should convert Date objects to MySQL DATETIME format for insertion', () => {
    const testData = [
      {
        ID: 1,
        TGL_DOKUMEN: new Date('2025-01-14T10:30:00.000Z'),
        NAMA_DOKUMEN: 'Test Document'
      }
    ];
    
    // Simulate the value conversion logic from jobRunner.ts
    const columns = ['ID', 'TGL_DOKUMEN', 'NAMA_DOKUMEN'];
    const row = testData[0];
    
    const values = columns.map((col) => {
      const value = row[col];
      
      // Handle Date objects properly for MySQL DATETIME columns
      if (value instanceof Date) {
        return value.toISOString().slice(0, 19).replace('T', ' ');
      }
      
      return value;
    });
    
    expect(values[0]).toBe(1); // ID
    expect(values[1]).toBe('2025-01-14 10:30:00'); // TGL_DOKUMEN converted to MySQL format
    expect(values[2]).toBe('Test Document'); // NAMA_DOKUMEN unchanged
  });
});

// Integration test simulation
describe('Oracle to MySQL DateTime Integration', () => {
  test('should handle complete Oracle to MySQL datetime conversion flow', () => {
    // Step 1: Oracle data extraction (simulated)
    const oracleResult = {
      rows: [
        {
          ID: 1,
          TGL_DOKUMEN: 'Tue Jan 14 2025 00:00:00 GMT+0700 (Indochina Time)',
          NAMA_DOKUMEN: 'Test Document'
        }
      ]
    };
    
    // Step 2: Oracle data processing
    const processedData = oracleResult.rows.map(row => {
      const processedRow = {};
      
      Object.keys(row).forEach(key => {
        const value = row[key];
        
        // Handle JavaScript date strings
        if (typeof value === 'string' && /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun)/.test(value)) {
          try {
            const dateObj = new Date(value);
            if (!isNaN(dateObj.getTime())) {
              processedRow[key] = dateObj;
            } else {
              processedRow[key] = value;
            }
          } catch (error) {
            processedRow[key] = value;
          }
        } else {
          processedRow[key] = value;
        }
      });
      
      return processedRow;
    });
    
    // Step 3: MySQL insertion value conversion
    const columns = ['ID', 'TGL_DOKUMEN', 'NAMA_DOKUMEN'];
    const insertValues = processedData.map(row => {
      return columns.map(col => {
        const value = row[col];
        
        // Handle Date objects properly for MySQL DATETIME columns
        if (value instanceof Date) {
          return value.toISOString().slice(0, 19).replace('T', ' ');
        }
        
        return value;
      });
    });
    
    // Verify the conversion worked
    expect(insertValues[0][1]).toMatch(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/);
    expect(typeof insertValues[0][1]).toBe('string');
  });
});